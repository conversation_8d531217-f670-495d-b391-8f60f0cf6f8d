"use client";

import { useState, useEffect, useCallback } from 'react';
import { AuthAPI } from '@/lib/api/auth';

interface User {
  username: string;
  email: string;
  avatar?: string;
}

export function useAuth() {
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [authChecked, setAuthChecked] = useState(false);

  // 登出函数
  const logout = useCallback(() => {
    console.log('🚪 用户登出，清除认证数据');
    if (typeof window !== 'undefined') {
      localStorage.removeItem('token');
      localStorage.removeItem('user');
      localStorage.removeItem('isLoggedIn');
    }
    setIsLoggedIn(false);
    setUser(null);
  }, []);

  // 认证检查函数
  const checkAuth = useCallback(async () => {
    if (authChecked || typeof window === 'undefined') return; // 避免重复检查和SSR问题

    setIsLoading(true);
    
    try {
      const token = localStorage.getItem('token');
      const userData = localStorage.getItem('user');
      
      console.log('🔍 认证检查 - Token存在:', !!token);
      
      if (token && userData) {
        try {
          console.log('🔐 验证token有效性...');
          const isValid = await AuthAPI.verifyToken();
          
          if (isValid) {
            console.log('✅ Token验证成功');
            setIsLoggedIn(true);
            setUser(JSON.parse(userData));
          } else {
            console.log('❌ Token验证失败，清除认证数据');
            logout();
          }
        } catch (error) {
          console.error('Token验证错误:', error);
          logout();
        }
      } else {
        console.log('ℹ️ 未找到token或用户数据');
        setIsLoggedIn(false);
        setUser(null);
      }
    } catch (error) {
      console.error('认证检查失败:', error);
      setIsLoggedIn(false);
      setUser(null);
    } finally {
      setIsLoading(false);
      setAuthChecked(true);
    }
  }, [authChecked, logout]);

  // 初始化时检查认证状态
  useEffect(() => {
    checkAuth();
  }, [checkAuth]);

  // 手动重新检查认证状态
  const refreshAuth = useCallback(() => {
    setAuthChecked(false);
    checkAuth();
  }, [checkAuth]);

  return {
    isLoggedIn,
    user,
    isLoading,
    logout,
    refreshAuth,
    authChecked
  };
}