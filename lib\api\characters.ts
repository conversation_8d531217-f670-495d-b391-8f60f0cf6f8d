// 角色API相关接口
import { CURRENT_API, API_PATHS, buildAuthHeaders } from './config';

// 角色资源数据接口
export interface CharacterResource {
  opening: string; // 开场白
  profile_image: string[]; // 档案图片数组
  image: Array<{
    description: string;
    url: string;
  }>; // 自拍图片数组
  voice_plays: Array<{
    description: string;
    url: string;
  }>; // 语音播放数组
  voice_call: string; // 语音通话文件
  image_urls: string[]; // 额外图片URL数组
}

// 角色创建者信息接口
export interface CharacterCreator {
  id: number;
  username: string;
  email: string;
}

// 后端角色数据接口
export interface BackendCharacter {
  id: number;
  name: string;
  age: number;
  occupation: string;
  tags: string[];
  description: string;
  chatCount: number;
  likeCount: number;
  favoriteCount: number;
  imageUrl: string;
  avatarUrl: string;
  resource: CharacterResource; // 现在是对象而不是JSON字符串
  gender: 'male' | 'female';
  creator: CharacterCreator; // 创建者信息对象
  isFollowed: boolean; // 是否已关注
  createdAt: string;
}

// 分页信息接口
export interface Pagination {
  totalCount: number;
  totalPages: number;
  currentPage: number;
  pageSize: number;
  hasNext: boolean;
  hasPrev: boolean;
}

// 角色列表API响应接口
export interface CharactersResponse {
  success: boolean;
  message: string;
  data: {
    characters: BackendCharacter[];
    pagination: Pagination;
  };
}

// 角色列表请求参数
export interface CharactersRequest {
  gender: 'male' | 'female';
  activeTag?: string;
  page?: number;
  pageSize?: number;
  searchTerm?: string; // 为后端搜索预留参数
}

// 最近聊天角色接口
export interface RecentChatCharacter {
  id: string;
  name: string;
  occupation: string;
  imageSrc: string;
  avatarSrc: string;
  gender: 'male' | 'female';
  lastChatAt: string;
  lastChatContent: string;
  unreadCount: number;
  pinned: boolean;
  private: boolean;
}

// 最近聊天API响应接口
export interface RecentChatResponse {
  success: boolean;
  data: {
    characters: RecentChatCharacter[];
  };
  message: string;
}

// 单个角色详情API响应接口
export interface CharacterDetailResponse {
  success: boolean;
  message: string;
  data: {
    character: BackendCharacter;
  };
}

// 转换后端角色数据为前端格式
export const transformBackendCharacter = (backendChar: any) => {
  // 处理creator字段，兼容不同的API响应格式
  const creator = backendChar.creator || {
    id: backendChar.creatorId || 1,
    username: `Creator ${backendChar.creatorId || 1}`,
    email: '',
  };

  // 安全地处理image_urls，避免空数组导致的错误
  const additionalImages = backendChar.resource?.image_urls || [];

  // 调试信息：记录图片资源
  // if (additionalImages.length > 0) {
  //   console.log(`🖼️ 角色 ${backendChar.name} 有 ${additionalImages.length} 张额外图片:`, additionalImages);
  //   console.log(`🎯 悬停时将显示最后一张图片:`, additionalImages[additionalImages.length - 1]);
  // }

  // 安全处理各种可能为空或undefined的字段
  return {
    id: backendChar.id.toString(),
    name: backendChar.name || 'Unknown',
    age: backendChar.age || 25,
    occupation: backendChar.occupation || 'Unknown',
    tags: Array.isArray(backendChar.tags) ? backendChar.tags : [],
    description: backendChar.description || 'No description available',
    chatCount: (backendChar.chatCount || 0).toString(),
    likeCount: (backendChar.likeCount || 0).toString(),
    followers: (backendChar.favoriteCount || 0).toString(),
    imageSrc:
      backendChar.imageUrl ||
      '/placeholder.svg?height=400&width=300&text=No+Image',
    altImageSrc:
      additionalImages.length > 0
        ? additionalImages[additionalImages.length - 1]
        : undefined, // 悬停时显示image_urls中的最后一张图片
    avatarSrc:
      backendChar.avatarUrl ||
      '/placeholder.svg?height=128&width=128&text=No+Avatar',
    images: [backendChar.imageUrl, ...additionalImages].filter(Boolean), // 过滤掉空值
    gender: backendChar.gender || 'male',
    creator: {
      id: creator.id.toString(),
      name: creator.username || `Creator ${creator.id}`,
      likeCount: (backendChar.likeCount || 0).toString(),
    },
    // 新增字段（安全处理可能不存在的字段）
    resource: backendChar.resource || null, // 保留完整的资源对象
    isFollowed: Boolean(backendChar.isFollowed), // 关注状态，确保为布尔值
    createdAt: backendChar.createdAt || new Date().toISOString(),
  };
};

// 获取角色列表
export const getCharacters = async (
  params: CharactersRequest
): Promise<CharactersResponse> => {
  try {
    const requestBody: any = {
      gender: params.gender,
      page: params.page || 1,
      pageSize: params.pageSize || 10,
    };

    // 只有当activeTag存在时才添加到请求体中
    if (params.activeTag) {
      requestBody.activeTag = params.activeTag;
    }

    // 为后端搜索预留参数
    if (params.searchTerm && params.searchTerm.trim()) {
      requestBody.searchTerm = params.searchTerm.trim();
    }

    console.log('🚀 调用角色列表API:', requestBody);

    const response = await fetch(
      `${CURRENT_API.MAIN}${API_PATHS.CHARACTERS.LIST}`,
      {
        method: 'POST',
        headers: buildAuthHeaders(),
        body: JSON.stringify(requestBody),
      }
    );

    if (!response.ok) {
      const errorText = await response.text();
      console.error('❌ 角色列表API调用失败:', response.status, errorText);
      throw new Error(`获取角色列表失败: ${response.status} ${errorText}`);
    }

    const data: CharactersResponse = await response.json();
    console.log('✅ 角色列表API调用成功:', data);

    return data;
  } catch (error) {
    console.error('❌ 角色列表API调用错误:', error);
    throw error;
  }
};

// 获取最近聊天列表
export const getRecentChats = async (
  limit: number = 3
): Promise<RecentChatResponse> => {
  try {
    console.log('🚀 调用最近聊天API:', { limit });

    const response = await fetch(
      `${CURRENT_API.MAIN}${API_PATHS.CHARACTERS.RECENT}?limit=${limit}`,
      {
        method: 'GET',
        headers: buildAuthHeaders(),
      }
    );

    if (!response.ok) {
      const errorText = await response.text();
      console.error('❌ 最近聊天API调用失败:', response.status, errorText);
      throw new Error(`获取最近聊天失败: ${response.status} ${errorText}`);
    }

    const data: RecentChatResponse = await response.json();
    console.log('✅ 最近聊天API调用成功:', data);

    return data;
  } catch (error) {
    console.error('❌ 最近聊天API调用错误:', error);
    throw error;
  }
};

// 获取单个角色详情
export const getCharacterDetail = async (
  characterId: string | number
): Promise<CharacterDetailResponse> => {
  try {
    // console.log('🚀 调用角色详情API:', { characterId });

    const response = await fetch(
      `${CURRENT_API.MAIN}${API_PATHS.CHARACTERS.DETAIL}/${characterId}`,
      {
        method: 'GET',
        headers: buildAuthHeaders(),
      }
    );

    if (!response.ok) {
      const errorText = await response.text();
      // console.error('❌ 角色详情API调用失败:', response.status, errorText);
      throw new Error(`获取角色详情失败: ${response.status} ${errorText}`);
    }

    const data: CharacterDetailResponse = await response.json();
    // console.log('✅ 角色详情API调用成功:', data);

    return data;
  } catch (error) {
    console.error('❌ 角色详情API调用错误:', error);
    throw error;
  }
};

// 转换单个角色详情数据为前端格式
export const transformCharacterDetail = (
  backendChar: CharacterDetailResponse['data']['character']
) => {
  // 安全地处理image_urls，避免空数组导致的错误
  const additionalImages = backendChar.resource?.image_urls || [];
  
  return {
    id: backendChar.id.toString(),
    name: backendChar.name,
    age: backendChar.age,
    occupation: backendChar.occupation,
    tags: backendChar.tags || [],
    description: backendChar.description,
    chatCount: backendChar.chatCount.toString(),
    likeCount: backendChar.likeCount.toString(),
    followers: backendChar.favoriteCount.toString(),
    favoriteCount: backendChar.favoriteCount.toString(), // 添加收藏数量字段
    imageSrc: backendChar.imageUrl,
    altImageSrc: additionalImages.length > 0 
      ? additionalImages[additionalImages.length - 1] 
      : undefined, // 悬停时显示image_urls中的最后一张图片
    avatarSrc: backendChar.avatarUrl,
    images: [backendChar.imageUrl, ...additionalImages], // 合并主图和额外图片
    gender: backendChar.gender,
    creator: {
      id: backendChar.creator.id.toString(),
      name: backendChar.creator.username,
      likeCount: backendChar.likeCount.toString(),
    },
    // 完整的资源数据和新增字段
    resource: backendChar.resource,
    isFollowed: backendChar.isFollowed,
    createdAt: backendChar.createdAt,
  };
};

// 通过角色名称查找角色
export const getCharacterByName = async (
  characterName: string
): Promise<any> => {
  try {
    console.log('🚀 通过名称查找角色:', { characterName });

    // 首先尝试从角色列表中查找
    const allCharacters = await getCharacters({ gender: 'male', page: 1, pageSize: 1000 });
    const femaleCharacters = await getCharacters({ gender: 'female', page: 1, pageSize: 1000 });
    
    const allChars = [...allCharacters.data.characters, ...femaleCharacters.data.characters];
    
    // 查找匹配的角色（不区分大小写）
    const character = allChars.find(char => 
      char.name.toLowerCase() === characterName.toLowerCase()
    );

    if (character) {
      console.log('✅ 通过名称找到角色:', character);
      return transformBackendCharacter(character);
    }

    // 如果没找到，返回null
    console.log('❌ 未找到角色:', characterName);
    return null;
  } catch (error) {
    console.error('❌ 通过名称查找角色失败:', error);
    throw error;
  }
};
