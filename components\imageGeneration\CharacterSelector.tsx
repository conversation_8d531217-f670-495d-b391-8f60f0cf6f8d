"use client";

import { useState, useEffect } from "react";
import Image from "next/image";
import { Check, Plus, Upload } from "lucide-react";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { getCharacterImageUrl } from "@/lib/image-utils";

interface Character {
  id: string;
  name: string;
  imageSrc: string;
  gender: "male" | "female";
}

interface RecentChat {
  id: string;
  name: string;
  imageSrc: string;
  gender: "male" | "female";
}

interface CharacterSelectorProps {
  selectedCharacter: string | null;
  selectedGender: "male" | "female";
  uploadedImage: string | null;
  onSelectCharacter: (characterId: string) => void;
  onSelectGender: (gender: "male" | "female") => void;
  onUploadImage: (imageUrl: string) => void;
  onOpenUpload: () => void;
}

export default function CharacterSelector({
  selectedCharacter,
  selectedGender,
  uploadedImage,
  onSelectCharacter,
  onSelectGender,
  onUploadImage,
  onOpenUpload,
}: CharacterSelectorProps) {
  const [recentCharacters, setRecentCharacters] = useState<RecentChat[]>([]);

  useEffect(() => {
    // 从localStorage获取最近的聊天记录
    const chatHistory = JSON.parse(localStorage.getItem('recentChats') || '[]');
    setRecentCharacters(chatHistory.slice(0, 3)); // 只取前3个最近的聊天
  }, []);

  return (
    <div className="space-y-6">
      <h2 className="text-xl font-bold">Choose Character</h2>
      
      {/* 最近聊天角色 */}
      {recentCharacters.length > 0 && (
        <div className="space-y-3">
          <h3 className="text-lg font-semibold">Recent Chats</h3>
          <div className="flex space-x-4">
            {recentCharacters.map((character) => (
              <div
                key={character.id}
                className={cn(
                  "relative cursor-pointer rounded-lg border-2 p-2 transition-all hover:scale-105",
                  selectedCharacter === character.id
                    ? "border-pink-500 bg-pink-500/10"
                    : "border-[#3a1a44] bg-[#1a0a24] hover:border-pink-400"
                )}
                onClick={() => onSelectCharacter(character.id)}
              >
                {selectedCharacter === character.id && (
                  <div className="absolute -top-2 -right-2 z-10 h-6 w-6 rounded-full bg-pink-500 flex items-center justify-center">
                    <Check className="h-4 w-4 text-white" />
                  </div>
                )}
                <div className="h-20 w-20 overflow-hidden rounded-lg">
                  <Image
                    src={getCharacterImageUrl(character.imageSrc)}
                    alt={character.name}
                    width={80}
                    height={80}
                    className="h-full w-full object-cover"
                  />
                </div>
                <p className="mt-2 text-center text-sm font-medium">
                  {character.name}
                </p>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* 性别选择 */}
      <div className="space-y-3">
        <h3 className="text-lg font-semibold">Create Character</h3>
        <div className="flex space-x-4">
          {["male", "female"].map((gender) => (
            <div
              key={gender}
              className={cn(
                "relative cursor-pointer rounded-lg border-2 p-4 transition-all hover:scale-105",
                selectedGender === gender && !selectedCharacter && !uploadedImage
                  ? "border-pink-500 bg-pink-500/10"
                  : "border-[#3a1a44] bg-[#1a0a24] hover:border-pink-400"
              )}
              onClick={() => onSelectGender(gender as "male" | "female")}
            >
              {selectedGender === gender && !selectedCharacter && !uploadedImage && (
                <div className="absolute -top-2 -right-2 z-10 h-6 w-6 rounded-full bg-pink-500 flex items-center justify-center">
                  <Check className="h-4 w-4 text-white" />
                </div>
              )}
              <div className="flex flex-col items-center space-y-2">
                <div className="h-16 w-16 rounded-full bg-gradient-to-br from-pink-400 to-purple-500 flex items-center justify-center">
                  <span className="text-2xl">
                    {gender === "male" ? "👨" : "👩"}
                  </span>
                </div>
                <p className="text-sm font-medium capitalize">{gender}</p>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* 上传图片选项 */}
      <div className="space-y-3">
        <h3 className="text-lg font-semibold">Upload Your Own</h3>
        <div className="flex space-x-4">
          {/* 已上传的图片 */}
          {uploadedImage && (
            <div className="relative rounded-lg border-2 border-pink-500 bg-pink-500/10 p-2">
              <div className="absolute -top-2 -right-2 z-10 h-6 w-6 rounded-full bg-pink-500 flex items-center justify-center">
                <Check className="h-4 w-4 text-white" />
              </div>
              <div className="h-20 w-20 overflow-hidden rounded-lg">
                <Image
                  src={getCharacterImageUrl(uploadedImage)}
                  alt="Uploaded character"
                  width={80}
                  height={80}
                  className="h-full w-full object-cover"
                />
              </div>
              <p className="mt-2 text-center text-sm font-medium">
                Uploaded
              </p>
            </div>
          )}
          
          {/* 上传按钮 */}
          <Button
            variant="outline"
            className="h-auto flex-col space-y-2 p-4 bg-[#1a0a24] border-[#3a1a44] hover:border-pink-400"
            onClick={onOpenUpload}
          >
            <div className="h-16 w-16 rounded-full bg-gray-700 flex items-center justify-center">
              <Upload className="h-8 w-8 text-gray-400" />
            </div>
            <span className="text-sm">Upload Image</span>
          </Button>
        </div>
      </div>
    </div>
  );
}