import { useRef, useCallback } from 'react';
import { sendUsageDeduction, ChatUsageRequest } from '@/lib/api';

interface UseCallUsageProps {
  onInsufficientBalance: () => void; // 余额不足时的回调
}

export function useCallUsage({ onInsufficientBalance }: UseCallUsageProps) {
  const usageTimerRef = useRef<NodeJS.Timeout | null>(null);

  // 开始通话计费
  const startCallUsage = useCallback(
    (token: string) => {
      console.log('🎬 开始通话实时扣费');

      // 清除之前的计时器
      if (usageTimerRef.current) {
        clearInterval(usageTimerRef.current);
      }

      // 每10秒扣费一次，每次扣除10秒的通话时长
      usageTimerRef.current = setInterval(async () => {
        try {
          const usageRequest: ChatUsageRequest = {
            msgType: 'call',
            amount: 10, // 每次扣除10秒
          };

          await sendUsageDeduction(token, usageRequest);
          console.log('✅ 通话扣费成功: 10秒');
        } catch (error) {
          if (error instanceof Error && error.message === 'NEED_SUBSCRIPTION') {
            console.log('💰 通话余额不足，停止扣费并触发支付弹窗');
            
            // 停止计费
            if (usageTimerRef.current) {
              clearInterval(usageTimerRef.current);
              usageTimerRef.current = null;
            }

            // 触发余额不足回调
            onInsufficientBalance();
          } else {
            console.error('❌ 通话扣费失败:', error);
            // 其他错误不中断通话，但记录日志
          }
        }
      }, 10000); // 每10秒执行一次
    },
    [onInsufficientBalance]
  );

  // 停止通话计费
  const stopCallUsage = useCallback(() => {
    console.log('🛑 停止通话实时扣费');
    
    if (usageTimerRef.current) {
      clearInterval(usageTimerRef.current);
      usageTimerRef.current = null;
    }
  }, []);

  // 清理函数
  const cleanupCallUsage = useCallback(() => {
    stopCallUsage();
  }, [stopCallUsage]);

  return {
    startCallUsage,
    stopCallUsage,
    cleanupCallUsage,
  };
}