"use client"

import type React from "react"

import { useState } from "react"
import Link from "next/link"
import { useRouter } from "next/navigation"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Heart, Eye, EyeOff, Mail, Lock } from "lucide-react"
import { AuthAPI } from "@/lib/api/auth"
import { useUser } from "@/contexts/UserContext"
import Image from "next/image"

export default function LoginPage() {
  const router = useRouter()
  const { refreshUserData } = useUser()
  const [email, setEmail] = useState("")
  const [password, setPassword] = useState("")
  const [showPassword, setShowPassword] = useState(false)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState("")
  const [rememberMe, setRememberMe] = useState(false)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setError("")
    setLoading(true)

    try {
      const response = await AuthAPI.login({ email, password })
      console.log("Login response:", response)
      
      // 给localStorage一点时间更新，然后刷新UserContext状态
      setTimeout(async () => {
        await refreshUserData()
        // 直接跳转，不显示动画
        router.push("/")
      }, 100)
      
    } catch (error) {
      console.error("Login error:", error)
      setError("登录失败，请稍后重试")
    } finally {
      setLoading(false)
    }
  }

  const handleGoogleLogin = () => {
    // 直接跳转到后端Google OAuth端点
    const apiUrl = process.env.NEXT_PUBLIC_API_URL || 'https://api.loomelove.ai'
    window.location.href = `${apiUrl}/api/auth/google`
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-[#0a0a0f] via-[#1a0a24] to-[#2a1a34] flex items-center justify-center p-3 sm:p-4">
      {/* Background decoration */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-pink-500/10 rounded-full blur-3xl"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-purple-500/10 rounded-full blur-3xl"></div>
      </div>

      <div className="relative w-full max-w-4xl flex items-center justify-center">
        {/* Left side - Image */}
        <div className="hidden lg:block w-1/2 max-w-md mr-8">
          <div className="relative h-full">
            <Image
              src="/login-left-image.jpg"
              alt="Login visual"
              width={400}
              height={600}
              className="w-full h-auto object-cover rounded-2xl shadow-2xl"
              priority
            />
            {/* Image overlay for better visual appeal */}
            <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent rounded-2xl"></div>
          </div>
        </div>

        {/* Right side - Login form */}
        <div className="w-full max-w-sm sm:max-w-md lg:w-1/2">
          {/* Main login card */}
          <div className="bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-xl border border-white/20 rounded-2xl sm:rounded-3xl p-5 sm:p-8 shadow-2xl">
            {/* Header */}
            <div className="text-center mb-6 sm:mb-8">
              <div className="flex justify-center mb-4 sm:mb-6">
                <div className="relative">
                  <div className="h-16 w-16 sm:h-20 sm:w-20 rounded-full p-1 bg-gradient-to-r from-pink-500 via-purple-500 to-pink-500 animate-pulse">
                    <div className="h-full w-full rounded-full bg-gradient-to-br from-[#1a0a24] to-[#2a1a34] flex items-center justify-center">
                      <Heart className="h-8 w-8 sm:h-10 sm:w-10 text-white drop-shadow-lg" fill="currentColor" />
                    </div>
                  </div>
                  <div className="absolute -inset-2 bg-gradient-to-r from-pink-500/20 to-purple-500/20 rounded-full blur-lg -z-10"></div>
                </div>
              </div>
              <h1 className="text-2xl sm:text-4xl font-bold bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent mb-2 sm:mb-3">
                Welcome Back
              </h1>
              <p className="text-gray-400 text-sm sm:text-lg">Sign in to continue your journey</p>
            </div>

            {/* Error message */}
            {error && (
              <div className="mb-4 sm:mb-6 p-3 sm:p-4 bg-gradient-to-r from-red-500/10 to-red-600/10 border border-red-500/30 rounded-xl backdrop-blur-sm">
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-red-500 rounded-full animate-pulse"></div>
                  <span className="text-red-300 text-sm font-medium">{error}</span>
                </div>
              </div>
            )}

            {/* Login form */}
            <form onSubmit={handleSubmit} className="space-y-4 sm:space-y-6">
              {/* Email field */}
              <div className="space-y-1.5 sm:space-y-2">
                <Label htmlFor="email" className="text-white font-medium text-sm flex items-center space-x-2">
                  <Mail className="h-3.5 w-3.5 sm:h-4 sm:w-4 text-pink-400" />
                  <span>Email Address</span>
                </Label>
                <div className="relative">
                  <Input
                    id="email"
                    type="email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    placeholder="Enter your email"
                    required
                    className="h-10 sm:h-12 bg-gradient-to-r from-[#0e0314]/80 to-[#1a0a24]/80 border-[#3a1a44] text-white placeholder-gray-500 focus:border-pink-500 focus:ring-2 focus:ring-pink-500/20 rounded-xl backdrop-blur-sm transition-all duration-200"
                  />
                  <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-pink-500/5 to-purple-500/5 pointer-events-none"></div>
                </div>
              </div>

              {/* Password field */}
              <div className="space-y-1.5 sm:space-y-2">
                <Label htmlFor="password" className="text-white font-medium text-sm flex items-center space-x-2">
                  <Lock className="h-3.5 w-3.5 sm:h-4 sm:w-4 text-pink-400" />
                  <span>Password</span>
                </Label>
                <div className="relative">
                  <Input
                    id="password"
                    type={showPassword ? "text" : "password"}
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    placeholder="Enter your password"
                    required
                    className="h-10 sm:h-12 bg-gradient-to-r from-[#0e0314]/80 to-[#1a0a24]/80 border-[#3a1a44] text-white placeholder-gray-500 focus:border-pink-500 focus:ring-2 focus:ring-pink-500/20 rounded-xl backdrop-blur-sm transition-all duration-200 pr-10 sm:pr-12"
                  />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="absolute right-2.5 sm:right-3 top-1/2 -translate-y-1/2 text-gray-400 hover:text-white transition-colors"
                  >
                    {showPassword ? <EyeOff className="h-4 w-4 sm:h-5 sm:w-5" /> : <Eye className="h-4 w-4 sm:h-5 sm:w-5" />}
                  </button>
                  <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-pink-500/5 to-purple-500/5 pointer-events-none"></div>
                </div>
              </div>

              {/* Remember me and forgot password */}
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <input
                    id="remember-me"
                    type="checkbox"
                    checked={rememberMe}
                    onChange={(e) => setRememberMe(e.target.checked)}
                    className="h-4 w-4 rounded border-[#3a1a44] bg-[#0e0314] text-pink-500 focus:ring-pink-500 focus:ring-offset-0 focus:ring-2"
                  />
                  <label htmlFor="remember-me" className="text-sm text-gray-300 select-none cursor-pointer">
                    Remember me
                  </label>
                </div>
                <Link
                  href="/forgot-password"
                  className="text-sm text-pink-400 hover:text-pink-300 transition-colors font-medium"
                >
                  Forgot password?
                </Link>
              </div>

              {/* Submit button */}
              <Button
                type="submit"
                disabled={loading}
                className="w-full h-10 sm:h-12 bg-gradient-to-r from-pink-500 to-purple-600 hover:from-pink-600 hover:to-purple-700 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {loading ? (
                  <div className="flex items-center space-x-2">
                    <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                    <span>Signing in...</span>
                  </div>
                ) : (
                  "Sign In"
                )}
              </Button>
            </form>

            {/* Divider */}
            <div className="my-6 sm:my-8 flex items-center">
              <div className="flex-1 h-px bg-gradient-to-r from-transparent via-white/20 to-transparent"></div>
              <span className="px-3 sm:px-4 text-sm text-gray-400">or</span>
              <div className="flex-1 h-px bg-gradient-to-r from-transparent via-white/20 to-transparent"></div>
            </div>

            {/* Google 登录按钮 */}
            <div className="space-y-4">
              <Button
                type="button"
                onClick={handleGoogleLogin}
                className="w-full h-10 sm:h-12 bg-white hover:bg-gray-50 text-gray-900 font-semibold rounded-xl border border-gray-300 shadow-sm hover:shadow-md transition-all duration-200 flex items-center justify-center space-x-3"
              >
                <svg className="h-5 w-5" viewBox="0 0 24 24">
                  <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                  <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                  <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                  <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                </svg>
                <span>Continue with Google</span>
              </Button>
            </div>

            {/* Register link */}
            <div className="text-center">
              <p className="text-gray-400 text-sm">
                Don't have an account?{" "}
                <Link
                  href="/register"
                  className="text-pink-400 hover:text-pink-300 transition-colors font-medium"
                >
                  Create one now
                </Link>
              </p>
            </div>
          </div>

          {/* Additional info */}
          <div className="mt-4 sm:mt-6 text-center">
            <div className="flex items-center justify-center space-x-3 sm:space-x-4 text-xs text-gray-500">
              <Link href="/terms" className="hover:text-gray-400 transition-colors">
                Terms of Service
              </Link>
              <span>•</span>
              <Link href="/privacy" className="hover:text-gray-400 transition-colors">
                Privacy Policy
              </Link>
              <span>•</span>
              <Link href="/help" className="hover:text-gray-400 transition-colors">
                Help
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
} 