/**
 * 图包相关的React Hooks
 * 用于管理Gallery/Sneaky页面的状态和数据
 */

import { useState, useEffect, useCallback } from 'react';
import {
  getGalleryPackages,
  getPurchasedPackages,
  getPackageDetail,
  getFavoriteResources,
  toggleFavoriteResource,
  purchasePackage,
  type GalleryPackage,
  type PurchasedPackage,
  type FavoriteResource,
} from '@/lib/api/gallery';

// ====================
// 图包列表Hook
// ====================

export interface UseGalleryPackagesParams {
  page?: number;
  pageSize?: number;
  style?: string;
  intimacy?: string;
  tag?: string;
  keyword?: string;
  sortBy?: string;
  sortDirection?: 'asc' | 'desc';
}

export function useGalleryPackages(params: UseGalleryPackagesParams) {
  const [packages, setPackages] = useState<GalleryPackage[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [totalPages, setTotalPages] = useState(0);
  const [currentPage, setCurrentPage] = useState(params.page || 1);

  const fetchPackages = useCallback(async () => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await getGalleryPackages(params);
      setPackages(response.data.packages);
      setTotalPages(response.data.totalPages);
      setCurrentPage(response.data.currentPage);
    } catch (err) {
      // 出现错误时设置空数据，避免循环调用
      setPackages([]);
      setTotalPages(0);
      setCurrentPage(1);
      setError(err instanceof Error ? err.message : 'Failed to fetch packages');
      console.error('获取图包列表失败:', err);
    } finally {
      setLoading(false);
    }
  }, [JSON.stringify(params)]); // 使用JSON.stringify避免对象引用导致的无限循环

  useEffect(() => {
    fetchPackages();
  }, [fetchPackages]);

  return {
    packages,
    loading,
    error,
    totalPages,
    currentPage,
    refetch: fetchPackages,
  };
}

// ====================
// 已购买图包Hook
// ====================

export interface UsePurchasedPackagesParams {
  page?: number;
  pageSize?: number;
}

export function usePurchasedPackages(params: UsePurchasedPackagesParams = {}) {
  const [purchasedPackages, setPurchasedPackages] = useState<PurchasedPackage[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [total, setTotal] = useState(0);

  const fetchPurchasedPackages = useCallback(async () => {
    const token = localStorage.getItem('token');
    if (!token) {
      setPurchasedPackages([]);
      setTotal(0);
      setError('用户未登录');
      setLoading(false);
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const response = await getPurchasedPackages(params);
      setPurchasedPackages(response.data.packages);
      setTotal(response.data.total);
    } catch (err) {
      // 出现错误时设置空数据
      setPurchasedPackages([]);
      setTotal(0);
      setError(err instanceof Error ? err.message : 'Failed to fetch purchased packages');
      console.error('获取已购买图包失败:', err);
    } finally {
      setLoading(false);
    }
  }, [JSON.stringify(params)]);

  useEffect(() => {
    fetchPurchasedPackages();
  }, [fetchPurchasedPackages]);

  return {
    purchasedPackages,
    loading,
    error,
    total,
    refetch: fetchPurchasedPackages,
  };
}

// ====================
// 图包详情Hook
// ====================

export function usePackageDetail(packageId: number) {
  const [packageDetail, setPackageDetail] = useState<GalleryPackage | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchPackageDetail = useCallback(async () => {
    if (!packageId) {
      setPackageDetail(null);
      setLoading(false);
      return;
    }

    const token = localStorage.getItem('token');
    if (!token) {
      setPackageDetail(null);
      setError('用户未登录');
      setLoading(false);
      return;
    }
    
    setLoading(true);
    setError(null);

    try {
      const response = await getPackageDetail(packageId);
      setPackageDetail(response.data.package);
    } catch (err) {
      // 出现错误时设置空数据
      setPackageDetail(null);
      setError(err instanceof Error ? err.message : 'Failed to fetch package detail');
      console.error('获取图包详情失败:', err);
    } finally {
      setLoading(false);
    }
  }, [packageId]);

  useEffect(() => {
    fetchPackageDetail();
  }, [fetchPackageDetail]);

  return {
    packageDetail,
    loading,
    error,
    refetch: fetchPackageDetail,
  };
}

// ====================
// 收藏列表Hook
// ====================

export interface UseFavoriteResourcesParams {
  page?: number;
  pageSize?: number;
}

export function useFavoriteResources(params: UseFavoriteResourcesParams = {}) {
  const [favorites, setFavorites] = useState<FavoriteResource[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [total, setTotal] = useState(0);

  const fetchFavorites = useCallback(async () => {
    const token = localStorage.getItem('token');
    if (!token) {
      setFavorites([]);
      setTotal(0);
      setError('用户未登录');
      setLoading(false);
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const response = await getFavoriteResources(params);
      setFavorites(response.data.favorites);
      setTotal(response.data.total);
    } catch (err) {
      // 出现错误时设置空数据
      setFavorites([]);
      setTotal(0);
      setError(err instanceof Error ? err.message : 'Failed to fetch favorites');
      console.error('获取收藏列表失败:', err);
    } finally {
      setLoading(false);
    }
  }, [JSON.stringify(params)]);

  useEffect(() => {
    fetchFavorites();
  }, [fetchFavorites]);

  return {
    favorites,
    loading,
    error,
    total,
    refetch: fetchFavorites,
  };
}

// ====================
// 收藏操作Hook
// ====================

export function useFavoriteAction() {
  const [loading, setLoading] = useState(false);

  const toggleFavorite = useCallback(async (
    resourceId: number,
    currentIsFavorited: boolean
  ) => {
    const token = localStorage.getItem('token');
    if (!token) {
      throw new Error('用户未登录');
    }

    setLoading(true);
    try {
      const collect = !currentIsFavorited; // 切换收藏状态
      const response = await toggleFavoriteResource(resourceId, collect);
      return response.data.isFavorited;
    } catch (err) {
      console.error('收藏操作失败:', err);
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  return {
    toggleFavorite,
    loading,
  };
}

// ====================
// 购买操作Hook
// ====================

export function usePurchaseAction() {
  const [loading, setLoading] = useState(false);

  const purchasePackageAction = useCallback(async (packageId: number) => {
    const token = localStorage.getItem('token');
    if (!token) {
      throw new Error('用户未登录');
    }

    setLoading(true);
    try {
      const response = await purchasePackage(packageId);
      
      // TODO: 根据API响应决定是否需要跳转到支付页面
      if (response.data.paymentUrl) {
        // 如果有支付URL，可能需要跳转到支付页面
        console.log('需要跳转到支付页面:', response.data.paymentUrl);
        // window.location.href = response.data.paymentUrl;
      }
      
      return response.data;
    } catch (err) {
      console.error('购买图包失败:', err);
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  return {
    purchasePackage: purchasePackageAction,
    loading,
  };
}