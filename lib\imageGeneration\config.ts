// 图片生成相关的配置数据

export interface OptionItem {
  key: string;
  label: string;
  thumbnail: string;
}

export interface ClothingOptions {
  male: Record<string, string>;
  female: Record<string, string>;
}

// 场景选项配置
export const SCENE_OPTIONS: Record<string, string> = {
  "Bedroom": "/placeholder.svg?height=100&width=100&text=Bedroom",
  "Bathroom": "/placeholder.svg?height=100&width=100&text=Bathroom", 
  "Kitchen": "/placeholder.svg?height=100&width=100&text=Kitchen",
  "Swimming Pool": "/placeholder.svg?height=100&width=100&text=Pool",
  "Garden": "/placeholder.svg?height=100&width=100&text=Garden",
  "Gym": "/placeholder.svg?height=100&width=100&text=Gym",
  "Elevator": "/placeholder.svg?height=100&width=100&text=Elevator",
  "Office": "/placeholder.svg?height=100&width=100&text=Office",
  "Classroom": "/placeholder.svg?height=100&width=100&text=Classroom",
  "High-End Restaurant": "/placeholder.svg?height=100&width=100&text=Restaurant",
  "Beach": "/placeholder.svg?height=100&width=100&text=Beach",
  "Dungeon": "/placeholder.svg?height=100&width=100&text=Dungeon",
  "Bar": "/placeholder.svg?height=100&width=100&text=Bar",
  "Street": "/placeholder.svg?height=100&width=100&text=Street",
  "Five-Star Hotel": "/placeholder.svg?height=100&width=100&text=Hotel",
};

// 服装选项配置（按性别分类）
export const CLOTHING_OPTIONS: ClothingOptions = {
  male: {
    "Jeans And T-Shirt": "/placeholder.svg?height=100&width=100&text=Jeans+Tee",
    "Suit": "/placeholder.svg?height=100&width=100&text=Suit",
    "Leather Jacket": "/placeholder.svg?height=100&width=100&text=Leather",
    "Sports Outfit": "/placeholder.svg?height=100&width=100&text=Sports", 
    "High-End Trench Coat": "/placeholder.svg?height=100&width=100&text=Trench+Coat",
    "Sweater": "/placeholder.svg?height=100&width=100&text=Sweater",
    "Preppy Style Outfit": "/placeholder.svg?height=100&width=100&text=Preppy",
    "Bathrobe": "/placeholder.svg?height=100&width=100&text=Bathrobe",
    "Groom Suit": "/placeholder.svg?height=100&width=100&text=Groom",
  },
  female: {
    "Casual Outfit": "/placeholder.svg?height=100&width=100&text=Casual",
    "Elegant Dress": "/placeholder.svg?height=100&width=100&text=Dress",
    "Business Attire": "/placeholder.svg?height=100&width=100&text=Business",
    "Workout Clothes": "/placeholder.svg?height=100&width=100&text=Workout",
    "Evening Gown": "/placeholder.svg?height=100&width=100&text=Gown",
    "Sweater & Jeans": "/placeholder.svg?height=100&width=100&text=Sweater",
    "Summer Outfit": "/placeholder.svg?height=100&width=100&text=Summer",
    "Bathrobe": "/placeholder.svg?height=100&width=100&text=Bathrobe", 
    "Wedding Dress": "/placeholder.svg?height=100&width=100&text=Wedding",
  },
};

// 姿势选项配置
export const POSE_OPTIONS: Record<string, string> = {
  "Standing": "/placeholder.svg?height=100&width=100&text=Standing",
  "Sitting": "/placeholder.svg?height=100&width=100&text=Sitting", 
  "Kneeling": "/placeholder.svg?height=100&width=100&text=Kneeling",
  "Hands On Hip": "/placeholder.svg?height=100&width=100&text=Hands+Hip",
  "Leaning": "/placeholder.svg?height=100&width=100&text=Leaning",
  "Turned Away": "/placeholder.svg?height=100&width=100&text=Turned",
};

// 角度选项配置
export const ANGLE_OPTIONS: Record<string, string> = {
  "Full Body": "/placeholder.svg?height=100&width=100&text=Full",
  "Half Body": "/placeholder.svg?height=100&width=100&text=Half", 
  "Front View": "/placeholder.svg?height=100&width=100&text=Front",
  "Side View": "/placeholder.svg?height=100&width=100&text=Side",
  "Back View": "/placeholder.svg?height=100&width=100&text=Back",
};

// 图片数量选项
export const IMAGE_COUNT_OPTIONS = [
  { value: 1, label: "1 Image", isPremium: false },
  { value: 4, label: "4 Images", isPremium: true },
  { value: 9, label: "9 Images", isPremium: true },
];

// 风格选项
export const STYLE_OPTIONS = [
  { value: "realistic", label: "Realistic" },
  { value: "anime", label: "Anime" },
];

// 性别选项
export const GENDER_OPTIONS = [
  { value: "male", label: "Male" },
  { value: "female", label: "Female" },
];

// 工具函数：获取随机选项
export function getRandomOption(options: Record<string, string>): string {
  const keys = Object.keys(options);
  return keys[Math.floor(Math.random() * keys.length)];
}

// 工具函数：获取性别对应的服装选项
export function getClothingOptions(gender: "male" | "female"): Record<string, string> {
  return CLOTHING_OPTIONS[gender];
}

// 工具函数：转换配置对象为选项数组
export function configToOptions(config: Record<string, string>): OptionItem[] {
  return Object.entries(config).map(([key, thumbnail]) => ({
    key,
    label: key,
    thumbnail,
  }));
}