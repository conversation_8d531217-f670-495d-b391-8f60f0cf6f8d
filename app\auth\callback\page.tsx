'use client'

import { useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useUser } from '@/contexts/UserContext';

export default function AuthCallbackPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { refreshUserData } = useUser();

  useEffect(() => {
    const token = searchParams.get('token');
    const refresh = searchParams.get('refresh');
    const error = searchParams.get('error');

    console.log('OAuth callback received:', { token: !!token, refresh: !!refresh, error });

    if (error) {
      console.error('OAuth error:', error);
      router.push('/login?error=oauth_failed');
      return;
    }

    if (token && refresh) {
      // 存储token
      localStorage.setItem('token', token);
      localStorage.setItem('refreshToken', refresh);
      localStorage.setItem('isLoggedIn', 'true');
      
      console.log('OAuth tokens stored, refreshing user data...');
      
      // 刷新用户数据
      refreshUserData().then(() => {
        console.log('User data refreshed, redirecting to home');
        router.push('/');
      }).catch((error) => {
        console.error('Failed to refresh user data:', error);
        router.push('/login?error=refresh_failed');
      });
    } else {
      console.error('Missing tokens in callback');
      router.push('/login?error=invalid_token');
    }
  }, [searchParams, router, refreshUserData]);

  return (
    <div className="min-h-screen bg-gradient-to-br from-[#0a0a0f] via-[#1a0a24] to-[#2a1a34] flex items-center justify-center">
      <div className="text-center">
        <div className="w-16 h-16 border-4 border-pink-500/30 border-t-pink-500 rounded-full animate-spin mx-auto mb-4"></div>
        <p className="text-white text-lg">正在登录...</p>
        <p className="text-gray-400 text-sm mt-2">请稍候，我们正在为您完成登录</p>
      </div>
    </div>
  );
}