import type { Config } from "tailwindcss"

const config: Config = {
  content: ["./pages/**/*.{js,ts,jsx,tsx}", "./components/**/*.{js,ts,jsx,tsx}", "*.{js,ts,jsx,tsx,mdx}"],
  theme: {
    extend: {
      colors: {
        heartBgDark: "#0A001A",
        heartFillPinkLight: "#FFB6C1", // Light pink
        heartFillPinkMedium: "#FF99AA", // Medium pink/coral
        heartFillPurpleLight: "#DDA0DD", // Light lavender/purple
        heartFillPurpleMedium: "#B066B0", // Medium purple
      },
      borderRadius: {
        lg: "var(--radius)",
        md: "calc(var(--radius) - 2px)",
        sm: "calc(var(--radius) - 4px)",
      },
    },
  },
  plugins: [require("tailwindcss-animate")],
}

export default config
