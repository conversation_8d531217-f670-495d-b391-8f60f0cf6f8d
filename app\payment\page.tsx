"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, DialogHeader, DialogTitle, DialogDescription } from "@/components/ui/dialog"
import { Carousel, CarouselContent, CarouselItem, CarouselNext, CarouselPrevious } from "@/components/ui/carousel"
import Sidebar from "@/components/sidebar"
import MobileBottomNav from "@/components/mobile-bottom-nav"
import { useSubscriptionPlans, useSubscribeAction } from "@/hooks/useSubscription"
import { convertPrivilegeToFeatures } from "@/lib/api/subscription"
import type { SubscriptionPlan } from "@/lib/api/subscription"
import { useUser, useUserPlan } from "@/contexts/UserContext"
import UserPlanHeader from "@/components/UserPlanHeader"
import { PageGuard, PageAccessLevel } from "@/components/auth/PageGuard"


type PlanType = "Free" | "Prime" | "Plus" | "Pro" | "Lifetime"

interface DialogState {
  isOpen: boolean
  type: "subscribe" | "upgrade" | "downgrade" | "success" | "error" | "already-subscribed"
  targetPlan?: PlanType
  currentPlan?: PlanType
}

export default function PaymentPage() {
  return (
    <PageGuard accessLevel={PageAccessLevel.AUTH_REQUIRED}>
      <PaymentPageContent />
    </PageGuard>
  )
}

function PaymentPageContent() {
  const { user, isLoggedIn, refreshUserData } = useUser()
  const { planDisplayName } = useUserPlan()
  const [currentSubscription, setCurrentSubscription] = useState<PlanType>("Free")
  const [dialog, setDialog] = useState<DialogState>({ isOpen: false, type: "subscribe" })
  const [isProcessing, setIsProcessing] = useState(false)
  
  // 获取API数据
  const { plans: apiPlans, loading: plansLoading, error: plansError } = useSubscriptionPlans()
  const { subscribe, loading: subscribeLoading } = useSubscribeAction()
  const [paymentStatus, setPaymentStatus] = useState<string | null>(null)

  // 同步当前套餐状态
  useEffect(() => {
    console.log('💳 Payment page state update:', { 
      planDisplayName, 
      currentSubscription,
      user: user?.id,
      isLoggedIn,
      userPlan: user?.plan?.id 
    })
    
    if (planDisplayName) {
      setCurrentSubscription(planDisplayName as PlanType)
    }
  }, [planDisplayName, user, isLoggedIn])

  // 合并API数据和前端UI配置的函数
  const mergeApiDataWithUIConfig = () => {
    // 根据新的UI配置更新套餐
    const defaultPlans = [
      {
        name: "Free" as PlanType,
        price: 0,
        period: "mo",
        duration: "",
        badge: null,
        features: [
          "✅ 30 text messages / month",
          "✅ 1 picture / month",
          "✅ 1 voice message / month",
          "✅ Create 1 character",
          "✅ 30-second voice call trial",
          "❌ Multi-picture generation (4/9)",
          "✅ Standard response speed",
          "✅ Cancel anytime",
          "✅ No credit card required",
        ],
        note: "No credit card required",
        apiId: 0,
      },
      {
        name: "Prime" as PlanType,
        price: 12.99,
        period: "mo",
        duration: "1 month",
        badge: null,
        features: [
          "✅ 1,000 text messages / month",
          "✅ 50 pictures / month",
          "✅ 60 voice messages / month",
          "✅ Create up to 2 characters",
          "✅ 3-minute voice call / month",
          "❌ Multi-picture generation (4/9)",
          "✅ Standard response speed",
          "✅ Cancel anytime",
        ],
        note: "Cancel anytime",
        apiId: 1,
      },
      {
        name: "Plus" as PlanType,
        price: 8.99,
        period: "mo",
        duration: "3 months",
        badge: "⭐ Most Popular",
        badgeColor: "from-pink-400 to-pink-500",
        features: [
          "✅ 3,000 text messages / month",
          "✅ 150 pictures / month",
          "✅ 180 voice messages / month",
          "✅ Create up to 10 characters",
          "✅ 10-minute voice call / month",
          "❌ Multi-picture generation (4/9)",
          "✅ Standard response speed",
          "✅ Cancel anytime",
        ],
        note: "3-month plan • Cancel anytime",
        apiId: 2,
      },
      {
        name: "Pro" as PlanType,
        price: 5.99,
        period: "mo",
        duration: "12 months",
        badge: null,
        features: [
          "✅ Unlimited text messages",
          "✅ 200 pictures / month",
          "✅ Unlimited voice messages",
          "✅ Create unlimited characters",
          "✅ Unlimited voice call time",
          "✅ Multi-picture generation (4/9)",
          "✅ VIP response speed",
          "✅ Cancel anytime",
        ],
        note: "12-month plan • Cancel anytime",
        apiId: 3,
      },
      {
        name: "Lifetime" as PlanType,
        price: 199.99,
        period: "total",
        duration: "",
        badge: null,
        features: [
          "✅ Unlimited text messages",
          "✅ 200 pictures / month",
          "✅ Unlimited voice messages",
          "✅ Create unlimited characters",
          "✅ Unlimited voice call time",
          "✅ Multi-picture generation (4/9)",
          "✅ VIP response speed",
          "✅ Lifetime access",
        ],
        note: "One-time purchase • Lifetime access",
        apiId: 4,
      },
    ];

    // 如果API数据加载成功，用API数据更新价格和特性
    if (!plansLoading && !plansError && apiPlans.length > 0) {
      return defaultPlans.map(defaultPlan => {
        // 根据级别查找对应的API套餐
        const apiPlan = apiPlans.find(api => api.level === defaultPlan.name);

        if (apiPlan) {
          const updatedPlan = { ...defaultPlan };
          updatedPlan.price = apiPlan.price;
          updatedPlan.apiId = apiPlan.id;
          
          // 更新特性（如果API提供）
          if (apiPlan.features) {
            updatedPlan.features = convertPrivilegeToFeatures(apiPlan.features, apiPlan.level);
          }

          return updatedPlan;
        }

        return defaultPlan;
      });
    }

    return defaultPlans;
  };

  const plans = mergeApiDataWithUIConfig();

  const planHierarchy = { Free: 0, Prime: 1, Plus: 2, Pro: 3, Lifetime: 4 }

  const handleSubscribeClick = (targetPlan: PlanType) => {
    console.log('🔄 handleSubscribeClick called:', { 
      targetPlan, 
      currentSubscription, 
      planDisplayName,
      user: user?.id,
      isLoggedIn 
    })
    
    if (targetPlan === "Free") return

    const currentLevel = planHierarchy[currentSubscription] ?? 0  // undefined按Free处理
    const targetLevel = planHierarchy[targetPlan]
    
    console.log('📊 Plan levels:', { 
      currentLevel, 
      targetLevel, 
      currentSubscription
    })

    if (currentLevel === targetLevel) {
      setDialog({
        isOpen: true,
        type: "already-subscribed",
        targetPlan,
        currentPlan: currentSubscription,
      })
    } else if (currentLevel > targetLevel) {
      setDialog({
        isOpen: true,
        type: "downgrade",
        targetPlan,
        currentPlan: currentSubscription,
      })
    } else if (currentLevel < targetLevel) {
      if (currentLevel === 0) {  // Free或undefined都按Free处理
        console.log('🎯 Triggering subscribe dialog for Free user')
        setDialog({
          isOpen: true,
          type: "subscribe",
          targetPlan,
          currentPlan: currentSubscription,
        })
      } else {
        console.log('🎯 Triggering upgrade dialog')
        setDialog({
          isOpen: true,
          type: "upgrade",
          targetPlan,
          currentPlan: currentSubscription,
        })
      }
    }
  }

  const handleConfirmSubscription = async () => {
    setIsProcessing(true)
    setPaymentStatus('正在处理订阅请求...')

    try {
      // 找到对应的套餐API ID
      const targetPlan = plans.find(p => p.name === dialog.targetPlan)
      if (!targetPlan) {
        throw new Error('未找到对应的套餐')
      }

      console.log('开始订阅套餐:', { planName: dialog.targetPlan, apiId: targetPlan.apiId })

      const result = await subscribe(targetPlan.apiId)
      
      if (result.paymentWindow) {
        // 支付窗口已打开，显示提示信息
        setPaymentStatus('支付页面已在新窗口中打开，请完成支付')
        console.log('支付窗口已打开:', result.paymentUrl)
        
        const paymentWindow = result.paymentWindow
        
        // 监听支付窗口关闭事件
        const checkClosed = setInterval(() => {
          if (paymentWindow.closed) {
            clearInterval(checkClosed)
            console.log('支付窗口已关闭')
            
            // 支付窗口关闭后询问用户支付结果
            setPaymentStatus('支付窗口已关闭，正在确认支付状态...')
            
            // 3秒后询问用户支付结果
            setTimeout(() => {
              const paymentSuccess = window.confirm('支付是否成功？\n\n点击"确定"如果支付成功\n点击"取消"如果支付失败或取消')
              
              if (paymentSuccess) {
                setPaymentStatus('支付成功！')
                setCurrentSubscription(dialog.targetPlan!)
                
                // 触发全局用户数据刷新
                window.dispatchEvent(new CustomEvent('paymentSuccess'))
                refreshUserData()
                
                setDialog({
                  isOpen: true,
                  type: "success",
                  targetPlan: dialog.targetPlan,
                  currentPlan: dialog.currentPlan,
                })
                setTimeout(() => {
                  setPaymentStatus(null)
                }, 3000)
              } else {
                setPaymentStatus('支付已取消或失败')
                setDialog({
                  isOpen: true,
                  type: "error",
                  targetPlan: dialog.targetPlan,
                  currentPlan: dialog.currentPlan,
                })
                setTimeout(() => {
                  setPaymentStatus(null)
                }, 3000)
              }
              setIsProcessing(false)
            }, 3000)
          }
        }, 1000)
        
        // 监听支付窗口消息（如果支付页面支持postMessage）
        const handlePaymentMessage = (event: MessageEvent) => {
          if (result.paymentUrl && event.origin !== new URL(result.paymentUrl).origin) return
          
          if (event.data.type === 'PAYMENT_SUCCESS') {
            console.log('支付成功')
            setPaymentStatus('支付成功！')
            setCurrentSubscription(dialog.targetPlan!)
            
            // 触发全局用户数据刷新
            window.dispatchEvent(new CustomEvent('paymentSuccess'))
            refreshUserData()
            
            setDialog({
              isOpen: true,
              type: "success",
              targetPlan: dialog.targetPlan,
              currentPlan: dialog.currentPlan,
            })
            clearInterval(checkClosed) // 清除窗口监听
            setTimeout(() => {
              setPaymentStatus(null)
              setIsProcessing(false)
            }, 3000)
            window.removeEventListener('message', handlePaymentMessage)
          } else if (event.data.type === 'PAYMENT_FAILED') {
            console.log('支付失败')
            setPaymentStatus('支付失败，请重试')
            setDialog({
              isOpen: true,
              type: "error",
              targetPlan: dialog.targetPlan,
              currentPlan: dialog.currentPlan,
            })
            clearInterval(checkClosed) // 清除窗口监听
            setTimeout(() => {
              setPaymentStatus(null)
              setIsProcessing(false)
            }, 3000)
            window.removeEventListener('message', handlePaymentMessage)
          }
        }
        
        window.addEventListener('message', handlePaymentMessage)
        
        // 10分钟后自动清理监听器和状态
        setTimeout(() => {
          window.removeEventListener('message', handlePaymentMessage)
          clearInterval(checkClosed)
          if (isProcessing) {
            setPaymentStatus(null)
            setIsProcessing(false)
          }
        }, 10 * 60 * 1000)
        
      } else {
        // 直接订阅成功的情况
        setPaymentStatus('订阅成功！')
        setCurrentSubscription(dialog.targetPlan!)
        setDialog({
          isOpen: true,
          type: "success",
          targetPlan: dialog.targetPlan,
          currentPlan: dialog.currentPlan,
        })
        setTimeout(() => {
          setPaymentStatus(null)
          setIsProcessing(false)
        }, 2000)
      }
    } catch (error) {
      console.error('订阅失败:', error)
      setPaymentStatus(`订阅失败: ${(error as Error).message}`)
      setDialog({
        isOpen: true,
        type: "error",
        targetPlan: dialog.targetPlan,
        currentPlan: dialog.currentPlan,
      })
      setTimeout(() => {
        setPaymentStatus(null)
        setIsProcessing(false)
      }, 3000)
    }
  }

  const closeDialog = () => {
    setDialog({ isOpen: false, type: "subscribe" })
  }

  const getButtonState = (planName: PlanType) => {
    const currentLevel = planHierarchy[currentSubscription] ?? 0  // undefined按Free处理
    const planLevel = planHierarchy[planName]

    if (planName === "Free") {
      return currentLevel === 0 ? "subscribed" : "downgrade"
    }

    if (currentLevel === planLevel) {
      return "current"
    } else if (currentLevel > planLevel) {
      return "downgrade"
    } else {
      return "available"
    }
  }

  const getUpgradeFeatures = (targetPlan: PlanType) => {
    switch (targetPlan) {
      case "Prime":
        return ["1,000 text messages per month", "50 pictures per month", "Up to 2 characters", "3-minute voice calls"]
      case "Plus":
        return ["3,000 text messages per month", "150 pictures per month", "Up to 10 characters", "10-minute voice calls"]
      case "Pro":
        return [
          "Unlimited text messages",
          "200 pictures per month",
          "Unlimited voice messages",
          "Multi-picture generation (4/9)",
          "VIP response speed",
        ]
      case "Lifetime":
        return ["All Pro features", "Lifetime access", "One-time payment", "No recurring charges"]
      default:
        return []
    }
  }

  return (
    <div className="flex min-h-screen">
      {/* 桌面端显示侧边栏，移动端隐藏 */}
      <div className="hidden md:block">
        <Sidebar />
      </div>

      <main className="flex-1 bg-[#0e0314] p-3 pb-24 md:p-4 lg:p-8 md:pb-8">
        <div className="max-w-8xl mx-auto">
          {/* 移动端标题栏 */}
          <div className="md:hidden flex items-center justify-between mb-4 px-2">
              <button
                onClick={() => window.history.back()}
                className="w-8 h-8 bg-black/20 hover:bg-black/40 backdrop-blur-sm border border-white/20 rounded-full flex items-center justify-center transition-all duration-200"
              >
                <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                </svg>
              </button>
              <h1 className="text-lg font-bold text-white">Subscription Plans</h1>
              <div className="w-8 h-8"></div> {/* 占位符保持居中 */}
            </div>

          {/* Payment Status Banner */}
          {paymentStatus && (
            <div className="mb-6 bg-gradient-to-r from-pink-500/20 to-purple-500/20 rounded-xl p-4 border border-pink-500/30">
              <div className="flex items-center justify-center">
                <div className="text-center">
                  <div className="text-lg font-medium text-pink-300 mb-2">
                    {paymentStatus}
                  </div>
                  {paymentStatus.includes('支付页面已在新窗口中打开') && (
                    <div className="text-sm text-gray-300">
                      请在新窗口中完成支付，支付完成后页面将自动更新
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}

          {/* Loading State */}
          {plansLoading && (
            <div className="flex justify-center items-center py-12 mb-8">
              <div className="text-white">
                <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-pink-500 mx-auto mb-4"></div>
                <p className="text-lg">Loading subscription plans...</p>
              </div>
            </div>
          )}

          {/* Error State */}
          {plansError && (
            <div className="flex flex-col items-center justify-center py-12 mb-8">
              <div className="text-gray-400 mb-4">📋</div>
              <div className="text-lg font-medium mb-2">无法加载套餐信息</div>
              <div className="text-gray-400 text-sm text-center max-w-md">
                {plansError.includes('403') || plansError.includes('访问被拒绝') 
                  ? '您暂时没有权限访问套餐信息' 
                  : '套餐信息暂时无法加载，将显示默认套餐'}
              </div>
            </div>
          )}

          <div className="text-center mb-4 md:mb-8 lg:mb-16">
            <h1 className="text-xl md:text-2xl lg:text-5xl font-bold mb-2 md:mb-4 lg:mb-6 bg-gradient-to-r from-pink-400 to-purple-500 bg-clip-text text-transparent px-4">
              Choose your plan
            </h1>
            <p className="hidden md:block text-gray-300 max-w-3xl mx-auto text-base lg:text-xl leading-relaxed px-4">
              Ready to fall deeper? Upgrade to enjoy more messages, voices, images, and romantic possibilities.
            </p>
            <div className="mt-1 md:mt-3 lg:mt-4 text-pink-400 text-sm md:text-base lg:text-lg">
              Current Plan: <span className="font-bold">{currentSubscription}</span>
            </div>
          </div>

          {/* Pricing Cards */}
          {/* 移动端滑动卡片 */}
          <div className="md:hidden px-4">
            <div className="text-center mb-3">
              <p className="text-gray-400 text-xs">👈 Swipe to explore plans 👉</p>
            </div>
            <Carousel
              opts={{
                align: "start",
                loop: false,
              }}
              className="w-full"
            >
                <CarouselContent className="-ml-2 md:-ml-4">
                  {plans.map((plan) => {
                    const buttonState = getButtonState(plan.name)

                    return (
                      <CarouselItem key={plan.name} className="pl-2 md:pl-4 basis-4/5 sm:basis-3/5">
                        <div
                          className={`rounded-2xl p-4 sm:p-6 relative border-2 flex flex-col transition-all duration-300 ${
                            plan.name === "Plus"
                              ? "bg-gradient-to-br from-pink-500 to-pink-600 border-pink-400"
                              : "bg-[#1a0a24] border-[#3a1a44]"
                          } ${currentSubscription === plan.name ? "ring-2 ring-pink-400" : ""}`}
                          style={{ minHeight: "420px" }}
                        >
                          {plan.badge && (
                            <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                              <div
                                className={`bg-gradient-to-r ${plan.badgeColor} text-white px-3 py-1 rounded-full text-xs font-bold flex items-center whitespace-nowrap`}
                              >
                                {plan.badge}
                              </div>
                            </div>
                          )}

                          {currentSubscription === plan.name && (
                            <div className="absolute -top-4 right-4">
                              <div className="bg-green-500 text-white px-2 py-1 rounded-full text-xs font-bold">✅ Current</div>
                            </div>
                          )}

                          <div className="flex-1">
                            {/* Plan Name and Duration */}
                            <div className="flex justify-between items-start mb-3">
                              <h3 className="text-xl font-bold text-white">{plan.name}</h3>
                              {plan.duration && <span className="text-gray-300 text-xs">{plan.duration}</span>}
                            </div>

                            {/* Price */}
                            <div className="mb-4">
                              <div className="text-2xl font-bold text-white">
                                ${plan.price.toFixed(2)}
                                <span className="text-sm text-gray-300">/{plan.period}</span>
                              </div>
                            </div>

                            {/* Features */}
                            <div className="space-y-2 mb-4">
                              {plan.features.map((feature, featureIndex) => (
                                <div key={featureIndex} className="text-white text-xs leading-relaxed">
                                  {feature}
                                </div>
                              ))}
                            </div>
                          </div>

                          {/* Button */}
                          <div className="mt-auto">
                            <Button
                              className={`w-full py-2 text-xs font-semibold ${
                                buttonState === "subscribed" || buttonState === "current"
                                  ? "bg-gray-600 text-gray-300 cursor-not-allowed"
                                  : buttonState === "downgrade"
                                    ? "bg-gray-700 text-gray-400 cursor-not-allowed"
                                    : plan.name === "Plus"
                                      ? "bg-white text-pink-600 hover:bg-gray-100"
                                      : "bg-white text-black hover:bg-gray-100"
                              }`}
                              disabled={
                                buttonState === "subscribed" || buttonState === "current" || buttonState === "downgrade"
                              }
                              onClick={() => handleSubscribeClick(plan.name)}
                            >
                              {buttonState === "subscribed"
                                ? "SUBSCRIBED"
                                : buttonState === "current"
                                  ? "SUBSCRIBED"
                                  : buttonState === "downgrade"
                                    ? plan.name === "Free"
                                      ? "DOWNGRADE NOT AVAILABLE"
                                      : "DOWNGRADE NOT AVAILABLE"
                                    : currentSubscription === "Free"
                                      ? "SUBSCRIBE"
                                      : "UPGRADE"}
                            </Button>

                            <div className="text-center mt-2">
                              <p className="text-gray-300 text-xs">🕒 {plan.note}</p>
                            </div>
                          </div>
                        </div>
                      </CarouselItem>
                    )
                  })}
                </CarouselContent>
                <CarouselPrevious className="hidden sm:flex" />
                <CarouselNext className="hidden sm:flex" />
              </Carousel>
            </div>

          {/* 桌面端网格布局 */}
          <div className="hidden md:grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
              {plans.map((plan) => {
                const buttonState = getButtonState(plan.name)

                return (
                  <div
                    key={plan.name}
                    className={`rounded-2xl p-6 relative border-2 flex flex-col transition-all duration-300 hover:scale-105 ${
                      plan.name === "Plus"
                        ? "bg-gradient-to-br from-pink-500 to-pink-600 border-pink-400"
                        : "bg-[#1a0a24] border-[#3a1a44]"
                    } ${currentSubscription === plan.name ? "ring-2 ring-pink-400" : ""}`}
                    style={{ minHeight: "600px" }}
                  >
                  {plan.badge && (
                    <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                      <div
                        className={`bg-gradient-to-r ${plan.badgeColor} text-white px-4 py-2 rounded-full text-sm font-bold flex items-center whitespace-nowrap`}
                      >
                        {plan.badge}
                      </div>
                    </div>
                  )}

                  {currentSubscription === plan.name && (
                    <div className="absolute -top-4 right-4">
                      <div className="bg-green-500 text-white px-3 py-1 rounded-full text-xs font-bold">✅ Current</div>
                    </div>
                  )}

                  <div className="flex-1">
                    {/* Plan Name and Duration */}
                    <div className="flex justify-between items-start mb-4">
                      <h3 className="text-2xl font-bold text-white">{plan.name}</h3>
                      {plan.duration && <span className="text-gray-300 text-sm">{plan.duration}</span>}
                    </div>

                    {/* Price */}
                    <div className="mb-6">
                      <div className="text-4xl font-bold text-white">
                        ${plan.price.toFixed(2)}
                        <span className="text-lg text-gray-300">/{plan.period}</span>
                      </div>
                    </div>

                    {/* Features */}
                    <div className="space-y-3 mb-6">
                      {plan.features.map((feature, featureIndex) => (
                        <div key={featureIndex} className="text-white text-sm leading-relaxed">
                          {feature}
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Button */}
                  <div className="mt-auto">
                    <Button
                      className={`w-full py-3 text-sm font-semibold ${
                        buttonState === "subscribed" || buttonState === "current"
                          ? "bg-gray-600 text-gray-300 cursor-not-allowed"
                          : buttonState === "downgrade"
                            ? "bg-gray-700 text-gray-400 cursor-not-allowed"
                            : plan.name === "Plus"
                              ? "bg-white text-pink-600 hover:bg-gray-100"
                              : "bg-white text-black hover:bg-gray-100"
                      }`}
                      disabled={
                        buttonState === "subscribed" || buttonState === "current" || buttonState === "downgrade"
                      }
                      onClick={() => handleSubscribeClick(plan.name)}
                    >
                      {buttonState === "subscribed"
                        ? "SUBSCRIBED"
                        : buttonState === "current"
                          ? "SUBSCRIBED"
                          : buttonState === "downgrade"
                            ? plan.name === "Free"
                              ? "DOWNGRADE NOT AVAILABLE"
                              : "DOWNGRADE NOT AVAILABLE"
                            : currentSubscription === "Free"
                              ? "SUBSCRIBE"
                              : "UPGRADE"}
                    </Button>

                    <div className="text-center mt-3">
                      <p className="text-gray-300 text-xs">🕒 {plan.note}</p>
                    </div>
                  </div>
                </div>
              )
            })}
          </div>
        </div>
      </main>

      {/* Dialogs */}
      <Dialog open={dialog.isOpen} onOpenChange={closeDialog}>
        <DialogContent className="bg-[#1a0a24] border-[#3a1a44] text-white max-w-md">
          {dialog.type === "subscribe" && (
            <>
              <DialogHeader>
                <DialogTitle className="text-2xl font-bold text-center bg-gradient-to-r from-pink-400 to-purple-500 bg-clip-text text-transparent">
                  Unlock your Love Story 💘
                </DialogTitle>
                <div className="text-gray-300 text-center mt-4">
                  <p className="mb-4">Subscribe to the {dialog.targetPlan} plan and start enjoying:</p>
                  <ul className="mt-4 space-y-2 text-left">
                    {getUpgradeFeatures(dialog.targetPlan!).map((feature, index) => (
                      <li key={index}>• {feature}</li>
                    ))}
                  </ul>
                </div>
              </DialogHeader>
              <div className="flex gap-4 mt-6">
                <Button
                  onClick={handleConfirmSubscription}
                  disabled={isProcessing}
                  className="flex-1 bg-pink-500 hover:bg-pink-600"
                >
                  {isProcessing ? "Processing..." : "Subscribe Now"}
                </Button>
                <Button onClick={closeDialog} variant="outline" className="flex-1 bg-transparent">
                  Cancel
                </Button>
              </div>
              <div className="mt-4 p-3 bg-[#2a1a34] rounded-lg border border-[#3a1a44]">
                <p className="text-gray-400 text-sm flex items-start">
                  <span className="mr-2">💡</span>
                  <span>Note: Plan changes will take effect immediately. No credit or refund will be applied.</span>
                </p>
              </div>
            </>
          )}

          {dialog.type === "upgrade" && (
            <>
              <DialogHeader>
                <DialogTitle className="text-xl font-bold text-center">Upgrade to {dialog.targetPlan} Plan</DialogTitle>
                <div className="text-gray-300 mt-4">
                  <p className="mb-4">You're currently subscribed to the {dialog.currentPlan} Plan.</p>
                  <p className="mb-4">Switching to the {dialog.targetPlan} Plan unlocks:</p>
                  <ul className="mt-4 space-y-2 mb-4">
                    {getUpgradeFeatures(dialog.targetPlan!).map((feature, index) => (
                      <li key={index}>• {feature}</li>
                    ))}
                  </ul>
                  <p><strong>Note:</strong> Upgrading will replace your current plan immediately. No credit or refund will be applied.</p>
                </div>
              </DialogHeader>
              <div className="flex gap-4 mt-6">
                <Button
                  onClick={handleConfirmSubscription}
                  disabled={isProcessing}
                  className="flex-1 bg-pink-500 hover:bg-pink-600"
                >
                  {isProcessing ? "Processing..." : `Upgrade Now`}
                </Button>
                <Button onClick={closeDialog} variant="outline" className="flex-1 bg-transparent">
                  Cancel
                </Button>
              </div>
              <div className="mt-4 p-3 bg-[#2a1a34] rounded-lg border border-[#3a1a44]">
                <p className="text-gray-400 text-sm flex items-start">
                  <span className="mr-2">💡</span>
                  <span>Note: You won't receive a refund for unused time in your current plan.</span>
                </p>
              </div>
            </>
          )}

          {dialog.type === "downgrade" && (
            <>
              <DialogHeader>
                <DialogTitle className="text-xl font-bold text-center">Current Plan: {dialog.currentPlan}</DialogTitle>
                <DialogDescription className="text-gray-300 text-center mt-4">
                  Downgrades are not supported at this time.
                  <br />
                  Please wait until your current plan expires.
                </DialogDescription>
              </DialogHeader>
              <div className="flex justify-center mt-6">
                <Button onClick={closeDialog} className="bg-pink-500 hover:bg-pink-600">
                  OK
                </Button>
              </div>
              <div className="mt-4 p-3 bg-[#2a1a34] rounded-lg border border-[#3a1a44]">
                <p className="text-gray-400 text-sm flex items-start">
                  <span className="mr-2">💡</span>
                  <span>Note: Switching to a lower plan is not available while your current plan is active.</span>
                </p>
              </div>
            </>
          )}

          {dialog.type === "already-subscribed" && (
            <>
              <DialogHeader>
                <DialogTitle className="text-xl font-bold text-center">Already Subscribed</DialogTitle>
                <DialogDescription className="text-gray-300 text-center mt-4">
                  You are already subscribed to this plan.
                </DialogDescription>
              </DialogHeader>
              <div className="flex justify-center mt-6">
                <Button onClick={closeDialog} className="bg-pink-500 hover:bg-pink-600">
                  OK
                </Button>
              </div>
              <div className="mt-4 p-3 bg-[#2a1a34] rounded-lg border border-[#3a1a44]">
                <p className="text-gray-400 text-sm flex items-start">
                  <span className="mr-2">💡</span>
                  <span>Note: You are on this plan already. No further action is needed.</span>
                </p>
              </div>
            </>
          )}

          {dialog.type === "success" && (
            <>
              <DialogHeader>
                <DialogTitle className="text-2xl font-bold text-center text-green-400">
                  🎉 Subscription Successful!
                </DialogTitle>
                <DialogDescription className="text-gray-300 text-center mt-4">
                  You're now enjoying the {dialog.targetPlan} plan.
                  <br />
                  Your benefits have been activated. 💖
                </DialogDescription>
              </DialogHeader>
              <div className="flex justify-center mt-6">
                <Button onClick={closeDialog} className="bg-green-500 hover:bg-green-600">
                  Awesome!
                </Button>
              </div>
              <div className="mt-4 p-3 bg-[#2a1a34] rounded-lg border border-[#3a1a44]">
                <p className="text-gray-400 text-sm flex items-start">
                  <span className="mr-2">💡</span>
                  <span>Note: Your new plan is now active. Previous plan benefits are no longer in effect.</span>
                </p>
              </div>
            </>
          )}

          {dialog.type === "error" && (
            <>
              <DialogHeader>
                <DialogTitle className="text-xl font-bold text-center text-red-400">⚠️ Payment Failed</DialogTitle>
                <DialogDescription className="text-gray-300 text-center mt-4">
                  Something went wrong with your transaction.
                  <br />
                  Please try again or contact support if the issue persists.
                </DialogDescription>
              </DialogHeader>
              <div className="flex gap-4 mt-6">
                <Button onClick={handleConfirmSubscription} className="flex-1 bg-pink-500 hover:bg-pink-600">
                  Try Again
                </Button>
                <Button onClick={closeDialog} variant="outline" className="flex-1">
                  Cancel
                </Button>
              </div>
              <div className="mt-4 p-3 bg-[#2a1a34] rounded-lg border border-[#3a1a44]">
                <p className="text-gray-400 text-sm flex items-start">
                  <span className="mr-2">💡</span>
                  <span>Note: Your current plan has not changed.</span>
                </p>
              </div>
            </>
          )}
        </DialogContent>
      </Dialog>

      {/* 移动端底部导航栏 */}
      <div className="md:hidden">
        <MobileBottomNav />
      </div>
    </div>
  )
}
