'use client';

import { useAgeVerification } from '@/hooks/useAgeVerification';
import AgeVerificationModal from '@/components/AgeVerificationModal';

interface AgeVerificationWrapperProps {
  children: React.ReactNode;
}

export function AgeVerificationWrapper({ children }: AgeVerificationWrapperProps) {
  const { isVerified, showModal, confirmAge, denyAge } = useAgeVerification();

  return (
    <>
      {children}
      <AgeVerificationModal
        isOpen={showModal}
        onConfirm={confirmAge}
        onDeny={denyAge}
      />
    </>
  );
}

export default AgeVerificationWrapper;