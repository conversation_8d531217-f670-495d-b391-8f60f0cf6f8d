// 最近聊天相关API

import { mainApiClient } from './client';
import { 
  RecentChatItem, 
  RecentChatsResponse,
  GetRecentChatsQuery,
  ApiResponse 
} from './types';
import { API_PATHS, STORAGE_KEYS } from './config';

export class RecentChatsAPI {
  /**
   * 获取存储的token
   */
  private static getToken(): string | null {
    if (typeof window === 'undefined') return null;
    return localStorage.getItem(STORAGE_KEYS.TOKEN);
  }

  /**
   * 获取最近聊天列表 (简化统一接口)
   */
  static async getRecentChats(options: GetRecentChatsQuery = {}): Promise<RecentChatItem[]> {
    const { limit = 10 } = options;

    try {
      console.log('🔍 开始获取最近聊天列表, limit:', limit);
      
      const response = await mainApiClient.get<RecentChatsResponse>(
        `${API_PATHS.CHAT.RECENT_CHATS}?limit=${limit}`
      );

      console.log('🔍 最近聊天API响应:', response);

      if (!response.success) {
        throw new Error(response.error || '获取最近聊天列表失败');
      }

      return response.data?.chats || [];
    } catch (error) {
      console.error('获取最近聊天列表失败:', error);
      
      // 如果API失败，回退到localStorage（临时兼容）
      console.warn('🔄 回退到localStorage读取最近聊天');
      return this.getRecentChatsFromLocal(limit);
    }
  }

  /**
   * 从本地存储获取最近聊天（回退方案）
   */
  private static getRecentChatsFromLocal(limit: number): RecentChatItem[] {
    try {
      if (typeof window === 'undefined') return [];
      
      const chatHistory = localStorage.getItem(STORAGE_KEYS.RECENT_CHATS);
      if (!chatHistory) return [];

      const chats = JSON.parse(chatHistory);
      return chats.slice(0, limit).map((chat: any) => ({
        id: chat.id?.toString() || '',
        name: chat.name || '',
        occupation: chat.occupation || 'Unknown',
        imageSrc: chat.imageSrc || '',
        avatarSrc: chat.imageSrc || '', // 临时使用同一图片
        gender: chat.gender || 'male',
        lastChatAt: chat.timestamp || new Date().toISOString(),
        lastMessage: chat.lastMessage || '',
        unreadCount: chat.unreadCount || (chat.unread ? 1 : 0), // 支持新旧字段
        isPrivate: chat.isPrivate || false,
      }));
    } catch (error) {
      console.error('从localStorage读取最近聊天失败:', error);
      return [];
    }
  }

  /**
   * 添加最近聊天记录
   */
  static async addRecentChat(characterId: string): Promise<ApiResponse> {
    try {
      const response = await mainApiClient.post<ApiResponse>(
        API_PATHS.CHAT.ADD_RECENT_CHAT,
        { characterId }
      );

      return response;
    } catch (error) {
      console.error('添加最近聊天记录失败:', error);
      
      // 如果API失败，保存到localStorage（临时兼容）
      this.saveRecentChatToLocal(characterId);
      
      return { success: false, error: 'API调用失败，已保存到本地' };
    }
  }

  /**
   * 保存最近聊天到本地存储（回退方案）
   */
  private static saveRecentChatToLocal(characterId: string): void {
    try {
      if (typeof window === 'undefined') return;

      const chatHistory = JSON.parse(
        localStorage.getItem(STORAGE_KEYS.RECENT_CHATS) || '[]'
      );

      const newChat = {
        id: characterId,
        timestamp: new Date().toISOString()
      };

      // 检查是否已存在该角色的聊天记录
      const existingIndex = chatHistory.findIndex(
        (chat: any) => chat.id === characterId
      );
      
      if (existingIndex !== -1) {
        chatHistory.splice(existingIndex, 1);
      }

      chatHistory.unshift(newChat);
      const updatedHistory = chatHistory.slice(0, 10); // 限制为10条记录
      
      localStorage.setItem(STORAGE_KEYS.RECENT_CHATS, JSON.stringify(updatedHistory));
      console.log('💾 最近聊天记录已保存到本地');
    } catch (error) {
      console.error('保存最近聊天记录到本地失败:', error);
    }
  }

  /**
   * 删除最近聊天记录
   */
  static async removeRecentChat(characterId: string): Promise<ApiResponse> {
    try {
      const response = await mainApiClient.delete<ApiResponse>(
        `${API_PATHS.CHAT.RECENT_CHATS}/${characterId}`
      );

      return response;
    } catch (error) {
      console.error('删除最近聊天记录失败:', error);
      throw error;
    }
  }

  /**
   * 清空所有最近聊天记录
   */
  static async clearRecentChats(): Promise<ApiResponse> {
    try {
      const response = await mainApiClient.delete<ApiResponse>(
        API_PATHS.CHAT.RECENT_CHATS
      );

      // 同时清除本地存储
      if (typeof window !== 'undefined') {
        localStorage.removeItem(STORAGE_KEYS.RECENT_CHATS);
      }

      return response;
    } catch (error) {
      console.error('清空最近聊天记录失败:', error);
      throw error;
    }
  }

  /**
   * 标记聊天为已读
   */
  static async markChatAsRead(characterId: string): Promise<ApiResponse> {
    try {
      const response = await mainApiClient.patch<ApiResponse>(
        `${API_PATHS.CHAT.RECENT_CHATS}/${characterId}/read`
      );

      return response;
    } catch (error) {
      console.error('标记聊天已读失败:', error);
      throw error;
    }
  }

  /**
   * 获取未读聊天数量
   */
  static async getUnreadCount(): Promise<number> {
    try {
      const response = await mainApiClient.get<{ success: boolean; data: { unreadCount: number } }>(
        `${API_PATHS.CHAT.UNREAD_COUNT}`
      );

      if (!response.success) {
        throw new Error('获取未读数量失败');
      }

      return response.data?.unreadCount || 0;
    } catch (error) {
      console.error('获取未读聊天数量失败:', error);
      return 0;
    }
  }
}

export default RecentChatsAPI;