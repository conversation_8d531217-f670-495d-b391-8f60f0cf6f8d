"use client";

import { useState } from "react";
import Image from "next/image";
import { Download, X } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Dialog, DialogContent } from "@/components/ui/dialog";
import { getCharacterImageUrl } from "@/lib/image-utils";

interface GenerationResultsProps {
  images: string[];
  isGenerating: boolean;
  onClear: () => void;
  className?: string;
}

export default function GenerationResults({
  images,
  isGenerating,
  onClear,
  className = "",
}: GenerationResultsProps) {
  const [selectedImage, setSelectedImage] = useState<string | null>(null);

  const handleDownload = (imageUrl: string, index: number) => {
    // 创建一个临时的a标签来触发下载
    const link = document.createElement('a');
    link.href = imageUrl;
    link.download = `generated-image-${index + 1}.png`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const handleDownloadAll = () => {
    images.forEach((image, index) => {
      setTimeout(() => {
        handleDownload(image, index);
      }, index * 100); // 延迟下载避免浏览器阻止
    });
  };

  if (isGenerating) {
    return (
      <div className={`space-y-4 ${className}`}>
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold">Generating Images...</h3>
        </div>
        <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
          {Array(3).fill(0).map((_, i) => (
            <div
              key={i}
              className="aspect-[3/4] rounded-lg bg-[#1a0a24] border border-[#3a1a44] animate-pulse flex items-center justify-center"
            >
              <div className="text-gray-400">Loading...</div>
            </div>
          ))}
        </div>
        <div className="text-center text-gray-400 text-sm">
          This may take a few moments...
        </div>
      </div>
    );
  }

  if (images.length === 0) {
    return null;
  }

  return (
    <>
      <div className={`space-y-4 ${className}`}>
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold">Generated Images</h3>
          <div className="flex items-center space-x-2">
            <Button
              size="sm"
              variant="outline"
              onClick={handleDownloadAll}
              className="bg-[#1a0a24] border-[#3a1a44]"
            >
              <Download className="h-4 w-4 mr-2" />
              Download All
            </Button>
            <Button
              size="sm"
              variant="outline"
              onClick={onClear}
              className="bg-[#1a0a24] border-[#3a1a44] hover:border-red-400"
            >
              <X className="h-4 w-4 mr-2" />
              Clear
            </Button>
          </div>
        </div>
        
        <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
          {images.map((image, index) => (
            <div
              key={index}
              className="group relative aspect-[3/4] rounded-lg overflow-hidden bg-[#1a0a24] border border-[#3a1a44] cursor-pointer hover:border-pink-400 transition-colors"
              onClick={() => setSelectedImage(image)}
            >
              <Image
                src={getCharacterImageUrl(image)}
                alt={`Generated image ${index + 1}`}
                fill
                className="object-cover group-hover:scale-105 transition-transform"
              />
              
              {/* 悬停时显示的操作按钮 */}
              <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center">
                <Button
                  size="sm"
                  onClick={(e) => {
                    e.stopPropagation();
                    handleDownload(image, index);
                  }}
                  className="bg-pink-500 hover:bg-pink-600"
                >
                  <Download className="h-4 w-4 mr-2" />
                  Download
                </Button>
              </div>
              
              {/* 图片编号 */}
              <div className="absolute top-2 right-2 bg-black/70 text-white text-xs px-2 py-1 rounded">
                {index + 1}
              </div>
            </div>
          ))}
        </div>
        
        <div className="text-center text-gray-400 text-sm">
          Click on any image to view in full size
        </div>
      </div>

      {/* 全屏查看对话框 */}
      <Dialog open={!!selectedImage} onOpenChange={() => setSelectedImage(null)}>
        <DialogContent className="max-w-4xl bg-[#1a0a24] border-[#3a1a44]">
          {selectedImage && (
            <div className="relative aspect-[3/4] max-h-[80vh]">
              <Image
                src={getCharacterImageUrl(selectedImage)}
                alt="Generated image full view"
                fill
                className="object-contain"
              />
              <Button
                className="absolute top-4 right-4 bg-black/70 hover:bg-black/90"
                size="sm"
                onClick={() => {
                  const index = images.indexOf(selectedImage);
                  handleDownload(selectedImage, index);
                }}
              >
                <Download className="h-4 w-4 mr-2" />
                Download
              </Button>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </>
  );
}