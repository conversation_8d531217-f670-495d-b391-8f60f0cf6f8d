# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

这是一个 Next.js 15 AI 伴侣应用的前端项目，使用 TypeScript 和 Tailwind CSS 构建。项目名为"LumiLove"，提供虚拟 AI 伴侣的情感陪伴体验。

## Tech Stack

- **Framework**: Next.js 15 with App Router
- **Language**: TypeScript
- **Styling**: Tailwind CSS + shadcn/ui components
- **Package Manager**: pnpm
- **UI Library**: Radix UI components via shadcn/ui
- **State Management**: React hooks + localStorage
- **Deployment**: AWS Amplify

## Development Commands

```bash
# Install dependencies
pnpm install

# Development server
pnpm dev

# Build for production
pnpm build

# Start production server
pnpm start

# Lint code
pnpm lint
```

## Architecture

### Directory Structure

- `app/` - Next.js App Router pages and layouts
- `components/` - Reusable React components
- `components/ui/` - shadcn/ui components
- `lib/` - Utility functions and API clients
- `hooks/` - Custom React hooks
- `public/` - Static assets (character images, avatars)

### Key Files

- `app/layout.tsx` - Root layout with theme provider
- `lib/api.ts` - API client functions for backend communication
- `lib/api/subscription.ts` - Subscription/payment API integration
- `lib/api/labels.ts` - Character labels/tags API integration
- `lib/config.ts` - API configuration and endpoints
- `hooks/useSubscription.ts` - Subscription-related React hooks
- `hooks/useLabels.ts` - Character labels/tags React hooks
- `app/api/subscribe-proxy/route.ts` - API proxy for subscription 302 redirects
- `next.config.js` - API proxy configuration for backend

### API Integration

- Backend API: `https://api.loomelove.ai` (主要 API 服务)
- RAG 服务: `https://chat.loomelove.ai` (for streaming chat)
- API proxying configured in `next.config.js`
- Authentication via Bearer tokens stored in localStorage
- Subscription API: `/plan/list` (套餐列表), `/api/plan/subscribe` (订阅服务)
- Character Labels API: `/characters/label/list` (角色标签列表)
- 302 重定向拦截: 通过 Next.js API 代理处理支付页面跳转

### Core Features

- **Auth 系统**: 登录/注册 (`/login`, `/register`)
- **角色创建**: 创建和选择 AI 伴侣 (`/create`, `/create-lover`)
- **聊天功能**: 实时流式对话 (`/chat/[id]`)
- **画廊功能**: 角色图片展示 (`/album`, `/sneaky`)
- **用户档案**: 个人资料管理 (`/profile`)
- **支付系统**: 订阅套餐管理 (`/payment`, `/premium`) - 真实 API 集成

### State Management

- User data stored in localStorage
- Chat history managed via backend API
- Character stats tracked locally
- Theme managed via next-themes

### Styling System

- Dark theme as default (`bg-[#0e0314]`)
- CSS 变量用于主题切换
- Responsive design with Tailwind breakpoints
- Custom animations via tailwindcss-animate

## 🚨 开发流程规则

### 部署规则
- **禁止自动部署**: Claude 不得执行任何部署命令（如 deploy.sh、npm run deploy 等）
- **仅通知部署**: 修改完成后，只通知用户进行手动部署
- **等待确认**: 每个功能点都必须等待用户测试完成后再进行下一步开发

### 测试流程
1. Claude 完成代码修改
2. 通知用户进行手动部署
3. 用户测试功能
4. 用户确认功能正常后，Claude 才能继续下一个功能的开发

## Development Notes

### API Integration Pattern

```typescript
// Standard API call pattern with auth headers
const response = await fetch(`${API_BASE_URL}/endpoint`, {
  headers: {
    'Authorization': `Bearer ${accessToken}`,
    'HTTP-Referer': 'https://main.d3m01u43jjmlec.amplifyapp.com/',
    'X-Title': 'Lumilove',
  },
});

// Subscription API with 302 redirect interception
const response = await fetch('/api/subscribe-proxy', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${token}`,
  },
  body: JSON.stringify({ planId }),
});

// 处理重定向和支付窗口
if (result.redirect && result.paymentUrl) {
  const paymentWindow = window.open(result.paymentUrl, '_blank');
  // 监控支付状态...
}
```

### Streaming Chat Implementation

- Uses Server-Sent Events for real-time chat
- Implements abort controller for request cancellation
- Session management with user_id and character_id

### Component Conventions

- Use shadcn/ui components as base
- TypeScript interfaces for all props
- Responsive design patterns
- Consistent color scheme with CSS variables

### Deployment

- AWS Amplify with `amplify.yml` configuration
- Automatic builds on git push to main branch
- PNPM used for faster installs and builds

## Page Structure and Functionality

### Core Pages

#### Home Page (`/`)

- **主页面**: 角色发现和选择中心
- **功能**:
  - 用户认证状态管理
  - 角色浏览（分男女两个分类）
  - 动态标签过滤系统（完全使用后端 API 数据）
  - 最近聊天历史显示
  - 趋势角色排行榜
- **特点**: 实时标签 API 集成，支持动态过滤和错误回退机制

#### Authentication Pages

- **登录页** (`/login`): 渐变 UI 设计，包含密码显示切换和记住我功能
- **注册页** (`/register`): 用户账户创建流程

#### Chat System (`/chat/`)

- **聊天首页** (`/chat`): 自动重定向到最近聊天
- **具体聊天页** (`/chat/[id]`):
  - 实时流式对话功能
  - 语音通话模拟
  - 图片生成请求（hardcoded responses）
  - 聊天历史管理（支持清除）
  - 角色资料侧边栏
  - 响应式设计（移动端适配）

#### Character Creation (`/create`)

- **AI 图片生成页面**:
  - 角色选择或图片上传
  - 场景、服装、姿势、角度选择系统
  - 随机生成功能
  - 图片数量选择（1/4/9，Premium 功能）
  - 下载生成图片功能

#### Gallery Pages

- **专辑页** (`/album`): 角色图片展示
- **Sneaky 页** (`/sneaky`):
  - 付费图片包浏览
  - 高级过滤系统（Style, Intimacy, Tags）
  - 分页和排序功能
  - 预览轮播效果
  - Premium 内容锁定

#### Payment & Premium (`/payment`, `/premium`)

- **支付页面**:
  - 4 档订阅计划（Free/Lite/Basic/Premium）
  - 真实 API 数据动态更新价格和功能
  - API 数据与前端 UI 配置智能合并
  - 订阅状态管理和用户反馈
  - 升级/降级逻辑处理
  - 302 重定向支付流程（新窗口支付页面）
  - 支付状态监控和确认机制

#### User Management

- **用户资料** (`/profile`): 个人信息管理
- **资料编辑** (`/profile/edit`): 用户信息更新

#### Creator Pages

- **创建者页面** (`/creator/[creatorId]`): 角色创建者展示
- **创建伴侣** (`/create-lover`): 角色定制流程

#### Support Pages

- **帮助页** (`/help`): 用户支持
- **条款** (`/terms`): 服务条款
- **隐私政策** (`/privacy`): 隐私保护说明

### Technical Features

#### State Management

- LocalStorage 用于用户数据和聊天历史
- React hooks 进行状态管理
- 角色数据的动态合并（默认+用户创建）

#### UI/UX 特色

- 深色主题为主（`bg-[#0e0314]`）
- 渐变动画效果
- 流式聊天的 thinking 状态显示
- 移动端响应式 sidebar
- 图片悬停预览效果

#### API Integration Patterns

- 流式聊天实现（SSE）
- 认证 token 管理
- 错误处理和重试机制
- 文件上传处理
- 302 重定向拦截模式（订阅支付）
- 支付窗口管理和状态监控
- API 数据转换和前端配置合并

### Development Notes

- 使用 shadcn/ui 组件系统
- 严格的 TypeScript 类型定义
- 组件化设计模式
- 图片优化（Next.js Image 组件）
