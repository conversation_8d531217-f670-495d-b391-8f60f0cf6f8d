"use client";

interface QuickRepliesProps {
  replyType: string;
  onQuickReply: (reply: string) => void;
  quickReplies: {
    text: string[];
    picture: string[];
    voice: string[];
  };
}

export default function QuickReplies({
  replyType,
  onQuickReply,
  quickReplies,
}: QuickRepliesProps) {
  return (
    <div className="px-6 py-3 overflow-x-auto whitespace-nowrap">
      <div className="flex space-x-3">
        {quickReplies[replyType as keyof typeof quickReplies].map(
          (reply, index) => (
            <button
              key={index}
              className="min-h-[44px] px-3 py-2 sm:px-4 sm:py-2 bg-[#1a0a24] text-sm sm:text-base rounded-full hover:bg-[#2a1a34] transition-colors"
              onClick={() => onQuickReply(reply)}
            >
              {reply}
            </button>
          )
        )}
      </div>
    </div>
  );
}