/**
 * 图包过滤器组件
 * 用于图包的分类、筛选和排序
 */

'use client';

import { Filter, ChevronDown } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

interface PackageFiltersProps {
  activeStyle: string;
  activeIntimacy: string;
  activeTag: string;
  activeSort: string;
  onStyleChange: (style: string) => void;
  onIntimacyChange: (intimacy: string) => void;
  onTagChange: (tag: string) => void;
  onSortChange: (sort: string) => void;
}

export default function PackageFilters({
  activeStyle,
  activeIntimacy,
  activeTag,
  activeSort,
  onStyleChange,
  onIntimacyChange,
  onTagChange,
  onSortChange,
}: PackageFiltersProps) {
  // TODO: 这些选项后续可以从API获取
  const styleOptions = [
    'All',
    'Bedroom',
    'Cosplay',
    'Outdoor',
    'Shower',
    'Fantasy',
    'Office',
    'Beach',
    'School',
  ];

  const intimacyOptions = [
    'All',
    'Soft',
    'Sexy',
    'NSFW',
    'VIP',
  ];

  const tagOptions = [
    'All',
    'HOT 🔥',
    'Limited ⏰',
    'New 🆕',
    'Popular',
    'Premium',
  ];

  const sortOptions = [
    'Most Popular',
    'Newest',
    'Price ↓',
    'Price ↑',
    'Most Liked',
    'Recently Added',
  ];

  return (
    <div className="space-y-4">
      {/* Style Filter */}
      <div>
        <h3 className="text-white font-medium mb-2 flex items-center">
          <Filter className="h-4 w-4 mr-2" />
          Style
        </h3>
        <div className="flex flex-wrap gap-2">
          {styleOptions.map((style) => (
            <Badge
              key={style}
              variant={style === activeStyle ? 'default' : 'outline'}
              className={`cursor-pointer px-3 py-1 text-sm ${
                style === activeStyle
                  ? 'bg-pink-500 hover:bg-pink-600 text-white'
                  : 'bg-[#1a0a24] hover:bg-[#2a1a34] text-gray-300 border-[#3a1a44]'
              }`}
              onClick={() => onStyleChange(style)}
            >
              {style}
            </Badge>
          ))}
        </div>
      </div>

      {/* Intimacy Filter */}
      <div>
        <h3 className="text-white font-medium mb-2">Intimacy Level</h3>
        <div className="flex flex-wrap gap-2">
          {intimacyOptions.map((intimacy) => (
            <Badge
              key={intimacy}
              variant={intimacy === activeIntimacy ? 'default' : 'outline'}
              className={`cursor-pointer px-3 py-1 text-sm ${
                intimacy === activeIntimacy
                  ? 'bg-pink-500 hover:bg-pink-600 text-white'
                  : 'bg-[#1a0a24] hover:bg-[#2a1a34] text-gray-300 border-[#3a1a44]'
              }`}
              onClick={() => onIntimacyChange(intimacy)}
            >
              {intimacy}
              {intimacy === 'VIP' && ' 👑'}
              {intimacy === 'NSFW' && ' 🔞'}
            </Badge>
          ))}
        </div>
      </div>

      {/* Tags Filter */}
      <div>
        <h3 className="text-white font-medium mb-2">Tags</h3>
        <div className="flex flex-wrap gap-2">
          {tagOptions.map((tag) => (
            <Badge
              key={tag}
              variant={tag === activeTag ? 'default' : 'outline'}
              className={`cursor-pointer px-3 py-1 text-sm ${
                tag === activeTag
                  ? 'bg-pink-500 hover:bg-pink-600 text-white'
                  : 'bg-[#1a0a24] hover:bg-[#2a1a34] text-gray-300 border-[#3a1a44]'
              }`}
              onClick={() => onTagChange(tag)}
            >
              {tag}
            </Badge>
          ))}
        </div>
      </div>

      {/* Sort Dropdown */}
      <div>
        <h3 className="text-white font-medium mb-2">Sort By</h3>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant="outline"
              className="w-full justify-between bg-[#1a0a24] border-[#3a1a44] text-white hover:bg-[#2a1a34]"
            >
              {activeSort}
              <ChevronDown className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent className="w-56 bg-[#1a0a24] border-[#3a1a44] text-white">
            {sortOptions.map((sort) => (
              <DropdownMenuItem
                key={sort}
                className={`cursor-pointer hover:bg-[#2a1a34] ${
                  sort === activeSort ? 'bg-[#2a1a34] text-pink-400' : ''
                }`}
                onClick={() => onSortChange(sort)}
              >
                {sort}
              </DropdownMenuItem>
            ))}
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      {/* Filter Summary */}
      <div className="pt-2 border-t border-[#3a1a44]">
        <div className="flex flex-wrap gap-1 text-xs text-gray-400">
          {activeStyle !== 'All' && (
            <span className="bg-[#2a1a34] px-2 py-1 rounded">
              Style: {activeStyle}
            </span>
          )}
          {activeIntimacy !== 'All' && (
            <span className="bg-[#2a1a34] px-2 py-1 rounded">
              Level: {activeIntimacy}
            </span>
          )}
          {activeTag !== 'All' && (
            <span className="bg-[#2a1a34] px-2 py-1 rounded">
              Tag: {activeTag}
            </span>
          )}
        </div>
      </div>
    </div>
  );
}