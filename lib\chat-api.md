# Chat API 接口文档 - 后端开发参考

## 概述

本文档定义了聊天系统相关的 API 接口规范，包括聊天历史管理、最近聊天列表、消息发送等功能。供后端开发人员实现相应的数据库操作和 API 端点。

## 数据模型

### ChatHistory 实体

聊天历史数据库实体定义。

```typescript
interface ChatHistory {
  id: number;                      // 主键，自增ID
  userId: number;                  // 用户ID，外键
  characterId: number;             // 角色ID，外键
  message: string;                 // 用户发送的消息内容，TEXT类型
  response: string;                // AI回复内容，TEXT类型
  msgType: 'text' | 'image' | 'voice'; // 消息类型枚举
  createdAt: Date;                 // 创建时间，自动生成
  updatedAt: Date;                 // 更新时间，自动更新
}
```

### RecentChat 实体

最近聊天数据库实体定义。

```typescript
interface RecentChat {
  id: number;                      // 主键，自增ID
  userId: number;                  // 用户ID，外键
  characterId: string;             // 角色ID，外键
  lastMessage?: string;            // 最后一条消息，可选
  lastChatAt: Date;                // 最后聊天时间
  unreadCount: number;             // 未读消息数量，默认0
  isPrivate: boolean;              // 是否私密聊天，默认false
  isPinned: boolean;               // 是否置顶，默认false
  createdAt: Date;                 // 创建时间
  updatedAt: Date;                 // 更新时间
}
```

## API 端点

### 1. 获取聊天历史

**接口路径:** `GET /api/chat/history/{characterId}`

**描述:** 获取用户与指定角色的聊天历史记录

**请求头:**
```
Authorization: Bearer {token}
```

**路径参数:**
- `characterId` (number): 角色 ID

**请求参数 (Query Parameters):**

```typescript
interface GetChatHistoryQuery {
  page?: number;                   // 页码，从1开始，默认1
  pageSize?: number;               // 每页数量，默认50，最大100
  msgType?: 'text' | 'image' | 'voice'; // 消息类型筛选，可选
  startDate?: string;              // 开始时间 (ISO格式)
  endDate?: string;                // 结束时间 (ISO格式)
}
```

**请求示例:**
```bash
GET /api/chat/history/2?page=1&pageSize=20&msgType=text
```

**响应结果:**

```typescript
interface GetChatHistoryResponse {
  success: boolean;
  histories: ChatHistoryItem[];    // 聊天历史列表
  totalCount: number;              // 总记录数
  pagination?: {
    totalPages: number;            // 总页数
    currentPage: number;           // 当前页码
    pageSize: number;              // 每页数量
    hasNext: boolean;              // 是否有下一页
    hasPrev: boolean;              // 是否有上一页
  };
  error?: string;
}

interface ChatHistoryItem {
  id: number;
  userId: number;
  characterId: number;
  message: string;
  response: string;
  msgType: 'text' | 'image' | 'voice';
  createdAt: string;               // ISO格式时间
}
```

**响应示例:**
```json
{
  "success": true,
  "histories": [
    {
      "id": 123,
      "userId": 456,
      "characterId": 2,
      "message": "Hello there!",
      "response": "Hi! How are you doing today?",
      "msgType": "text",
      "createdAt": "2024-06-26T14:20:00Z"
    },
    {
      "id": 124,
      "userId": 456,
      "characterId": 2,
      "message": "I'm doing great, thanks for asking!",
      "response": "That's wonderful to hear! What would you like to talk about?",
      "msgType": "text",
      "createdAt": "2024-06-26T14:21:30Z"
    }
  ],
  "totalCount": 45,
  "pagination": {
    "totalPages": 3,
    "currentPage": 1,
    "pageSize": 20,
    "hasNext": true,
    "hasPrev": false
  }
}
```

### 2. 清除聊天历史

**接口路径:** `DELETE /api/chat/history/{characterId}`

**描述:** 清除用户与指定角色的所有聊天历史记录

**请求头:**
```
Authorization: Bearer {token}
```

**路径参数:**
- `characterId` (number): 角色 ID

**请求示例:**
```bash
DELETE /api/chat/history/2
```

**响应结果:**

```typescript
interface ClearChatHistoryResponse {
  success: boolean;
  message?: string;
  deletedCount?: number;           // 删除的记录数量
  error?: string;
}
```

**响应示例:**
```json
{
  "success": true,
  "message": "聊天历史已成功清除",
  "deletedCount": 45
}
```

### 3. 获取最近聊天列表 (统一接口)

**接口路径:** `GET /api/chat/recent-chats`

**描述:** 获取用户最近聊天的角色列表，包含基础角色信息和聊天状态。此接口合并了原本的 `/api/characters/recent-chats` 功能，同时支持首页展示和聊天页面的需求。

**请求头:**
```
Authorization: Bearer {token}
```

**请求参数:**

```typescript
interface GetRecentChatsQuery {
  limit?: number;                  // 返回数量限制，默认10，最大50
}
```

**请求示例:**
```bash
# 获取最近聊天列表
GET /api/chat/recent-chats?limit=10
```

**响应结果:**

```typescript
interface RecentChatsResponse {
  success: boolean;
  data?: {
    chats: RecentChatItem[];
    totalCount: number;
    totalUnreadCount: number;      // 总未读消息数
  };
  error?: string;
}

interface RecentChatItem {
  // 基础角色信息
  id: string;                      // 聊天/角色ID
  name: string;                    // 角色名称
  occupation?: string;             // 职业
  age?: number;                    // 年龄
  
  // 图片信息
  imageSrc: string;               // 主图片
  avatarSrc: string;              // 头像图片
  
  // 基础属性
  gender: 'male' | 'female';      // 性别
  
  // 聊天状态信息
  lastChatAt: string;             // 最后聊天时间 (ISO格式)
  lastMessage?: string;           // 最后一条消息内容
  unreadCount: number;            // 未读消息数量，默认0
  isPrivate?: boolean;            // 是否为私密聊天
  isPinned?: boolean;             // 是否置顶
}
```

**注意:** 角色的详细信息（如描述、标签、统计数据、创作者信息等）请通过 `GET /api/characters/{id}` 接口单独获取。

**响应示例:**
```json
{
  "success": true,
  "data": {
    "chats": [
      {
        "id": "2",
        "name": "Alexander",
        "occupation": "Businessman",
        "age": 28,
        "imageSrc": "/male/male_02.png",
        "avatarSrc": "/avatar/alexander_avatar.png",
        "gender": "male",
        "lastChatAt": "2024-06-26T14:20:00Z",
        "lastMessage": "I've been thinking about you...",
        "unreadCount": 2,
        "isPrivate": false,
        "isPinned": true
      },
      {
        "id": "4",
        "name": "Rhonda", 
        "occupation": "Entrepreneur",
        "age": 32,
        "imageSrc": "/female/female01.png",
        "avatarSrc": "/avatar/female_01_avatar.png",
        "gender": "female",
        "lastChatAt": "2024-06-25T18:45:00Z",
        "lastMessage": "Want to talk business?",
        "unreadCount": 0,
        "isPrivate": false,
        "isPinned": false
      }
    ],
    "totalCount": 8,
    "totalUnreadCount": 5
  }
}
```

### 4. 添加最近聊天记录

**接口路径:** `POST /api/chat/recent-chats`

**描述:** 添加或更新最近聊天记录

**请求头:**
```
Authorization: Bearer {token}
Content-Type: application/json
```

**请求体:**

```typescript
interface AddRecentChatRequest {
  characterId: string;             // 角色ID
  lastMessage?: string;            // 最后消息内容，可选
}
```

**请求示例:**
```bash
POST /api/chat/recent-chats
Content-Type: application/json

{
  "characterId": "2",
  "lastMessage": "Hello there!"
}
```

**响应示例:**
```json
{
  "success": true,
  "message": "最近聊天记录已更新"
}
```

### 5. 删除最近聊天记录

**接口路径:** `DELETE /api/chat/recent-chats/{characterId}`

**描述:** 删除指定角色的最近聊天记录

**请求头:**
```
Authorization: Bearer {token}
```

**路径参数:**
- `characterId` (string): 角色 ID

**请求示例:**
```bash
DELETE /api/chat/recent-chats/2
```

**响应示例:**
```json
{
  "success": true,
  "message": "最近聊天记录已删除"
}
```

### 6. 清空所有最近聊天记录

**接口路径:** `DELETE /api/chat/recent-chats`

**描述:** 清空用户所有最近聊天记录

**请求头:**
```
Authorization: Bearer {token}
```

**请求示例:**
```bash
DELETE /api/chat/recent-chats
```

**响应示例:**
```json
{
  "success": true,
  "message": "所有最近聊天记录已清空",
  "deletedCount": 8
}
```

### 7. 标记聊天为已读

**接口路径:** `PATCH /api/chat/recent-chats/{characterId}/read`

**描述:** 标记指定角色的聊天为已读状态

**请求头:**
```
Authorization: Bearer {token}
```

**路径参数:**
- `characterId` (string): 角色 ID

**请求示例:**
```bash
PATCH /api/chat/recent-chats/2/read
```

**响应示例:**
```json
{
  "success": true,
  "message": "聊天已标记为已读"
}
```

### 8. 获取未读消息数量

**接口路径:** `GET /api/chat/unread-count`

**描述:** 获取用户总的未读聊天消息数量

**请求头:**
```
Authorization: Bearer {token}
```

**请求示例:**
```bash
GET /api/chat/unread-count
```

**响应结果:**

```typescript
interface UnreadCountResponse {
  success: boolean;
  data?: {
    unreadCount: number;           // 总未读消息数
    unreadChats: number;           // 有未读消息的聊天数
  };
  error?: string;
}
```

**响应示例:**
```json
{
  "success": true,
  "data": {
    "unreadCount": 12,
    "unreadChats": 3
  }
}
```

### 9. 置顶/取消置顶聊天

**接口路径:** `PATCH /api/chat/recent-chats/{characterId}/pin`

**描述:** 置顶或取消置顶指定聊天

**请求头:**
```
Authorization: Bearer {token}
Content-Type: application/json
```

**路径参数:**
- `characterId` (string): 角色 ID

**请求体:**

```typescript
interface PinChatRequest {
  isPinned: boolean;               // true为置顶，false为取消置顶
}
```

**请求示例:**
```bash
PATCH /api/chat/recent-chats/2/pin
Content-Type: application/json

{
  "isPinned": true
}
```

**响应示例:**
```json
{
  "success": true,
  "message": "聊天已置顶"
}
```

### 10. 设置聊天隐私状态

**接口路径:** `PATCH /api/chat/recent-chats/{characterId}/privacy`

**描述:** 设置指定聊天的隐私状态

**请求头:**
```
Authorization: Bearer {token}
Content-Type: application/json
```

**路径参数:**
- `characterId` (string): 角色 ID

**请求体:**

```typescript
interface SetPrivacyRequest {
  isPrivate: boolean;              // true为私密，false为普通
}
```

**请求示例:**
```bash
PATCH /api/chat/recent-chats/2/privacy
Content-Type: application/json

{
  "isPrivate": true
}
```

**响应示例:**
```json
{
  "success": true,
  "message": "聊天隐私状态已更新"
}
```

## 数据库表设计建议

### chat_histories 表

```sql
CREATE TABLE chat_histories (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  user_id BIGINT NOT NULL,
  character_id VARCHAR(50) NOT NULL,
  message TEXT NOT NULL,
  response TEXT NOT NULL,
  msg_type ENUM('text', 'image', 'voice') DEFAULT 'text',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  INDEX idx_user_character (user_id, character_id),
  INDEX idx_user_created (user_id, created_at),
  INDEX idx_character_created (character_id, created_at)
);
```

### recent_chats 表

```sql
CREATE TABLE recent_chats (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  user_id BIGINT NOT NULL,
  character_id VARCHAR(50) NOT NULL,
  last_message TEXT,
  last_chat_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  unread_count INT DEFAULT 0,
  is_private BOOLEAN DEFAULT FALSE,
  is_pinned BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  UNIQUE KEY uk_user_character (user_id, character_id),
  INDEX idx_user_last_chat (user_id, last_chat_at DESC),
  INDEX idx_user_pinned (user_id, is_pinned, last_chat_at DESC)
);
```

## 错误响应

所有API在发生错误时返回统一格式：

```typescript
interface ErrorResponse {
  success: false;
  error: {
    code: string;                  // 错误代码
    message: string;               // 错误信息
    details?: any;                 // 错误详情（可选）
  };
}
```

**常见错误代码:**
- `CHAT_HISTORY_NOT_FOUND`: 聊天历史不存在
- `CHARACTER_NOT_FOUND`: 角色不存在
- `INVALID_PARAMETERS`: 请求参数无效
- `UNAUTHORIZED`: 未授权访问
- `PERMISSION_DENIED`: 无权限访问该聊天
- `RATE_LIMIT_EXCEEDED`: 请求频率超限
- `INTERNAL_SERVER_ERROR`: 服务器内部错误

**错误响应示例:**
```json
{
  "success": false,
  "error": {
    "code": "CHARACTER_NOT_FOUND",
    "message": "Character with id '999' not found",
    "details": {
      "characterId": "999"
    }
  }
}
```

## 实现注意事项

### 1. 性能优化
- 聊天历史查询应支持分页，避免一次性返回大量数据
- 对于热门角色的聊天历史，考虑使用缓存机制
- 最近聊天列表可以缓存到Redis中提高响应速度

### 2. 数据安全
- 所有聊天相关接口都需要用户认证
- 用户只能访问自己的聊天历史
- 敏感信息需要适当脱敏处理

### 3. 业务逻辑
- 删除聊天历史时，同时更新最近聊天列表
- 发送新消息时，自动更新最近聊天记录
- 未读消息计数需要实时更新

### 4. 扩展性考虑
- 消息类型枚举支持后续扩展（如文件、位置等）
- 聊天记录支持软删除机制
- 考虑消息加密存储的可能性