import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

// 模糊搜索工具函数
export function fuzzySearch(searchTerm: string, text: string): boolean {
  if (!searchTerm) return true;
  
  const normalizedSearch = searchTerm.toLowerCase().trim();
  const normalizedText = text.toLowerCase();
  
  return normalizedText.includes(normalizedSearch);
}

// 角色模糊搜索函数
export function searchCharacters(characters: any[], searchTerm: string): any[] {
  if (!searchTerm.trim()) return characters;
  
  const normalizedSearch = searchTerm.toLowerCase().trim();
  
  return characters.filter(character => {
    // 搜索角色名称
    if (fuzzySearch(normalizedSearch, character.name)) return true;
    
    // 搜索职业
    if (character.occupation && fuzzySearch(normalizedSearch, character.occupation)) return true;
    
    // 搜索标签
    if (character.tags && Array.isArray(character.tags)) {
      if (character.tags.some((tag: string) => fuzzySearch(normalizedSearch, tag))) return true;
    }
    
    // 搜索描述
    if (character.description && fuzzySearch(normalizedSearch, character.description)) return true;
    
    // 搜索创作者名称
    if (character.creator?.name && fuzzySearch(normalizedSearch, character.creator.name)) return true;
    
    return false;
  });
}

// 防抖函数，用于优化搜索性能
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;
  
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}
