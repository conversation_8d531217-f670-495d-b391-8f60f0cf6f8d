'use client';

import { Twitter, MessageCircle, Share2 } from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { useState } from "react";

interface ShareDialogProps {
  isOpen: boolean;
  onClose: () => void;
  characterName: string;
  characterId: string | number;
}

export default function ShareDialog({
  isOpen,
  onClose,
  characterName,
  characterId,
}: ShareDialogProps) {
  const [copySuccess, setCopySuccess] = useState(false);
  // 使用角色ID作为URL标识符，与现有URL结构保持一致
  const shareUrl = `https://www.loomelove.ai/chat/${characterId}`;
  const shareText = `Check out ${characterName} on Loomelove.ai! 💕`;

  // 复制到剪贴板
  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(shareUrl);
      setCopySuccess(true);
      setTimeout(() => setCopySuccess(false), 2000);
    } catch (err) {
      console.error('Failed to copy: ', err);
    }
  };

  // Twitter 分享
  const shareToTwitter = () => {
    const twitterUrl = `https://twitter.com/intent/tweet?text=${encodeURIComponent(shareText)}&url=${encodeURIComponent(shareUrl)}`;
    window.open(twitterUrl, '_blank', 'width=600,height=400');
  };

  // Reddit 分享
  const shareToReddit = () => {
    const redditUrl = `https://reddit.com/submit?url=${encodeURIComponent(shareUrl)}&title=${encodeURIComponent(shareText)}`;
    window.open(redditUrl, '_blank', 'width=600,height=400');
  };

  // Telegram 分享
  const shareToTelegram = () => {
    const telegramUrl = `https://t.me/share/url?url=${encodeURIComponent(shareUrl)}&text=${encodeURIComponent(shareText)}`;
    window.open(telegramUrl, '_blank', 'width=600,height=400');
  };

  // WhatsApp 分享
  const shareToWhatsApp = () => {
    const whatsappUrl = `https://wa.me/?text=${encodeURIComponent(shareText + ' ' + shareUrl)}`;
    window.open(whatsappUrl, '_blank', 'width=600,height=400');
  };

  // Facebook 分享
  const shareToFacebook = () => {
    const facebookUrl = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(shareUrl)}`;
    window.open(facebookUrl, '_blank', 'width=600,height=400');
  };

  // LinkedIn 分享
  const shareToLinkedIn = () => {
    const linkedinUrl = `https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(shareUrl)}`;
    window.open(linkedinUrl, '_blank', 'width=600,height=400');
  };

  // 通用分享（使用 Web Share API）
  const shareNative = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: `Check out ${characterName}`,
          text: shareText,
          url: shareUrl,
        });
      } catch (err) {
        console.error('Error sharing:', err);
      }
    } else {
      // 如果不支持 Web Share API，复制链接
      copyToClipboard();
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="bg-[#1a0a24] border-[#3a1a44] text-white">
        <DialogHeader>
          <DialogTitle className="text-xl">
            Share {characterName}
          </DialogTitle>
          <DialogDescription className="text-gray-400">
            Share this character with your friends
          </DialogDescription>
        </DialogHeader>
        
        <div className="grid grid-cols-3 gap-4 py-6">
          {/* Twitter */}
          <button 
            onClick={shareToTwitter}
            className="flex flex-col items-center p-3 rounded-lg hover:bg-[#2a1a34] transition-colors"
          >
            <div className="h-14 w-14 rounded-full bg-[#1DA1F2] flex items-center justify-center mb-2">
              <Twitter className="h-7 w-7 text-white" />
            </div>
            <span className="text-sm text-gray-300">Twitter</span>
          </button>
          
          {/* Reddit */}
          <button 
            onClick={shareToReddit}
            className="flex flex-col items-center p-3 rounded-lg hover:bg-[#2a1a34] transition-colors"
          >
            <div className="h-14 w-14 rounded-full bg-[#FF4500] flex items-center justify-center mb-2">
              <svg
                className="h-7 w-7 text-white"
                viewBox="0 0 24 24"
                fill="currentColor"
              >
                <path d="M12 0A12 12 0 0 0 0 12a12 12 0 0 0 12 12 12 12 0 0 0 12-12A12 12 0 0 0 12 0zm5.01 4.744c.688 0 1.25.561 1.25 1.249a1.25 1.25 0 0 1-2.498.056l-2.597-.547-.8 3.747c1.824.07 3.48.632 4.674 1.488.308-.309.73-.491 1.207-.491.968 0 1.754.786 1.754 1.754 0 .716-.435 1.333-1.01 1.614a3.111 3.111 0 0 1 .042.52c0 2.694-3.13 4.87-7.004 4.87-3.874 0-7.004-2.176-7.004-4.87 0-.183.015-.366.043-.534A1.748 1.748 0 0 1 4.028 12c0-.968.786-1.754 1.754-1.754.463 0 .898.196 1.207.49 1.207-.883 2.878-1.43 4.744-1.487l.885-4.182a.342.342 0 0 1 .14-.197.35.35 0 0 1 .238-.042l2.906.617a1.214 1.214 0 0 1 1.108-.701zM9.25 12C8.561 12 8 12.562 8 13.25c0 .687.561 1.248 1.25 1.248.687 0 1.248-.561 1.248-1.249 0-.688-.561-1.249-1.249-1.249zm5.5 0c-.687 0-1.248.561-1.248 1.25 0 .687.561 1.248 1.249 1.248.688 0 1.249-.561 1.249-1.249 0-.687-.562-1.249-1.25-1.249zm-5.466 3.99a.327.327 0 0 0-.231.094.33.33 0 0 0 0 .463c.842.842 2.484.913 2.961.913.477 0 2.105-.056 2.961-.913a.361.361 0 0 0 .029-.463.33.33 0 0 0-.464 0c-.547.533-1.684.73-2.512.73-.828 0-1.979-.196-2.512-.73a.326.326 0 0 0-.232-.095z" />
              </svg>
            </div>
            <span className="text-sm text-gray-300">Reddit</span>
          </button>
          
          {/* Telegram */}
          <button 
            onClick={shareToTelegram}
            className="flex flex-col items-center p-3 rounded-lg hover:bg-[#2a1a34] transition-colors"
          >
            <div className="h-14 w-14 rounded-full bg-[#0088cc] flex items-center justify-center mb-2">
              <svg
                className="h-7 w-7 text-white"
                viewBox="0 0 24 24"
                fill="currentColor"
              >
                <path d="M12 0C5.373 0 0 5.373 0 12s5.373 12 12 12 12-5.373 12-12S18.627 0 12 0zm.375 16.875c-1.003 0-1.875-.897-1.875-1.875s.872-1.875 1.875-1.875c1.002 0 1.875.897 1.875 1.875s-.873 1.875-1.875 1.875zm2.1-5.175c-.224.117-1.152.57-1.152.57l-1.272-.127s-1.044-.57-1.044-1.331c0-.76.796-1.725 2.1-1.725 1.304 0 2.1.965 2.1 1.725 0 .76-.508 1.013-.732 1.128v-.24zm4.401-2.376c-.035 1.723-.962 3.476-2.376 4.95v.001c-1.474 1.414-3.227 2.342-4.95 2.376h-.15c-1.723-.034-3.476-.962-4.95-2.376-1.414-1.474-2.342-3.227-2.376-4.95v-.15c.034-1.723.962-3.476 2.376-4.95 1.474-1.414 3.227-2.342 4.95-2.376h.15c1.723.034 3.476.962 4.95 2.376 1.414 1.474 2.342 3.227 2.376 4.95v.15z" />
              </svg>
            </div>
            <span className="text-sm text-gray-300">Telegram</span>
          </button>
          
          {/* WhatsApp */}
          <button 
            onClick={shareToWhatsApp}
            className="flex flex-col items-center p-3 rounded-lg hover:bg-[#2a1a34] transition-colors"
          >
            <div className="h-14 w-14 rounded-full bg-[#25D366] flex items-center justify-center mb-2">
              <svg
                className="h-7 w-7 text-white"
                viewBox="0 0 24 24"
                fill="currentColor"
              >
                <path d="M12 0a12 12 0 1 0 0 24 12 12 0 0 0 0-24zm.14 4.5a7.34 7.34 0 0 1 5.7 11.95l.57 2.05-2.1-.6a7.22 7.22 0 0 1-4.17 1.3 7.35 7.35 0 0 1-7.35-7.35c0-4.05 3.3-7.35 7.35-7.35zm0 1.16a6.19 6.19 0 0 0-6.19 6.2 6.19 6.19 0 0 0 6.2 6.18c1.09 0 2.16-.3 3.1-.84l.17-.12.7.2-.27-.98.16-.17a6.19 6.19 0 0 0 2.14-4.27 6.19 6.19 0 0 0-6.2-6.2zm3.8 2.94c.15 0 .3.01.43.04.12.03.23.08.33.15.1.07.18.17.24.3.05.14.08.29.08.47 0 .2-.05.42-.15.68-.1.26-.3.54-.6.83a3.1 3.1 0 0 1-1.2.93c-.48.23-1.09.35-1.84.35h-.2a.4.4 0 0 0-.24.08a.43.43 0 0 0-.15.23l-.02.06-.38 1.7c-.02.09-.05.15-.1.2a.33.33 0 0 1-.23.1h-.7l-.04-.02a.1.1 0 0 1-.04-.04a.1.1 0 0 1-.01-.05l.6-2.6v-.01l.24-1.01v-.02a.4.4 0 0 1 .13-.22a.38.38 0 0 1 .25-.1h.24c.65 0 1.17-.1 1.56-.3.39-.2.67-.44.85-.74.18-.3.3-.6.36-.92.06-.33.09-.6.09-.83 0-.22-.03-.4-.09-.54a.49.49 0 0 0-.23-.28a.9.9 0 0 0-.35-.12 2.32.32 0 0 0-.4-.02h-.02c-.4 0-.75.07-1.07.22a2.2 2.2 0 0 0-.8.61c-.21.26-.37.56-.48.92-.1.35-.16.73-.16 1.13v.04c0 .17-.04.32-.13.45a.48.48 0 0 1-.35.23h-.01a.75.75 0 0 1-.5-.23a.77.77 0 0 1-.23-.52v-.01c0-.7.13-1.32.4-1.88.25-.56.6-1.03 1.05-1.41.45-.38.97-.67 1.56-.87a5.87 5.87 0 0 1 1.89-.29zm-7.46.43c.15 0 .3.04.42.12.12.08.22.2.28.34.07.15.1.32.1.52 0 .2-.03.4-.11.59l-.02.05c-.27.65-.68 1.13-1.25 1.43-.57.3-1.24.45-2.02.45h-.02a.77.77 0 0 0-.46.16a.6.6 0 0 0-.22.4l-.01.06-.2.92-.21.94a.96.96 0 0 1-.11.29a.3.3 0 0 1-.24.13h-.71l-.04-.02a.08.08 0 0 1-.03-.04a.1.1 0 0 1-.02-.05l.03-.14.82-3.66.02-.07a.52.52 0 0 1 .17-.27a.45.45 0 0 1 .3-.11h.3c.33 0 .64-.04.92-.12.28-.08.52-.2.73-.35.2-.15.36-.34.48-.57.12-.23.18-.5.18-.79 0-.3-.07-.53-.2-.7a.74.74 0 0 0-.56-.25h-.02c-.23 0-.47.06-.72.17-.25.12-.48.3-.7.54-.2.24-.38.54-.52.9a4 4 0 0 0-.19 1.3c0 .3-.07.53-.2.7a.74.74 0 0 1-.56.25h-.01a.74.74 0 0 1-.56-.25c-.14-.17-.2-.4-.2-.7 0-.64.12-1.2.37-1.7.25-.5.59-.92 1.02-1.27.43-.35.93-.61 1.5-.8a5.25 5.25 0 0 1 1.74-.26h.05z" />
              </svg>
            </div>
            <span className="text-sm text-gray-300">WhatsApp</span>
          </button>
          
          {/* Facebook */}
          <button 
            onClick={shareToFacebook}
            className="flex flex-col items-center p-3 rounded-lg hover:bg-[#2a1a34] transition-colors"
          >
            <div className="h-14 w-14 rounded-full bg-[#1877F2] flex items-center justify-center mb-2">
              <svg
                className="h-7 w-7 text-white"
                viewBox="0 0 24 24"
                fill="currentColor"
              >
                <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
              </svg>
            </div>
            <span className="text-sm text-gray-300">Facebook</span>
          </button>
          
          {/* Native Share / Copy */}
          <button 
            onClick={shareNative}
            className="flex flex-col items-center p-3 rounded-lg hover:bg-[#2a1a34] transition-colors"
          >
            <div className="h-14 w-14 rounded-full bg-[#8B5CF6] flex items-center justify-center mb-2">
              <Share2 className="h-7 w-7 text-white" />
            </div>
            <span className="text-sm text-gray-300">
              {typeof navigator.share === 'function' ? 'Share' : 'Copy'}
            </span>
          </button>
        </div>
        
        <div className="bg-[#0e0314] rounded-lg p-4 flex items-center">
          <input
            type="text"
            value={shareUrl}
            readOnly
            className="flex-1 bg-transparent border-none focus:outline-none text-base text-gray-300"
          />
          <button 
            className={`ml-2 px-4 py-1.5 text-white text-sm rounded-md transition-colors ${
              copySuccess 
                ? 'bg-green-500 hover:bg-green-600' 
                : 'bg-pink-500 hover:bg-pink-600'
            }`}
            onClick={copyToClipboard}
          >
            {copySuccess ? 'Copied!' : 'Copy'}
          </button>
        </div>
        
        <DialogFooter>
          <Button
            variant="outline"
            className="text-base"
            onClick={onClose}
          >
            Close
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}