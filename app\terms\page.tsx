"use client"
import Link from "next/link"
import { ArrowLeft, Shield, Users, MessageCircle, Heart } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"

export default function TermsOfServicePage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-[#0a0a0f] via-[#1a0a24] to-[#2a1a34]">
      {/* Background decoration */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-pink-500/10 rounded-full blur-3xl"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-purple-500/10 rounded-full blur-3xl"></div>
      </div>

      <div className="relative max-w-4xl mx-auto px-6 py-12">
        {/* Header */}
        <div className="mb-12">
          <Link href="/login">
            <Button variant="ghost" className="mb-6 text-gray-300 hover:text-white">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Login
            </Button>
          </Link>
          
          <div className="text-center">
            <div className="flex justify-center mb-6">
              <div className="h-16 w-16 rounded-full p-1 bg-gradient-to-r from-pink-500 via-purple-500 to-pink-500">
                <div className="h-full w-full rounded-full bg-gradient-to-br from-[#1a0a24] to-[#2a1a34] flex items-center justify-center">
                  <Shield className="h-8 w-8 text-white" />
                </div>
              </div>
            </div>
            <h1 className="text-4xl font-bold bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent mb-4">
              Terms of Service
            </h1>
            <p className="text-gray-400 text-lg">
              Last updated: January 2025
            </p>
          </div>
        </div>

        {/* Content */}
        <div className="bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-xl border border-white/20 rounded-3xl p-8 space-y-8">
          
          {/* Section 1: Agreement */}
          <section>
            <div className="flex items-center mb-4">
              <Heart className="h-6 w-6 text-pink-400 mr-3" />
              <h2 className="text-2xl font-bold text-white">1. Agreement to Terms</h2>
            </div>
            <div className="text-gray-300 space-y-4">
              <p>
                By accessing and using Lumilove ("the Service"), you accept and agree to be bound by the terms and provision of this agreement. 
                If you do not agree to abide by the above, please do not use this service.
              </p>
              <p>
                Lumilove is an AI companion platform that provides interactive chat experiences with AI-generated characters. 
                Users must be at least 18 years old to use this service.
              </p>
            </div>
          </section>

          {/* Section 2: User Conduct */}
          <section>
            <div className="flex items-center mb-4">
              <Users className="h-6 w-6 text-purple-400 mr-3" />
              <h2 className="text-2xl font-bold text-white">2. User Conduct</h2>
            </div>
            <div className="text-gray-300 space-y-4">
              <p>You agree to use the Service responsibly and in compliance with all applicable laws. You will NOT:</p>
              <ul className="list-disc list-inside space-y-2 ml-4">
                <li>Use the Service for any illegal or unauthorized purpose</li>
                <li>Attempt to gain unauthorized access to our systems or other users' accounts</li>
                <li>Share, distribute, or publicly display content from the Service without permission</li>
                <li>Create multiple accounts to circumvent limitations or restrictions</li>
                <li>Use the Service to harass, abuse, or harm others</li>
                <li>Attempt to extract, copy, or reverse engineer our AI models</li>
              </ul>
            </div>
          </section>

          {/* Section 3: AI Interactions */}
          <section>
            <div className="flex items-center mb-4">
              <MessageCircle className="h-6 w-6 text-green-400 mr-3" />
              <h2 className="text-2xl font-bold text-white">3. AI Character Interactions</h2>
            </div>
            <div className="text-gray-300 space-y-4">
              <p>
                Our AI characters are artificial entities designed for entertainment and companionship. You understand that:
              </p>
              <ul className="list-disc list-inside space-y-2 ml-4">
                <li>AI responses are generated by machine learning models and do not represent real human thoughts or feelings</li>
                <li>AI characters may occasionally produce unexpected or inappropriate responses despite our safety measures</li>
                <li>You should not form unhealthy emotional dependencies on AI characters</li>
                <li>AI interactions are not a substitute for professional therapy, counseling, or medical advice</li>
                <li>We continuously work to improve AI safety and appropriateness</li>
              </ul>
            </div>
          </section>

          {/* Section 4: Content and Privacy */}
          <section>
            <h2 className="text-2xl font-bold text-white mb-4">4. Content and Privacy</h2>
            <div className="text-gray-300 space-y-4">
              <p>
                Your conversations with AI characters are private by default. However:
              </p>
              <ul className="list-disc list-inside space-y-2 ml-4">
                <li>We may analyze conversations to improve our AI models (in anonymized form)</li>
                <li>We reserve the right to review content that violates our terms</li>
                <li>You retain ownership of content you create, but grant us license to use it for service operation</li>
                <li>We implement security measures to protect your data, but cannot guarantee absolute security</li>
              </ul>
            </div>
          </section>

          {/* Section 5: Subscription and Payments */}
          <section>
            <h2 className="text-2xl font-bold text-white mb-4">5. Subscription and Payments</h2>
            <div className="text-gray-300 space-y-4">
              <p>
                Premium features require a paid subscription:
              </p>
              <ul className="list-disc list-inside space-y-2 ml-4">
                <li>Subscriptions auto-renew unless cancelled</li>
                <li>Refunds are provided according to our refund policy</li>
                <li>We may change pricing with 30 days notice</li>
                <li>Free tier users have limited access to features</li>
                <li>Payment processing is handled by secure third-party providers</li>
              </ul>
            </div>
          </section>

          {/* Section 6: Intellectual Property */}
          <section>
            <h2 className="text-2xl font-bold text-white mb-4">6. Intellectual Property</h2>
            <div className="text-gray-300 space-y-4">
              <p>
                The Service and its original content, features, and functionality are owned by Lumilove and are protected by 
                international copyright, trademark, patent, trade secret, and other intellectual property laws.
              </p>
            </div>
          </section>

          {/* Section 7: Disclaimers */}
          <section>
            <h2 className="text-2xl font-bold text-white mb-4">7. Disclaimers and Limitations</h2>
            <div className="text-gray-300 space-y-4">
              <p>
                The Service is provided "as is" without warranties of any kind. We disclaim all warranties, 
                express or implied, including but not limited to:
              </p>
              <ul className="list-disc list-inside space-y-2 ml-4">
                <li>Merchantability and fitness for a particular purpose</li>
                <li>Non-infringement of third-party rights</li>
                <li>Accuracy, reliability, or completeness of AI responses</li>
                <li>Uninterrupted or error-free service operation</li>
              </ul>
            </div>
          </section>

          {/* Section 8: Termination */}
          <section>
            <h2 className="text-2xl font-bold text-white mb-4">8. Termination</h2>
            <div className="text-gray-300 space-y-4">
              <p>
                We may terminate or suspend your account and access to the Service immediately, without prior notice, 
                for conduct that we believe violates these Terms or is harmful to other users, us, or third parties.
              </p>
            </div>
          </section>

          {/* Section 9: Contact */}
          <section>
            <h2 className="text-2xl font-bold text-white mb-4">9. Contact Information</h2>
            <div className="text-gray-300 space-y-4">
              <p>
                If you have any questions about these Terms of Service, please contact us at:
              </p>
              <div className="bg-[#1a0a24] rounded-lg p-4">
                <p className="text-white font-medium">Email: <EMAIL></p>
                <p className="text-gray-400">We typically respond within 24-48 hours</p>
              </div>
            </div>
          </section>

        </div>

        {/* Footer */}
        <div className="mt-8 text-center">
          <p className="text-gray-500 text-sm">
            By using Lumilove, you acknowledge that you have read and understood these terms.
          </p>
        </div>
      </div>
    </div>
  )
} 