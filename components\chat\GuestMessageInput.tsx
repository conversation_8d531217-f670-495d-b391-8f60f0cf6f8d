"use client";

import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { MessageCircle } from 'lucide-react';

interface GuestMessageInputProps {
  characterName: string;
}

export default function GuestMessageInput({ characterName }: GuestMessageInputProps) {
  return (
    <div className="p-2 sm:p-3 md:p-4 border-t border-[#3a1a44] pb-2">
      {/* PC端游客提示 */}
      <div className="hidden md:block">
        <div className="bg-gradient-to-r from-purple-500/20 to-pink-500/20 rounded-xl p-6 text-center border border-purple-500/30">
          <div className="flex items-center justify-center mb-4">
            <div className="bg-gradient-to-r from-purple-400 to-pink-400 rounded-full p-3">
              <MessageCircle className="h-6 w-6 text-white" />
            </div>
          </div>
          
          <h3 className="text-white font-medium mb-2 text-lg">Start chatting with {characterName}</h3>
          <p className="text-purple-200 text-sm mb-6">
            Sign in to chat unlimited with AI characters and save your conversations
          </p>
          
          <div className="flex gap-4 justify-center">
            <Button variant="outline" size="default" asChild className="min-w-[120px]">
              <Link href="/login">Sign In</Link>
            </Button>
            <Button variant="default" size="default" asChild className="min-w-[120px]">
              <Link href="/register">Sign Up</Link>
            </Button>
          </div>
          
          <div className="mt-4 pt-4 border-t border-purple-500/20">
            <p className="text-xs text-gray-400">
              Already have account? <Link href="/login" className="text-pink-400 hover:text-pink-300">Sign in now</Link>
            </p>
          </div>
        </div>
      </div>

      {/* 移动端游客提示 */}
      <div className="md:hidden">
        <div className="bg-gradient-to-r from-purple-500/20 to-pink-500/20 rounded-xl p-4 text-center border border-purple-500/30">
          <div className="flex items-center justify-center mb-3">
            <div className="bg-gradient-to-r from-purple-400 to-pink-400 rounded-full p-2">
              <MessageCircle className="h-5 w-5 text-white" />
            </div>
          </div>
          
          <h3 className="text-white font-medium mb-2">Start Chatting</h3>
          <p className="text-purple-200 text-sm mb-4">
            Sign in to chat with {characterName}
          </p>
          
          <div className="flex gap-3 justify-center">
            <Button variant="outline" size="sm" asChild className="flex-1 max-w-[120px]">
              <Link href="/login">Sign In</Link>
            </Button>
            <Button variant="default" size="sm" asChild className="flex-1 max-w-[120px]">
              <Link href="/register">Sign Up</Link>
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}