"use client"

import type React from "react"

import { useEffect, useRef, useCallback, useState } from "react"
import { cn } from "@/lib/utils"

interface Bubble {
  id: number
  baseX: number
  baseY: number
  x: number
  y: number
  offsetX: number
  size: number
  color: { base: string; light: string; accent: string }
  text: string
  longText: string
  pulse: number
  pulseSpeed: number
  floatAmplitude: number
  mass: number
  isHovered: boolean
  clickAnimation: number
  alpha: number
  scale: number
  isDisappearing: boolean
  spawnAnimation: number
  zIndex: number
  shimmerOffset: number
  shimmerSpeed: number
}

interface FloatingHeartBubblesProps {
  onSelectText: (text: string) => void
  className?: string
  onToggleCollapse?: () => void
  isCollapsed?: boolean
}

const heartTexts = [
  { short: "Hey gorgeous", long: "Can I steal you for a second?" },
  { short: "Miss you", long: "Missing you is my new hobby." },
  { short: "Cuddle?", long: "Wish you were here to cuddle me tonight." },
  { short: "Sweet dreams", long: "Sweet dreams… bet you'll dream of me." },
  { short: "My love", long: "My love, what are you wearing right now?" },
  { short: "Kiss me", long: "I want your kisses all over me." },
  { short: "You're perfect", long: "You're trouble, but I like your kind of trouble." },
  { short: "Date night", long: "Let's skip dinner and go straight to dessert…" },
  { short: "Hold me", long: "I need your arms around me right now." },
  { short: "Forever", long: "Can I keep you forever?" },
  { short: "Love you", long: "If I say I love you, will you come over?" },
  { short: "Be mine", long: "Just say you're mine tonight." },
  { short: "Hug me", long: "Only your hugs can fix my mood." },
  { short: "So cute", long: "Why are you so cute? It's distracting…" },
  { short: "Adorable", long: "You're so adorable, I just want to tease you." },
  { short: "Sweetheart", long: "Miss hearing you call me sweetheart." },
  { short: "Darling", long: "What would you do if I was right beside you now?" },
  { short: "Baby", long: "Baby, I can't behave when you're around." },
  { short: "Angel", long: "Were you this sweet to everyone, or just me?" },
  { short: "Flirt with me", long: "I like it when you flirt back." },
  { short: "Tease me", long: "You know how much I love when you tease me." },
  { short: "Miss your touch", long: "I keep replaying your touch in my mind." },
  { short: "Dream of you", long: "Bet I'll wake up smiling after dreaming of you." },
  { short: "Need you", long: "I need you more than you know." },
  { short: "Heartbeat", long: "You make my heart race like crazy." },
  { short: "Can't sleep", long: "Can't sleep… care to join me?" },
  { short: "Come closer", long: "Closer… I want to feel your breath on my neck." },
  { short: "Naughty", long: "Are you trying to be naughty, or is it just me?" },
  { short: "Just us", long: "Let's get lost in our own little world." },
  { short: "You & me", long: "Just you and me, and a little bit of trouble." },
]

const bubbleColors = [
  { base: "#FF69B4", light: "#FFE4E1", accent: "#FF1493" },
  { base: "#DA70D6", light: "#E6E6FA", accent: "#BA55D3" },
  { base: "#87CEEB", light: "#E0F6FF", accent: "#4682B4" },
  { base: "#FFB347", light: "#FFF8DC", accent: "#FF8C00" },
  { base: "#98FB98", light: "#F0FFF0", accent: "#32CD32" },
  { base: "#DDA0DD", light: "#F8F8FF", accent: "#9370DB" },
  { base: "#F0E68C", light: "#FFFACD", accent: "#DAA520" },
]

export function FloatingHeartBubbles({
  onSelectText,
  className,
  onToggleCollapse,
  isCollapsed,
}: FloatingHeartBubblesProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const wrapperRef = useRef<HTMLDivElement>(null)
  const animationRef = useRef<number>(0)
  const bubblesRef = useRef<Bubble[]>([])
  const mouseRef = useRef({ x: 0, y: 0, isInside: false })
  const [isInitialized, setIsInitialized] = useState(false)
  const [hasError, setHasError] = useState(false)
  const [isCanvasSupported, setIsCanvasSupported] = useState(true)
  const bubbleIdCounter = useRef(0)
  const frameCounter = useRef(0)
  const zIndexCounter = useRef(0)
  const heartTextIndex = useRef(0)

  // 检查Canvas支持
  useEffect(() => {
    try {
      const canvas = document.createElement('canvas')
      const ctx = canvas.getContext('2d')
      if (!ctx) {
        setIsCanvasSupported(false)
        setHasError(true)
      }
    } catch (error) {
      console.error('Canvas not supported:', error)
      setIsCanvasSupported(false)
      setHasError(true)
    }
  }, [])

  // 🎯 高度敏感的数量策略，移动端优化
  const getBubbleCount = useCallback((rect: DOMRect) => {
    // 检测是否为移动端
    const isMobile = rect.width < 640;
    
    if (isMobile) {
      // 移动端减少泡泡数量，提高性能
      if (rect.height < 100) return Math.max(6, Math.min(10, Math.floor(rect.width / 100)));
      return Math.max(8, Math.min(14, Math.floor((rect.width * rect.height) / 8000)));
    } else {
      // 桌面端保持原逻辑
      if (rect.height < 100) return Math.max(8, Math.min(12, Math.floor(rect.width / 120)));
      return Math.max(10, Math.min(18, Math.floor((rect.width * rect.height) / 7000)));
    }
  }, [])

  // 🎯 自动间距与不重叠，更靠近中心分布
  const findNonOverlappingPosition = useCallback((existingBubbles: Bubble[], size: number, rect: DOMRect) => {
    let x: number,
      y: number,
      valid: boolean,
      tries = 0

    do {
      x = size / 2 + Math.random() * (rect.width - size)
      // 🎯 y 只在 40%-70% 范围生成，更靠近中心，避免贴边
      y = rect.height * (0.4 + Math.random() * 0.3)

      // 🎯 加大间距检查，从5改成10
      valid = existingBubbles
        .filter((h) => !h.isDisappearing)
        .every((h) => Math.hypot(x - h.x, y - h.y) > (size + h.size) / 2 + 10)
      tries++
      // 如果30次都找不到合适位置，缩小泡泡尺寸
      if (!valid && tries >= 30) {
        size = Math.max(40, size * 0.8) // 缩小20%，最小40px
        x = size / 2 + Math.random() * (rect.width - size)
        y = rect.height * (0.4 + Math.random() * 0.3)
        valid = true // 强制接受
      }
    } while (!valid && tries < 30)

    return { x, y, size }
  }, [])

  // 🎯 4. 泡泡尺寸缩小区间，移动端优化
  const generateBubbleSize = useCallback(() => {
    // 检测是否为移动端
    const isMobile = typeof window !== 'undefined' && window.innerWidth < 640;
    const baseSize = isMobile ? 50 : 60; // 移动端基础尺寸稍小
    
    const random = Math.random()
    if (random < 0.35) {
      // 大号泡泡
      return baseSize + 40 + Math.random() * 20
    } else if (random < 0.7) {
      // 中号泡泡
      return baseSize + 10 + Math.random() * 30
    } else {
      // 小号泡泡
      return baseSize + Math.random() * 10
    }
  }, [])

  const generateNewBubble = useCallback(
    (rect: DOMRect, existingBubbles: Bubble[] = []): Bubble => {
      let size = generateBubbleSize()
      const position = findNonOverlappingPosition(existingBubbles, size, rect)
      const x = position.x
      const y = position.y
      size = position.size
      const colorPair = bubbleColors[Math.floor(Math.random() * bubbleColors.length)]

      // 保证内容唯一，循环使用heartTexts
      const textData = heartTexts[heartTextIndex.current % heartTexts.length]
      heartTextIndex.current++

      return {
        id: bubbleIdCounter.current++,
        baseX: x,
        baseY: y,
        x: x,
        y: y,
        offsetX: 0,
        size,
        color: colorPair,
        text: textData.short,
        longText: textData.long, // 添加长文本属性
        pulse: Math.random() * Math.PI * 2,
        pulseSpeed: 0.005 + Math.random() * 0.003,
        floatAmplitude: 10 + Math.random() * 8, // 从8~12增加到10~18，增加垂直活动范围
        mass: size / 12,
        isHovered: false,
        clickAnimation: 0,
        alpha: 0,
        scale: 0.4,
        isDisappearing: false,
        spawnAnimation: 1,
        zIndex: zIndexCounter.current++,
        shimmerOffset: Math.random() * Math.PI * 2,
        shimmerSpeed: 0.02 + Math.random() * 0.03,
      }
    },
    [findNonOverlappingPosition, generateBubbleSize],
  )

  const initializeBubbles = useCallback(() => {
    try {
      const wrapper = wrapperRef.current
      if (!wrapper) return

      const rect = wrapper.getBoundingClientRect()
      const bubbles: Bubble[] = []
      const bubbleCount = getBubbleCount(rect)

      for (let i = 0; i < bubbleCount; i++) {
        const newBubble = generateNewBubble(rect, bubbles)
        // 初始化时直接显示
        newBubble.alpha = 1
        newBubble.scale = 1
        newBubble.spawnAnimation = 0
        bubbles.push(newBubble)
      }

      bubblesRef.current = bubbles
      setIsInitialized(true)
    } catch (error) {
      console.error('Error in initializeBubbles:', error)
      setHasError(true)
    }
  }, [generateNewBubble, getBubbleCount])

  const adjustBubbleCount = useCallback(
    (rect: DOMRect) => {
      try {
        const idealCount = getBubbleCount(rect)
        const currentCount = bubblesRef.current.filter((h) => !h.isDisappearing).length

        if (currentCount < idealCount) {
          const needToAdd = idealCount - currentCount
          for (let i = 0; i < needToAdd; i++) {
            const newBubble = generateNewBubble(rect, bubblesRef.current)
            bubblesRef.current.push(newBubble)
          }
        } else if (currentCount > idealCount) {
          const needToRemove = currentCount - idealCount
          let removed = 0
          for (let i = 0; i < bubblesRef.current.length && removed < needToRemove; i++) {
            const bubble = bubblesRef.current[i]
            if (!bubble.isDisappearing && !bubble.isHovered) {
              bubble.isDisappearing = true
              removed++
            }
          }
        }
      } catch (error) {
        console.error('Error in adjustBubbleCount:', error)
        setHasError(true)
      }
    },
    [getBubbleCount, generateNewBubble],
  )

  const updatePhysics = useCallback(() => {
    try {
      const wrapper = wrapperRef.current
      if (!wrapper) return
      const rect = wrapper.getBoundingClientRect()
      const mouse = mouseRef.current
      const bubbles = bubblesRef.current

      frameCounter.current++

      bubbles.forEach((bubble, index) => {
        // 处理消失动画
        if (bubble.isDisappearing) {
          bubble.alpha = Math.max(0, bubble.alpha - 0.04)
          bubble.scale = Math.max(0.2, bubble.scale - 0.04)
          if (bubble.alpha <= 0) {
            const newBubble = generateNewBubble(rect, bubbles)
            bubbles[index] = newBubble
          }
          return
        }

        // 处理出现动画
        if (bubble.spawnAnimation > 0) {
          bubble.spawnAnimation -= 0.03
          bubble.alpha = Math.min(1, 1 - bubble.spawnAnimation)
          bubble.scale = Math.min(1, 0.4 + (1 - bubble.spawnAnimation) * 0.6)
        }

        // 🌊 核心机制：pulse上下漂浮
        bubble.pulse += bubble.pulseSpeed
        bubble.y = bubble.baseY + Math.sin(bubble.pulse + bubble.id) * bubble.floatAmplitude

        // 🌊 轻微噪声扰动，保证x一直在左右轻晃（基础浮动）
        bubble.offsetX += (Math.random() - 0.5) * 0.24 // 从0.16增加到0.24

        // 🌊 缓慢回弹到baseX，稍微放慢回弹速度让活动范围更大
        bubble.offsetX *= 0.92 // 从0.94调整到0.92，让回弹稍慢一点

        // 🌊 计算最终x坐标
        bubble.x = bubble.baseX + bubble.offsetX

        // 更新水晶球闪烁效果
        bubble.shimmerOffset += bubble.shimmerSpeed

        // 🟠 区域边界吸附（直接修正baseX/baseY，避免反弹卡边）
        const radius = bubble.size / 2
        bubble.baseX = Math.max(radius, Math.min(rect.width - radius, bubble.baseX))
        bubble.baseY = Math.max(radius, Math.min(rect.height - radius, bubble.baseY))

        // 鼠标交互
      if (mouse.isInside) {
        const dx = mouse.x - bubble.x
        const dy = mouse.y - bubble.y
        const distance = Math.sqrt(dx * dx + dy * dy)
        const wasHovered = bubble.isHovered
        bubble.isHovered = distance < bubble.size / 2
        if (bubble.isHovered && !wasHovered) {
          bubble.zIndex = zIndexCounter.current++
        }
      } else {
        bubble.isHovered = false
      }

      // 更新动画属性
      if (bubble.clickAnimation > 0) bubble.clickAnimation -= 0.04
    })

    // 🌊 碰撞检测/响应（baseX/baseY平滑分离，无抖动）
    const activeBubbles = bubbles.filter((h) => !h.isDisappearing)
    for (let i = 0; i < activeBubbles.length; i++) {
      for (let j = i + 1; j < activeBubbles.length; j++) {
        const b1 = activeBubbles[i]
        const b2 = activeBubbles[j]
        const dx = b1.x - b2.x
        const dy = b1.y - b2.y
        const distance = Math.sqrt(dx * dx + dy * dy)
        const minDistance = (b1.size + b2.size) / 2 + 4 // 4px安全间隔

        if (distance < minDistance && distance > 0) {
          const overlap = (minDistance - distance) / 2
          const nx = dx / distance
          const ny = dy / distance

          // 用 lerp 平滑推开，防止跳动
          const lerp = (a: number, b: number, t: number) => a + (b - a) * t

          b1.baseX = lerp(b1.baseX, b1.baseX + nx * overlap, 0.3)
          b1.baseY = lerp(b1.baseY, b1.baseY + ny * overlap, 0.3)
          b2.baseX = lerp(b2.baseX, b2.baseX - nx * overlap, 0.3)
          b2.baseY = lerp(b2.baseY, b2.baseY - ny * overlap, 0.3)

          // 极小扰动避免死锁
          b1.baseX += (Math.random() - 0.5) * 0.2
          b1.baseY += (Math.random() - 0.5) * 0.2
          b2.baseX += (Math.random() - 0.5) * 0.2
          b2.baseY += (Math.random() - 0.5) * 0.2
        }
      }
    }
  } catch (error) {
    console.error('Error in updatePhysics:', error)
    setHasError(true)
  }
  }, [generateNewBubble])

  const drawBubble = useCallback((ctx: CanvasRenderingContext2D, bubble: Bubble) => {
    try {
      const { x, y, size, color, text, pulse, isHovered, clickAnimation, alpha, scale, shimmerOffset } = bubble

      if (alpha <= 0) return

      ctx.save()
      ctx.globalAlpha = alpha

    ctx.translate(x, y)
    const pulseScale = 1 + Math.sin(pulse) * 0.03 // 增加脉冲幅度
    const hoverScale = isHovered ? 1.15 : 1
    const clickScale = clickAnimation > 0 ? 1 + clickAnimation * 0.3 : 1
    const totalScale = scale * pulseScale * hoverScale * clickScale
    ctx.scale(totalScale, totalScale)

    const radius = size / 2

    // 🔮 第一层：基础渐变球体
    const baseGradient = ctx.createRadialGradient(0, -radius * 0.3, 0, 0, 0, radius)
    baseGradient.addColorStop(0, color.accent) // 中心是深色
    baseGradient.addColorStop(0.4, color.base) // 中间是基础色
    baseGradient.addColorStop(0.8, color.light) // 外围是浅色
    baseGradient.addColorStop(1, "rgba(255,255,255,0.95)") // 边缘是白色

    // 外层阴影
    if (isHovered) {
      ctx.shadowColor = color.base
      ctx.shadowBlur = 25
      ctx.shadowOffsetX = 0
      ctx.shadowOffsetY = 4
    } else {
      ctx.shadowColor = "rgba(0,0,0,0.25)"
      ctx.shadowBlur = 12
      ctx.shadowOffsetX = 0
      ctx.shadowOffsetY = 3
    }

    // 绘制基础球体
    ctx.fillStyle = baseGradient
    ctx.beginPath()
    ctx.arc(0, 0, radius, 0, Math.PI * 2)
    ctx.fill()

    // 清除阴影
    ctx.shadowColor = "transparent"

    // 🔮 第二层：动态彩虹泡泡叠加层
    const bubbleTime = shimmerOffset * 2

    // 创建多个彩虹条纹
    for (let i = 0; i < 3; i++) {
      const angle = (bubbleTime + i * Math.PI * 0.7) % (Math.PI * 2)
      const offsetX = Math.cos(angle) * radius * 0.3
      const offsetY = Math.sin(angle) * radius * 0.3

      const rainbowGradient = ctx.createRadialGradient(offsetX, offsetY, 0, offsetX, offsetY, radius * 0.8)

      const hue1 = (bubbleTime * 30 + i * 120) % 360
      const hue2 = (hue1 + 60) % 360
      const hue3 = (hue1 + 120) % 360

      rainbowGradient.addColorStop(0, `hsla(${hue1}, 80%, 70%, 0.4)`)
      rainbowGradient.addColorStop(0.3, `hsla(${hue2}, 80%, 70%, 0.3)`)
      rainbowGradient.addColorStop(0.6, `hsla(${hue3}, 80%, 70%, 0.2)`)
      rainbowGradient.addColorStop(1, `hsla(${hue1}, 80%, 70%, 0)`)

      ctx.fillStyle = rainbowGradient
      ctx.globalCompositeOperation = "screen" // 叠加模式
      ctx.beginPath()
      ctx.arc(0, 0, radius, 0, Math.PI * 2)
      ctx.fill()
    }

    // 重置混合模式
    ctx.globalCompositeOperation = "source-over"

    // 🔮 第三层：流动的彩虹反射条纹
    const stripeAngle = bubbleTime * 0.5
    const stripeGradient = ctx.createLinearGradient(
      -radius * Math.cos(stripeAngle),
      -radius * Math.sin(stripeAngle),
      radius * Math.cos(stripeAngle),
      radius * Math.sin(stripeAngle),
    )

    const mainHue = (bubbleTime * 40) % 360
    stripeGradient.addColorStop(0, `hsla(${mainHue}, 90%, 80%, 0)`)
    stripeGradient.addColorStop(0.2, `hsla(${mainHue + 30}, 90%, 80%, 0.3)`)
    stripeGradient.addColorStop(0.4, `hsla(${mainHue + 60}, 90%, 80%, 0.5)`)
    stripeGradient.addColorStop(0.6, `hsla(${mainHue + 90}, 90%, 80%, 0.3)`)
    stripeGradient.addColorStop(0.8, `hsla(${mainHue + 120}, 90%, 80%, 0.2)`)
    stripeGradient.addColorStop(1, `hsla(${mainHue}, 90%, 80%, 0)`)

    ctx.fillStyle = stripeGradient
    ctx.globalCompositeOperation = "overlay"
    ctx.beginPath()
    ctx.arc(0, 0, radius, 0, Math.PI * 2)
    ctx.fill()

    // 重置混合模式
    ctx.globalCompositeOperation = "source-over"

    // 🔮 第四层：高光反射
    const highlightGradient = ctx.createRadialGradient(
      -radius * 0.4,
      -radius * 0.4,
      0,
      -radius * 0.4,
      -radius * 0.4,
      radius * 0.7,
    )
    highlightGradient.addColorStop(0, "rgba(255,255,255,0.9)")
    highlightGradient.addColorStop(0.3, "rgba(255,255,255,0.5)")
    highlightGradient.addColorStop(0.7, "rgba(255,255,255,0.2)")
    highlightGradient.addColorStop(1, "rgba(255,255,255,0)")

    ctx.fillStyle = highlightGradient
    ctx.beginPath()
    ctx.arc(0, 0, radius, 0, Math.PI * 2)
    ctx.fill()

    // 🔮 第五层：边缘光晕
    const glowIntensity = 0.3 + Math.sin(bubbleTime * 2) * 0.2
    const edgeGradient = ctx.createRadialGradient(0, 0, radius * 0.8, 0, 0, radius)
    edgeGradient.addColorStop(0, "rgba(255,255,255,0)")
    edgeGradient.addColorStop(0.9, "rgba(255,255,255,0)")
    edgeGradient.addColorStop(1, `rgba(255,255,255,${glowIntensity})`)

    ctx.fillStyle = edgeGradient
    ctx.beginPath()
    ctx.arc(0, 0, radius, 0, Math.PI * 2)
    ctx.fill()

    ctx.restore() // 恢复到球体绘制前的状态

    // 🎯 文字独立绘制 - 优化字体清晰度
    ctx.save()
    ctx.globalAlpha = alpha
    ctx.translate(x, y)

    // 优化字体渲染设置
    const dpr = window.devicePixelRatio || 1
    ctx.fillStyle = "#000000" // 纯黑色
    const baseFontSize = Math.max(16, size * 0.2)
    const fontSize = Math.round(baseFontSize * dpr) / dpr // 确保整数像素

    // 使用更现代柔和的字体，但保持清晰
    ctx.font = `600 ${fontSize}px "SF Pro Display", "Inter", "Segoe UI", -apple-system, BlinkMacSystemFont, sans-serif`
    ctx.textAlign = "center"
    ctx.textBaseline = "middle"

    // 启用最佳文字渲染
    ;(ctx as any).textRenderingOptimization = "optimizeQuality"

    // 内容选择逻辑保持不变
    let displayText = bubble.text
    if (size < 65) {
      displayText = displayText.length > 4 ? "💖" : displayText.slice(0, 2)
    }

    // 宽度限制
    const textMetrics = ctx.measureText(displayText)
    const maxWidth = size * 0.8
    if (textMetrics.width > maxWidth) {
      const scaleFactor = maxWidth / textMetrics.width
      const adjustedFontSize = Math.round(fontSize * scaleFactor * dpr) / dpr
      ctx.font = `600 ${adjustedFontSize}px "SF Pro Display", "Inter", "Segoe UI", -apple-system, BlinkMacSystemFont, sans-serif`
    }

    // 绘制文字
    ctx.fillText(displayText, 0, 0)
    ctx.restore()
  } catch (error) {
    console.error('Error in drawBubble:', error)
    setHasError(true)
  }
  }, [])

  const render = useCallback(() => {
    try {
      const canvas = canvasRef.current
      const wrapper = wrapperRef.current
      const ctx = canvas?.getContext("2d")
      if (!canvas || !ctx || !wrapper) return

      const rect = wrapper.getBoundingClientRect()
      const dpr = window.devicePixelRatio || 1

      ctx.setTransform(dpr, 0, 0, dpr, 0, 0)
      ctx.clearRect(0, 0, rect.width, rect.height)

      updatePhysics()

      // 🎯 8. zIndex 逻辑保留，但渲染时大泡放后面
      const sortedBubbles = [...bubblesRef.current].sort((a, b) => {
        // 大泡在后面（先渲染），小泡在前面（后渲染）
        if (a.size !== b.size) return b.size - a.size
        return a.zIndex - b.zIndex
      })
      sortedBubbles.forEach((bubble) => drawBubble(ctx, bubble))

      // 🎯 底部提示文字 - 使用背景和更好的对比度
      ctx.save()

      // 添加半透明背景
      const tipText = "Click a bubble to use message ✨"
      ctx.font = "bold 13px -apple-system, BlinkMacSystemFont, sans-serif"
      ctx.textAlign = "right"
      const textMetrics = ctx.measureText(tipText)
      const padding = 8
      const bgX = rect.width - textMetrics.width - padding - 4
      const bgY = rect.height - 25
      const bgWidth = textMetrics.width + padding * 2
      const bgHeight = 20

      // 绘制背景
      ctx.fillStyle = "rgba(0,0,0,0.7)"
      ctx.roundRect(bgX, bgY, bgWidth, bgHeight, 10)
      ctx.fill()

      // 绘制文字
      ctx.fillStyle = "#FFB6C1" // 使用粉色，与爱心主题一致
      ctx.strokeStyle = "rgba(0,0,0,0.9)"
      ctx.lineWidth = 1.5
      ctx.strokeText(tipText, rect.width - 12, rect.height - 10)
      ctx.fillText(tipText, rect.width - 12, rect.height - 10)

      ctx.restore()

      animationRef.current = requestAnimationFrame(render)
    } catch (error) {
      console.error('Error in render function:', error)
      setHasError(true)
      // 停止动画循环
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current)
        animationRef.current = 0
      }
    }
  }, [updatePhysics, drawBubble])

  const handleCanvasClick = useCallback(
    (e: React.MouseEvent<HTMLCanvasElement>) => {
      try {
        const wrapper = wrapperRef.current
        if (!wrapper) return
        const rect = wrapper.getBoundingClientRect()
        const x = e.clientX - rect.left
        const y = e.clientY - rect.top

        // 优先点击小泡（在前面的）
        const sortedBubbles = [...bubblesRef.current].filter((h) => !h.isDisappearing).sort((a, b) => a.size - b.size) // 小的在前

        const clickedBubble = sortedBubbles.find((bubble) => {
          const dx = x - bubble.x
          const dy = y - bubble.y
          return Math.sqrt(dx * dx + dy * dy) < bubble.size / 2
        })

        if (clickedBubble) {
          onSelectText(clickedBubble.longText) // 使用长文本
          clickedBubble.isDisappearing = true
          clickedBubble.clickAnimation = 1
        }
      } catch (error) {
        console.error('Error in handleCanvasClick:', error)
        setHasError(true)
      }
    },
    [onSelectText],
  )

  const handleMouseMove = useCallback((e: React.MouseEvent<HTMLCanvasElement>) => {
    const wrapper = wrapperRef.current
    if (!wrapper) return
    const rect = wrapper.getBoundingClientRect()
    mouseRef.current = { x: e.clientX - rect.left, y: e.clientY - rect.top, isInside: true }
  }, [])

  const handleMouseLeave = useCallback(() => {
    mouseRef.current.isInside = false
  }, [])

  useEffect(() => {
    try {
      const canvas = canvasRef.current
      const wrapper = wrapperRef.current
      if (!canvas || !wrapper) return

      const resizeObserver = new ResizeObserver(() => {
        try {
          // 在收缩状态下不执行Canvas重新计算
          if (isCollapsed) return
          
          const rect = wrapper.getBoundingClientRect()
          const dpr = window.devicePixelRatio || 1
          
          canvas.width = rect.width * dpr
          canvas.height = rect.height * dpr
          canvas.style.width = `${rect.width}px`
          canvas.style.height = `${rect.height}px`
          const ctx = canvas.getContext("2d")
          if (ctx) {
            ctx.setTransform(dpr, 0, 0, dpr, 0, 0)
            ctx.imageSmoothingEnabled = true
            ctx.imageSmoothingQuality = "high"
          }
          if (!isInitialized) {
            initializeBubbles()
          } else {
            adjustBubbleCount(rect)
          }
        } catch (error) {
          console.error('Error in resize observer:', error)
          setHasError(true)
        }
      })
      resizeObserver.observe(wrapper)

      return () => {
        resizeObserver.disconnect()
        if (animationRef.current) cancelAnimationFrame(animationRef.current)
      }
    } catch (error) {
      console.error('Error in resize effect:', error)
      setHasError(true)
    }
  }, [initializeBubbles, isInitialized, adjustBubbleCount, isCollapsed])

  useEffect(() => {
    try {
      // 只在非收缩状态下启动动画
      if (isInitialized && !isCollapsed) {
        animationRef.current = requestAnimationFrame(render)
      } else {
        // 收缩状态下停止动画
        if (animationRef.current) {
          cancelAnimationFrame(animationRef.current)
          animationRef.current = 0
        }
      }
      return () => {
        if (animationRef.current) cancelAnimationFrame(animationRef.current)
      }
    } catch (error) {
      console.error('Error in animation effect:', error)
      setHasError(true)
    }
  }, [isInitialized, render, isCollapsed])

  // 当 isCollapsed 状态变化时，重新计算 Canvas 尺寸
  useEffect(() => {
    const canvas = canvasRef.current
    const wrapper = wrapperRef.current
    if (!canvas || !wrapper || isCollapsed) return

    // 延迟一点时间让CSS动画完成后再重新计算尺寸
    const timeoutId = setTimeout(() => {
      const rect = wrapper.getBoundingClientRect()
      const dpr = window.devicePixelRatio || 1
      
      // 重新设置Canvas尺寸
      canvas.width = rect.width * dpr
      canvas.height = rect.height * dpr
      canvas.style.width = `${rect.width}px`
      canvas.style.height = `${rect.height}px`
      
      const ctx = canvas.getContext("2d")
      if (ctx) {
        ctx.setTransform(dpr, 0, 0, dpr, 0, 0)
        ctx.imageSmoothingEnabled = true
        ctx.imageSmoothingQuality = "high"
      }

      // 如果还没初始化，先初始化泡泡
      if (!isInitialized) {
        initializeBubbles()
      } else {
        // 已初始化则重新调整泡泡数量和位置
        adjustBubbleCount(rect)
      }
    }, 350) // 稍微延迟等待CSS动画完成

    return () => clearTimeout(timeoutId)
  }, [isCollapsed, isInitialized, adjustBubbleCount, initializeBubbles])

  // 如果有错误，显示错误状态
  if (hasError) {
    return (
      <div ref={wrapperRef} className={cn("relative w-full h-full", className)}>
        <div className="absolute inset-0 w-full h-full bg-gradient-to-br from-gray-900/80 to-gray-800/60 rounded-lg border border-gray-700/30 flex items-center justify-center">
          <div className="text-center">
            <div className="text-red-400 text-sm mb-2">
              {!isCanvasSupported ? "💔 Canvas not supported" : "💔 Quick messages unavailable"}
            </div>
            {isCanvasSupported && (
              <button
                onClick={() => {
                  setHasError(false)
                  setIsInitialized(false)
                  // 重新初始化
                  setTimeout(() => {
                    const wrapper = wrapperRef.current
                    if (wrapper) {
                      const rect = wrapper.getBoundingClientRect()
                      if (rect.width > 0 && rect.height > 0) {
                        initializeBubbles()
                      }
                    }
                  }, 100)
                }}
                className="text-pink-300 text-xs hover:text-pink-200 underline"
              >
                Try again
              </button>
            )}
          </div>
        </div>
      </div>
    )
  }

  return (
    <div ref={wrapperRef} className={cn("relative w-full h-full", className)}>
      {isCollapsed ? (
        // 收缩状态：显示简单的文本提示，整个区域可点击
        <div 
          className="absolute inset-0 w-full h-full bg-gradient-to-br from-gray-900/80 to-gray-800/60 rounded-lg border border-gray-700/30 flex items-center justify-center cursor-pointer hover:from-gray-800/90 hover:to-gray-700/70 transition-all duration-200"
          onClick={onToggleCollapse}
        >
          <span className="text-pink-300 text-sm font-medium">💖 Click to expand quick messages</span>
        </div>
      ) : (
        // 展开状态：显示Canvas泡泡
        <canvas
          ref={canvasRef}
          className="absolute inset-0 w-full h-full bg-gradient-to-br from-gray-900/80 to-gray-800/60 rounded-lg border border-gray-700/30 cursor-pointer"
          onClick={handleCanvasClick}
          onMouseMove={handleMouseMove}
          onMouseLeave={handleMouseLeave}
        />
      )}

      {/* 收起/展开按钮 */}
      <button
        onClick={onToggleCollapse}
        className="absolute bottom-2 left-2 min-w-[44px] min-h-[44px] w-10 h-10 sm:w-8 sm:h-8 bg-gray-800/90 hover:bg-gray-700/90 rounded-full flex items-center justify-center transition-colors border border-gray-600/50 hover:border-gray-500/50"
        title={isCollapsed ? "Expand bubbles" : "Collapse bubbles"}
      >
        <svg
          className={`w-4 h-4 text-gray-300 hover:text-white transition-all duration-200 ${
            isCollapsed ? 'rotate-180' : ''
          }`}
          fill="currentColor"
          viewBox="0 0 20 20"
        >
          <path
            fillRule="evenodd"
            d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
            clipRule="evenodd"
          />
        </svg>
      </button>
    </div>
  )
}
