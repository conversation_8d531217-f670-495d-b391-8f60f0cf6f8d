import { useCallback } from 'react';
import { ChatAPI } from '@/lib/api/chat';
import type { ChatHistoryItem } from '@/lib/api/types';
import type { Message } from './useMessages';

export const useChatHistory = () => {
  // 统一的历史消息转换函数
  const convertHistoryToMessages = useCallback((history: ChatHistoryItem[]): Message[] => {
    const convertedMessages: Message[] = [];

    history.forEach((item) => {
      // 添加用户消息
      convertedMessages.push({
        id: item.id * 2 - 1,
        sender: 'user',
        text: item.message,
        timestamp: item.createdAt,
      });

      // 添加AI回复（跳过占位符）
      if (
        item.response &&
        item.response !== '[流式响应]' &&
        item.response.trim() !== ''
      ) {
        // 尝试解析response为JSON格式的完整数据结构
        let aiMessage: Message = {
          id: item.id * 2,
          sender: 'ai',
          text: item.response,
          timestamp: item.createdAt,
        };

        try {
          // 检查是否为JSON格式的完整数据结构
          const responseData = JSON.parse(item.response);

          if (
            responseData &&
            typeof responseData === 'object' &&
            responseData.type
          ) {
            // 如果是完整的数据结构，根据类型创建对应的消息
            aiMessage = {
              id: item.id * 2,
              sender: 'ai',
              text: responseData.text || '',
              timestamp: item.createdAt,
              ...(responseData.type === 'image'
                ? {
                    hasImage: true,
                    imageSrc: responseData.url,
                  }
                : responseData.type === 'voice'
                ? {
                    hasAudio: true,
                    audioSrc: responseData.url,
                    audioDuration: responseData.audioDuration || 10,
                  }
                : {}),
            };

            console.log('🎯 成功解析历史记录中的多媒体消息:', {
              类型: responseData.type,
              文本: responseData.text,
              URL: responseData.url,
            });
          }
        } catch (parseError) {
          // 如果解析失败，说明是纯文本response，保持原有逻辑
          console.log('📝 历史记录为纯文本格式:', item.response);
        }

        convertedMessages.push(aiMessage);
      }
    });

    return convertedMessages;
  }, []);

  // 加载聊天历史
  const loadChatHistory = useCallback(async (characterId: number): Promise<Message[]> => {
    try {
      const history = await ChatAPI.getChatHistory(characterId);
      console.log('成功加载聊天历史:', history);
      return convertHistoryToMessages(history);
    } catch (error) {
      console.error('加载历史消息失败:', error);
      throw error;
    }
  }, [convertHistoryToMessages]);

  // 重新加载历史记录
  const reloadChatHistory = useCallback(async (
    characterId: number,
    replaceMessages: (messages: Message[]) => void
  ) => {
    try {
      const convertedMessages = await loadChatHistory(characterId);
      replaceMessages(convertedMessages);
    } catch (error) {
      console.error('重新加载聊天历史失败:', error);
    }
  }, [loadChatHistory]);

  // 清除聊天记录
  const clearChatHistory = useCallback(async (
    characterId: number,
    clearMessages: () => void
  ): Promise<boolean> => {
    try {
      await ChatAPI.clearChatHistory(characterId);
      clearMessages();
      console.log('聊天记录已清除');
      return true;
    } catch (error) {
      console.error('清除聊天记录失败:', error);
      return false;
    }
  }, []);

  return {
    convertHistoryToMessages,
    loadChatHistory,
    reloadChatHistory,
    clearChatHistory
  };
};