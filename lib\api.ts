import { CURRENT_API, COMMON_HEADERS, API_PATHS } from './api/config';

interface LoginResponse {
  accessToken: string;
}

interface ChatResponse {
  message: string;
  // Add other response fields as needed
}

interface FollowCharacterRequest {
  characterId: number;
  follow: boolean;
}

interface FollowCharacterResponse {
  success: boolean;
  data: null;
  message: string;
}

const API_BASE_URL = CURRENT_API.MAIN;

export async function login(email: string, password: string): Promise<string> {
  const response = await fetch(`${API_BASE_URL}/auth/login`, {
    method: 'POST',
    headers: {
      ...COMMON_HEADERS,
    },
    body: JSON.stringify({ email, password }),
  });

  if (!response.ok) {
    throw new Error('Login failed');
  }

  const data: LoginResponse = await response.json();
  return data.accessToken;
}

export async function sendChatMessage(
  accessToken: string,
  message: string,
  chatId: string,
  characterId: number
): Promise<ChatResponse> {
  const response = await fetch(`${API_BASE_URL}/chat`, {
    method: 'POST',
    headers: {
      ...COMMON_HEADERS,
      Authorization: `Bearer ${accessToken}`,
    },
    body: JSON.stringify({
      characterId,
      message,
      chatId,
    }),
  });

  if (!response.ok) {
    throw new Error('Failed to send message');
  }

  return response.json();
}

export async function sendChatMessageStream(
  accessToken: string,
  message: string,
  chatId: string,
  characterId: number,
  onChunk: (content: string) => void,
  onComplete: () => void,
  onError: (error: string) => void
): Promise<() => void> {
  const controller = new AbortController();

  try {
    // 获取真实的用户数据
    const userData = localStorage.getItem('user');
    let userId = '0'; // 默认值

    if (userData) {
      try {
        const user = JSON.parse(userData);
        // 假设用户数据中有id字段，如果没有可以用email的hash或其他唯一标识
        userId = user.id?.toString() || user.email?.split('@')[0] || '0';
        console.log(`🔍 获取到用户ID: ${userId}`);
      } catch (e) {
        console.error('解析用户数据失败:', e);
      }
    }

    // 直接调用RAG服务的认证流式接口
    const response = await fetch(
      `${CURRENT_API.RAG}/chat/message/stream/authenticated`,
      {
        method: 'POST',
        headers: {
          ...COMMON_HEADERS,
          Authorization: `Bearer ${accessToken}`,
        },
        body: JSON.stringify({
          user_id: userId, // 使用真实的用户ID
          session_id: `user_${userId}_character_${characterId}`, // 使用真实的session_id
          message: message,
        }),
        signal: controller.signal,
      }
    );

    if (!response.ok) {
      throw new Error('流式请求失败');
    }

    const reader = response.body?.getReader();
    if (!reader) {
      throw new Error('无法读取响应流');
    }

    const decoder = new TextDecoder();
    let buffer = '';

    while (true) {
      const { done, value } = await reader.read();
      if (done) break;

      buffer += decoder.decode(value, { stream: true });
      const lines = buffer.split('\n');
      buffer = lines.pop() || '';

      for (const line of lines) {
        if (line.startsWith('data: ')) {
          const content = line.slice(6);
          if (content === '[DONE]') {
            onComplete();
            return () => controller.abort();
          }
          if (content.trim()) {
            onChunk(content);
          }
        }
      }
    }

    onComplete();
  } catch (error) {
    onError(error instanceof Error ? error.message : String(error));
  }

  return () => controller.abort();
}

// 新的流式聊天接口（使用新的API格式）
export async function sendChatMessageStreamV2(
  accessToken: string,
  message: string,
  characterId: number,
  onChunk: (content: string) => void,
  onComplete: () => void,
  onError: (error: string) => void
): Promise<() => void> {
  const controller = new AbortController();

  try {
    console.log('🚀 使用新接口发送流式消息:', {
      characterId,
      message,
      endpoint: `${API_BASE_URL}${API_PATHS.CHAT.STREAM_V2}`,
    });

    // 调用新的流式接口
    const response = await fetch(`${API_BASE_URL}${API_PATHS.CHAT.STREAM_V2}`, {
      method: 'POST',
      headers: {
        ...COMMON_HEADERS,
        Authorization: `Bearer ${accessToken}`,
      },
      body: JSON.stringify({
        characterId: characterId,
        message: message,
        msgType: 'text',
        // response 参数可选，暂时不传
      }),
      signal: controller.signal,
    });

    if (!response.ok) {
      throw new Error(`新接口流式请求失败: ${response.status}`);
    }

    const reader = response.body?.getReader();
    if (!reader) {
      throw new Error('无法读取新接口响应流');
    }

    const decoder = new TextDecoder();
    let buffer = '';

    while (true) {
      const { done, value } = await reader.read();
      if (done) break;

      buffer += decoder.decode(value, { stream: true });
      const lines = buffer.split('\n');
      buffer = lines.pop() || '';

      for (const line of lines) {
        if (line.startsWith('data: ')) {
          const content = line.slice(6);
          if (content === '[DONE]') {
            onComplete();
            return () => controller.abort();
          }
          if (content.trim()) {
            onChunk(content);
          }
        }
      }
    }

    onComplete();
  } catch (error) {
    console.error('❌ 新接口流式处理失败:', error);
    onError(error instanceof Error ? error.message : String(error));
  }

  return () => controller.abort();
}

// 定义聊天历史类型（匹配后端）
export interface ChatHistoryItem {
  id: number;
  userId: number;
  characterId: number;
  message: string;
  response: string;
  msgType: 'text' | 'image' | 'voice'; // 改为小写
  createdAt: string;
}

// 添加后端响应的包装类型
export interface ChatHistoryResponse {
  success: boolean;
  histories: ChatHistoryItem[];
  totalCount: number;
  error?: string;
}

// 获取聊天历史
export async function getChatHistory(
  accessToken: string,
  characterId: number
): Promise<ChatHistoryItem[]> {
  const response = await fetch(`${API_BASE_URL}/chat/history/${characterId}`, {
    method: 'GET',
    headers: {
      ...COMMON_HEADERS,
      Authorization: `Bearer ${accessToken}`,
    },
  });

  if (!response.ok) {
    const errorText = await response.text();
    throw new Error(
      `Failed to get chat history: ${response.status} ${errorText}`
    );
  }

  const data: ChatHistoryResponse = await response.json();

  if (!data.success) {
    throw new Error(data.error || 'Failed to get chat history');
  }

  return data.histories || [];
}

// 清空聊天历史
export async function clearChatHistory(
  accessToken: string,
  characterId: number
): Promise<{ success: boolean; error?: string }> {
  try {
    const response = await fetch(
      `${API_BASE_URL}/chat/history/${characterId}`,
      {
        method: 'DELETE',
        headers: {
          ...COMMON_HEADERS,
          Authorization: `Bearer ${accessToken}`,
        },
      }
    );

    if (!response.ok) {
      throw new Error(`清空聊天历史失败: ${response.status}`);
    }

    return response.json();
  } catch (error) {
    console.error('Error clearing chat history:', error);
    throw error;
  }
}

// 添加获取用户ID的辅助函数
export function getCurrentUserId(): string {
  try {
    if (typeof window === 'undefined') {
      return '0';
    }

    const userData = localStorage.getItem('user');
    if (!userData) return '0';

    const user = JSON.parse(userData);

    // 按优先级尝试获取用户ID
    if (user.id) return user.id.toString();
    if (user.userId) return user.userId.toString();
    if (user.email) {
      // 使用email前缀作为用户ID（临时方案）
      return user.email.split('@')[0];
    }

    return '0';
  } catch (error) {
    console.error('获取用户ID失败:', error);
    return '0';
  }
}

// 生成session_id的辅助函数
export function generateSessionId(characterId: number): string {
  const userId = getCurrentUserId();
  return `user_${userId}_character_${characterId}`;
}

// 关注/取关角色
export async function followCharacter(
  accessToken: string,
  characterId: number,
  follow: boolean
): Promise<FollowCharacterResponse> {
  try {
    const response = await fetch(
      `${API_BASE_URL}${API_PATHS.CHARACTERS.FOLLOW}`,
      {
        method: 'POST',
        headers: {
          ...COMMON_HEADERS,
          Authorization: `Bearer ${accessToken}`,
        },
        body: JSON.stringify({
          characterId,
          follow,
        }),
      }
    );

    if (!response.ok) {
      throw new Error(`关注角色失败: ${response.status}`);
    }

    const data: FollowCharacterResponse = await response.json();
    return data;
  } catch (error) {
    console.error('Error following character:', error);
    throw error;
  }
}

// 聊天发送请求接口
export interface ChatSendRequest {
  characterId: number;
  message: string;
  msgType: 'text' | 'image' | 'voice' | 'call' | 'video';
  response?: string;
}

// 聊天发送响应接口
export interface ChatSendResponse {
  success: boolean;
  data: null;
  message: string;
}

// 使用量扣除请求接口
export interface ChatUsageRequest {
  msgType: 'text' | 'image' | 'voice' | 'call' | 'video';
  amount: number;
}

// 使用量扣除响应接口
export interface ChatUsageResponse {
  success: boolean;
  data: null;
  message: string;
}

// 发送聊天消息进行计数
export async function sendChatForCounting(
  accessToken: string,
  request: ChatSendRequest
): Promise<ChatSendResponse> {
  try {
    console.log('🚀 调用聊天计数API:', request);

    const response = await fetch(`${API_BASE_URL}${API_PATHS.CHAT.SEND}`, {
      method: 'POST',
      headers: {
        ...COMMON_HEADERS,
        Authorization: `Bearer ${accessToken}`,
      },
      body: JSON.stringify(request),
    });
    console.log('🔍 所有响应头:', Array.from(response.headers.entries()));

    if (!response.ok) {
      // 检查是否是余额不足错误（和sendUsageDeduction一样的逻辑）
      if (response.status === 400) {
        // 尝试不同的响应头获取方式
        const errorCode =
          response.headers.get('x-error-code') ||
          response.headers.get('X-Error-Code');
        const errorMessage =
          response.headers.get('x-error-message') ||
          response.headers.get('X-Error-Message');

        // 打印所有响应头进行调试
        console.log('🔍 所有响应头:', Array.from(response.headers.entries()));
        console.log('🔍 聊天计数错误详情:', {
          status: response.status,
          errorCode,
          errorMessage,
        });

        // 如果是额度不足错误，抛出特殊错误类型用于支付弹窗
        if (
          errorCode === 'COMMON_ERROR' &&
          errorMessage === 'You have already run out of usage.'
        ) {
          throw new Error('NEED_SUBSCRIPTION');
        }
      }

      const errorText = await response.text();
      console.error('❌ 聊天计数API调用失败:', response.status, errorText);
      throw new Error(`聊天计数失败: ${response.status} ${errorText}`);
    }

    const data: ChatSendResponse = await response.json();
    console.log('✅ 聊天计数API调用成功:', data);

    return data;
  } catch (error) {
    console.error('❌ 聊天计数API调用错误:', error);
    throw error;
  }
}

// 发送使用量扣除请求（用于通话等实时扣费）
export async function sendUsageDeduction(
  accessToken: string,
  request: ChatUsageRequest
): Promise<ChatUsageResponse> {
  try {
    console.log('🚀 调用使用量扣除API:', request);

    const response = await fetch(`${API_BASE_URL}/chat/usage`, {
      method: 'POST',
      headers: {
        ...COMMON_HEADERS,
        Authorization: `Bearer ${accessToken}`,
      },
      body: JSON.stringify(request),
    });

    console.log('🔍 所有响应头:', Array.from(response.headers.entries()));
    if (!response.ok) {
      // 检查是否是余额不足错误（和之前send接口一样的逻辑）
      if (response.status === 400) {
        const errorCode = response.headers.get('X-Error-Code');
        const errorMessage = response.headers.get('X-Error-Message');

        console.log('🔍 使用量扣除错误详情:', {
          status: response.status,
          errorCode,
          errorMessage,
        });

        // 如果是额度不足错误，抛出特殊错误类型用于通话停止和支付弹窗
        if (
          errorCode === 'COMMON_ERROR' &&
          errorMessage === 'You have already run out of usage.'
        ) {
          throw new Error('NEED_SUBSCRIPTION');
        }
      }

      const errorText = await response.text();
      console.error('❌ 使用量扣除API调用失败:', response.status, errorText);
      throw new Error(`使用量扣除失败: ${response.status} ${errorText}`);
    }

    const result: ChatUsageResponse = await response.json();
    console.log('✅ 使用量扣除API调用成功:', result);

    return result;
  } catch (error) {
    console.error('❌ 使用量扣除API调用失败:', error);
    throw error;
  }
}

// 导出类型
export type { FollowCharacterRequest, FollowCharacterResponse };
