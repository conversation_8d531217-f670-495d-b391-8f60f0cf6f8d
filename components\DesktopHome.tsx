'use client';

import Link from 'next/link';
import { Search } from 'lucide-react';
import Sidebar from '@/components/sidebar';
import CharacterCard from '@/components/character-card';
import TrendingList from '@/components/trending-list';
import { Button } from '@/components/ui/button';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { useState, useCallback, useMemo } from 'react';
import SmartImage from '@/components/SmartImage';
import { getTrendingCharactersSync, getFilterTags } from '@/lib/characters';
import { followCharacter } from '@/lib/api';
import { getAvatarUrl } from '@/lib/image-utils';
import { searchCharacters, debounce } from '@/lib/utils';

// 引入新的性能优化hooks
import { useCharactersData } from '@/hooks/useCharactersData';
import { useRecentChats } from '@/hooks/useRecentChats';
import { useFilterTags } from '@/hooks/useLabels';
import UserPlanHeader from '@/components/UserPlanHeader';
import { useUser } from '@/contexts/UserContext';

export default function DesktopHome() {
  // UI状态
  const [activeGender, setActiveGender] = useState<'guys' | 'girls'>('guys');
  const [activeTag, setActiveTag] = useState('All');
  const [searchTerm, setSearchTerm] = useState('');
  const [followStates, setFollowStates] = useState<
    Record<string, { isFollowing: boolean; isLoading: boolean }>
  >({});

  // 使用优化后的hooks
  const { isLoggedIn, user, isLoading: authLoading } = useUser();

  const {
    characters,
    loading: charactersLoading,
    error: charactersError,
  } = useCharactersData({
    gender: activeGender === 'guys' ? 'male' : 'female',
    activeTag: activeTag,
    pageSize: 20,
  });

  const {
    recentChats,
    loading: recentChatsLoading,
    hasRecentChats,
  } = useRecentChats(isLoggedIn, 3);

  // 获取动态标签数据
  const {
    filterTags,
    loading: tagsLoading,
    error: tagsError,
  } = useFilterTags(activeGender === 'guys' ? 'male' : 'female');

  // 搜索过滤后的角色列表
  const filteredCharacters = useMemo(() => {
    if (!characters || characters.length === 0) return [];
    return searchCharacters(characters, searchTerm);
  }, [characters, searchTerm]);

  // 防抖搜索处理函数
  const debouncedSearch = useMemo(
    () => debounce((value: string) => {
      setSearchTerm(value);
    }, 300),
    []
  );

  // 创建关注处理函数
  const handleFollowToggle = useCallback(
    async (characterId: string | number) => {
      if (!isLoggedIn) {
        console.error('用户未登录');
        return;
      }

      const token = localStorage.getItem('token');
      if (!token) {
        console.error('未找到认证token');
        return;
      }

      const id = characterId.toString();
      const currentState = followStates[id] || {
        isFollowing: false,
        isLoading: false,
      };

      if (currentState.isLoading) {
        return; // 防止重复点击
      }

      try {
        // 设置加载状态
        setFollowStates((prev) => ({
          ...prev,
          [id]: { ...currentState, isLoading: true },
        }));

        // 调用关注/取关API
        const response = await followCharacter(
          token,
          parseInt(id),
          !currentState.isFollowing
        );

        if (response.success) {
          // 更新关注状态
          setFollowStates((prev) => ({
            ...prev,
            [id]: {
              isFollowing: !currentState.isFollowing,
              isLoading: false,
            },
          }));
          
          console.log('关注状态更新成功:', response.message);
        } else {
          console.error('关注操作失败:', response.message);
          setFollowStates((prev) => ({
            ...prev,
            [id]: { ...currentState, isLoading: false },
          }));
        }
      } catch (error) {
        console.error('关注操作出错:', error);
        setFollowStates((prev) => ({
          ...prev,
          [id]: { ...currentState, isLoading: false },
        }));
      }
    },
    [followStates, isLoggedIn]
  );

  // 认证加载中显示loading
  if (authLoading) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="text-white">Loading...</div>
      </div>
    );
  }

  // 使用同步版本获取趋势角色（暂时保持向后兼容）
  const trendingCharacters = getTrendingCharactersSync('male', 5);
  const trendingFemaleCharacters = getTrendingCharactersSync('female', 5);

  return (
    <div className="flex min-h-screen">
      <Sidebar />
      <main className="flex-1 overflow-auto">
        <div className="px-2 py-6">
          {/* Top Navigation */}
          <div className="flex justify-between items-center mb-8">
            <div className="relative w-96">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <input
                type="text"
                placeholder="Search characters..."
                className="w-full pl-10 pr-4 py-2 bg-[#1a0a24] border border-[#3a1a44] rounded-lg text-white placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-pink-500"
                onChange={(e) => debouncedSearch(e.target.value)}
              />
            </div>
            <UserPlanHeader showLogout={true} />
          </div>

          {/* Recent Chats Section (only shown if user has recent chats) */}
          {hasRecentChats && (
            <div className="mb-8">
              <h2 className="text-xl font-semibold mb-3">Recent chat</h2>
              {recentChatsLoading ? (
                <div className="flex justify-center py-4">
                  <div className="text-gray-400">Loading recent chats...</div>
                </div>
              ) : (
                <div className="flex space-x-5">
                  {recentChats.map((character) => (
                    <Link
                      href={`/chat/${character.id}`}
                      key={character.id}
                      className="text-center"
                    >
                      <div className="relative h-20 w-20 rounded-full overflow-hidden mx-auto mb-2 border-2 border-pink-500">
                        <SmartImage
                          src={getAvatarUrl(character.avatarSrc)}
                          alt={character.name}
                          width={80}
                          height={80}
                          className="object-cover"
                        />
                        {/* 显示未读消息数量徽章 */}
                        {character.unreadCount > 0 && (
                          <div className="absolute -top-1 -right-1 bg-red-500 rounded-full h-5 w-5 flex items-center justify-center text-xs font-bold">
                            {character.unreadCount > 99
                              ? '99+'
                              : character.unreadCount}
                          </div>
                        )}
                      </div>
                      <span className="text-base">{character.name}</span>
                    </Link>
                  ))}
                </div>
              )}
            </div>
          )}

          {/* Hero Banner */}
          <div className="relative rounded-xl overflow-hidden mb-8 gradient-bg">
            <div className="flex flex-col md:flex-row items-center p-3 md:p-5">
              <div className="md:w-1/2 mb-3 md:mb-0">
                <h2 className="text-xl md:text-2xl lg:text-3xl font-bold mb-2">
                  Welcome to the #1 Playground for AI Companions
                </h2>
                <p className="text-gray-300 mb-3 max-w-lg text-sm">
                  Chat with your ideal AI boyfriend or girlfriend. Laugh, connect, and share your day.
                </p>
                <Link href="/create-lover">
                  <Button className="bg-pink-500 hover:bg-pink-600 text-white px-4 py-3 h-auto rounded-full text-sm">
                    <span className="mr-2">+</span> START CREATING NOW
                  </Button>
                </Link>
              </div>
              <div className="md:w-1/2 relative h-28 md:h-40 overflow-hidden rounded-lg">
                <div className="grid grid-cols-3 gap-2 h-full">
                  {[
                    '/banner-image2 .JPG',
                    '/banner-image .JPG',
                    '/banner-image1.PNG'
                  ].map((imagePath, i) => (
                    <div key={i} className="overflow-hidden rounded-md">
                      <SmartImage
                        src={imagePath}
                        alt={`AI character banner ${i + 1}`}
                        width={800}
                        height={600}
                        className="object-cover h-full w-full hover:scale-105 transition-transform duration-300"
                      />
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>

          {/* Gender Tabs */}
          <Tabs
            defaultValue="guys"
            className="mb-6"
            onValueChange={(value) => {
              setActiveGender(value as 'guys' | 'girls');
              setActiveTag('All'); // 切换性别时重置标签为All
            }}
          >
            <TabsList className="bg-[#1a0a24] p-1 rounded-full w-fit">
              <TabsTrigger
                value="girls"
                className="rounded-full px-8 py-2.5 text-base data-[state=active]:bg-pink-500"
              >
                👩 Female
              </TabsTrigger>
              <TabsTrigger
                value="guys"
                className="rounded-full px-8 py-2.5 text-base data-[state=active]:bg-pink-500"
              >
                🧑‍🦱 Male
              </TabsTrigger>
            </TabsList>
          </Tabs>

          {/* Filter Tags */}
          <div className="flex overflow-x-auto pb-4 mb-6 filter-tags-scroll">
            <div className="flex space-x-3 min-w-max">
              {tagsLoading ? (
                // 标签加载中状态
                <div className="flex space-x-3">
                  {[1, 2, 3, 4, 5].map((i) => (
                    <div
                      key={i}
                      className="h-10 w-20 bg-[#1a0a24] rounded-full animate-pulse"
                    />
                  ))}
                </div>
              ) : tagsError ? (
                // 标签加载失败，使用fallback
                getFilterTags(activeGender === 'guys' ? 'male' : 'female').map(
                  (tag: string, index: number) => (
                    <Badge
                      key={index}
                      variant={tag === activeTag ? 'default' : 'outline'}
                      className={`whitespace-nowrap px-5 py-2.5 text-base rounded-full cursor-pointer ${
                        tag === activeTag
                          ? 'bg-pink-500 hover:bg-pink-600'
                          : 'bg-[#1a0a24] hover:bg-[#2a1a34]'
                      }`}
                      onClick={() => setActiveTag(tag)}
                    >
                      {tag === 'All' && <span className="mr-1">⭐</span>}
                      {tag !== 'All' && <span className="mr-1">🏷️</span>}
                      {tag}
                    </Badge>
                  )
                )
              ) : (
                // 使用API数据
                filterTags.map((tag: string, index: number) => (
                  <Badge
                    key={index}
                    variant={tag === activeTag ? 'default' : 'outline'}
                    className={`whitespace-nowrap px-5 py-2.5 text-base rounded-full cursor-pointer ${
                      tag === activeTag
                        ? 'bg-pink-500 hover:bg-pink-600'
                        : 'bg-[#1a0a24] hover:bg-[#2a1a34]'
                    }`}
                    onClick={() => setActiveTag(tag)}
                  >
                    {tag === 'All' && <span className="mr-1">⭐</span>}
                    {tag !== 'All' && <span className="mr-1">🏷️</span>}
                    {tag}
                  </Badge>
                ))
              )}
            </div>
          </div>

          {/* Main Content */}
          <div className="flex flex-col md:flex-row gap-4">
            {/* Character Cards */}
            <div className="md:w-3/4">
              {/* 搜索结果统计 */}
              {searchTerm && (
                <div className="mb-4 text-sm text-gray-400">
                  Found {filteredCharacters.length} character{filteredCharacters.length !== 1 ? 's' : ''} 
                  {characters && characters.length > 0 && (
                    <span> out of {characters.length} total</span>
                  )}
                  {searchTerm && (
                    <span> for "{searchTerm}"</span>
                  )}
                </div>
              )}
              
              {charactersLoading ? (
                <div className="flex justify-center items-center py-12">
                  <div className="text-white">Loading character data...</div>
                </div>
              ) : charactersError ? (
                <div className="flex flex-col items-center justify-center py-12">
                  <div className="text-red-400 mb-4">
                    Failed to load character data
                  </div>
                  <div className="text-gray-400 text-sm">{charactersError}</div>
                </div>
              ) : (
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                  {filteredCharacters.map((character) => {
                    const characterId = character.id.toString();
                    const followState = followStates[characterId] || {
                      isFollowing: false,
                      isLoading: false,
                    };

                    return (
                      <CharacterCard
                        key={character.id}
                        character={{
                          ...character,
                          isFollowing: followState.isFollowing,
                          isFollowLoading: followState.isLoading,
                          onFollowToggle: isLoggedIn
                            ? () => handleFollowToggle(character.id)
                            : undefined,
                        }}
                      />
                    );
                  })}
                  {filteredCharacters.length === 0 && (
                    <div className="col-span-full text-center text-gray-400 py-12">
                      {searchTerm ? (
                        <div>
                          <div className="text-lg mb-2">🔍 No characters found</div>
                          <div className="text-sm">
                            No characters match "{searchTerm}". Try a different search term.
                          </div>
                        </div>
                      ) : (
                        <div>
                          <div className="text-lg mb-2">📭 No characters available</div>
                          <div className="text-sm">
                            No characters found for the current filters.
                          </div>
                        </div>
                      )}
                    </div>
                  )}
                </div>
              )}
            </div>

            {/* Trending Sidebar */}
            <div className="md:w-1/4">
              <TrendingList
                characters={
                  activeGender === 'guys'
                    ? trendingCharacters
                    : trendingFemaleCharacters
                }
              />
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}