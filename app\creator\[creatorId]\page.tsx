"use client"

import Image from "next/image"
import Link from "next/link"
import { ArrowLeft, UserPlus } from "lucide-react"
import Sidebar from "@/components/sidebar"
import CharacterCard from "@/components/character-card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { follow<PERSON><PERSON><PERSON> } from "@/lib/api"
import { getFakeStatsForCharacter } from "@/lib/fake-stats"
import React from "react"

// 导入创作者数据管理
import { 
  getDynamicCreatorData, 
  creatorExists, 
  getCreatorForCharacter,
  getAllCreatorIds 
} from "@/lib/creator-data"

// 导入角色API
import { getCharacters, transformBackendCharacter } from "@/lib/api/characters"

export default function CreatorProfilePage({ params }: { params: Promise<{ creatorId: string }> }) {
  const [creatorData, setCreatorData] = React.useState<any>(null)
  const [creatorCharacters, setCreatorCharacters] = React.useState<any[]>([])
  const [isLoading, setIsLoading] = React.useState(true)
  const [charactersLoading, setCharactersLoading] = React.useState(false)
  const [followStates, setFollowStates] = React.useState<Record<string, { isFollowing: boolean; isLoading: boolean }>>({})
  
  // 使用React.use()来解包Promise参数
  const resolvedParams = React.use(params)

  // 加载创作者基础数据
  React.useEffect(() => {
    const loadCreatorData = () => {
      const creatorId = resolvedParams.creatorId
      
      if (!creatorExists(creatorId)) {
        setCreatorData(null)
        setIsLoading(false)
        return
      }

      // 获取创作者基础数据
      const dynamicCreatorData = getDynamicCreatorData(creatorId)
      setCreatorData(dynamicCreatorData)
      setIsLoading(false)
    }

    loadCreatorData()
  }, [resolvedParams.creatorId])

  // 加载创作者的角色数据
  React.useEffect(() => {
    const loadCreatorCharacters = async () => {
      if (!creatorData) return

      setCharactersLoading(true)
      
      try {
        // 获取所有角色数据
        const allCharacters = await Promise.all([
          getCharacters({ gender: 'male', pageSize: 50 }),
          getCharacters({ gender: 'female', pageSize: 50 })
        ])

        // 合并所有角色
        const allChars = [
          ...allCharacters[0].data.characters,
          ...allCharacters[1].data.characters
        ]

        // 过滤出属于当前创作者的角色
        const creatorChars = allChars.filter(char => {
          const assignedCreator = getCreatorForCharacter(char.id)
          return assignedCreator === resolvedParams.creatorId
        })

        // 转换为前端格式
        const transformedChars = creatorChars.map(char => {
          const transformed = transformBackendCharacter(char)
          const fakeStats = getFakeStatsForCharacter(transformed)
          
          return {
            ...transformed,
            chatCount: fakeStats.chatCount,
            likeCount: fakeStats.likeCount,
            followers: fakeStats.followers,
            creator: {
              id: creatorData.id,
              name: creatorData.name,
              likeCount: creatorData.followers
            }
          }
        })

        setCreatorCharacters(transformedChars)
      } catch (error) {
        console.error('获取创作者角色失败:', error)
        setCreatorCharacters([])
      } finally {
        setCharactersLoading(false)
      }
    }

    if (creatorData) {
      loadCreatorCharacters()
    }
  }, [creatorData, resolvedParams.creatorId])

  // 关注处理函数
  const handleFollowToggle = React.useCallback(async (characterId: string | number) => {
    // 检查用户是否登录
    if (typeof window !== 'undefined') {
      const isLoggedIn = localStorage.getItem('isLoggedIn') === 'true';
      if (!isLoggedIn) {
        console.error('用户未登录');
        return;
      }

      const token = localStorage.getItem('token');
      if (!token) {
        console.error('未找到认证token');
        return;
      }

      const id = characterId.toString();
      const currentState = followStates[id] || { isFollowing: false, isLoading: false };

      if (currentState.isLoading) {
        return; // 防止重复点击
      }

      try {
        // 设置加载状态
        setFollowStates(prev => ({
          ...prev,
          [id]: { ...currentState, isLoading: true }
        }));

        // 调用关注/取关API
        const response = await followCharacter(token, parseInt(id), !currentState.isFollowing);

        if (response.success) {
          // 更新关注状态
          setFollowStates(prev => ({
            ...prev,
            [id]: {
              isFollowing: !currentState.isFollowing,
              isLoading: false
            }
          }));
          
          console.log('关注状态更新成功:', response.message);
        } else {
          console.error('关注操作失败:', response.message);
          setFollowStates(prev => ({
            ...prev,
            [id]: { ...currentState, isLoading: false }
          }));
        }
      } catch (error) {
        console.error('关注操作出错:', error);
        setFollowStates(prev => ({
          ...prev,
          [id]: { ...currentState, isLoading: false }
        }));
      }
    }
  }, [followStates]);

  if (isLoading) {
    return (
      <div className="flex min-h-screen">
        <Sidebar />
        <main className="flex-1 p-6 flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-pink-500 mx-auto mb-4"></div>
            <p className="text-white text-lg">Loading creator profile...</p>
          </div>
        </main>
      </div>
    )
  }

  if (!creatorData) {
    return (
      <div className="flex min-h-screen">
        <Sidebar />
        <main className="flex-1 p-6 flex items-center justify-center">
          <p className="text-xl text-gray-400">Creator not found.</p>
        </main>
      </div>
    )
  }

  return (
    <div className="flex min-h-screen">
      {/* PC端侧边栏 - 移动端隐藏 */}
      <div className="hidden md:block">
        <Sidebar />
      </div>
      
      <main className="flex-1 overflow-auto bg-[#0e0314]">
        {/* Header with back button */}
        <div className="p-4 flex items-center">
          <Link href="/">
            <Button
              variant="outline"
              size="icon"
              className="bg-[#1a0a24] border-[#3a1a44] hover:bg-[#2a1a34] text-white mr-4"
            >
              <ArrowLeft className="h-5 w-5" />
            </Button>
          </Link>
        </div>

        {/* Profile Header - 移动端优化，PC端保持原样 */}
        <div className="px-4 md:px-6 pt-2 pb-6">
          <div className="flex flex-col md:flex-row items-start md:items-end justify-between">
            {/* 头像和基本信息 */}
            <div className="flex flex-col sm:flex-row items-start sm:items-end w-full md:w-auto">
              {/* 头像 - 移动端更小，PC端保持原尺寸 */}
              <div className="relative h-24 w-24 sm:h-32 sm:w-32 md:h-40 md:w-40 rounded-full border-4 border-[#0e0314] overflow-hidden bg-gray-600 flex-shrink-0">
                <Image
                  src={creatorData.avatarSrc}
                  alt={creatorData.name}
                  fill
                  className="object-cover"
                />
              </div>
              
              {/* 基本信息 */}
              <div className="mt-3 sm:mt-0 sm:ml-4 mb-2 flex-1">
                <h1 className="text-xl sm:text-2xl md:text-3xl font-bold text-white">{creatorData.name}</h1>
                <div className="flex flex-col sm:flex-row sm:items-center space-y-1 sm:space-y-0 sm:space-x-4 text-sm text-gray-400 mt-1">
                  <span>{creatorData.followers} Followers</span>
                  <span>{creatorData.following} Following</span>
                  <span>{creatorData.interactions} Interactions</span>
                </div>
              </div>
            </div>
            
            {/* 关注按钮 */}
            <div className="flex gap-2 mt-4 md:mt-0 w-full sm:w-auto">
              <Button className="bg-pink-500 hover:bg-pink-600 text-white w-full sm:w-auto">
                <UserPlus className="h-4 w-4 mr-2" /> Follow
              </Button>
            </div>
          </div>
        </div>

        {/* Profile Content */}
        <div className="p-4 md:p-6">
          <div className="bg-[#1a0a24] rounded-xl p-4 md:p-6 border border-[#3a1a44] mb-6">
            <p className="text-gray-300 mb-4">{creatorData.description}</p>
          </div>

          {/* Characters Section */}
          <div className="mb-4">
            <h2 className="text-xl font-semibold text-white mb-4">Characters ({creatorCharacters.length})</h2>
          </div>

          {/* Characters Grid - 移动端双列瀑布式，PC端保持原网格布局 */}
          {charactersLoading ? (
            <div className="flex items-center justify-center py-10">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-pink-500"></div>
              <span className="ml-2 text-gray-400">Loading characters...</span>
            </div>
          ) : creatorCharacters.length > 0 ? (
            <div className="columns-2 md:grid md:grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 md:columns-none gap-4 space-y-4 md:space-y-0">
              {creatorCharacters.map((char: any) => {
                const followState = followStates[char.id] || { isFollowing: false, isLoading: false };
                const isLoggedIn = typeof window !== 'undefined' ? localStorage.getItem('isLoggedIn') === 'true' : false;
                
                return (
                  <div key={char.id} className="break-inside-avoid mb-4 md:mb-0">
                    <CharacterCard
                      character={{
                        ...char,
                        isFollowing: followState.isFollowing,
                        isFollowLoading: followState.isLoading,
                        onFollowToggle: isLoggedIn ? () => handleFollowToggle(char.id) : undefined,
                      }}
                    />
                  </div>
                );
              })}
            </div>
          ) : (
            <div className="text-center py-10 text-gray-400">
              <p className="text-xl">No characters found for this creator.</p>
            </div>
          )}
        </div>
      </main>
    </div>
  )
} 