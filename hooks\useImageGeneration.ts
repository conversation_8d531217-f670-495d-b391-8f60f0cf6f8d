"use client";

import { useState, useCallback } from 'react';
import { 
  getRandomOption, 
  getClothingOptions,
  SCENE_OPTIONS,
  POSE_OPTIONS,
  ANGLE_OPTIONS
} from '@/lib/imageGeneration/config';

export interface ImageGenerationState {
  // 角色选择
  selectedCharacter: string | null;
  selectedGender: "male" | "female";
  selectedStyle: "realistic" | "anime";
  uploadedImage: string | null;
  
  // 生成参数
  selectedScene: string | null;
  selectedClothing: string | null;
  selectedPose: string | null;
  selectedAngle: string | null;
  imageCount: number;
  
  // 生成状态
  isGenerating: boolean;
  generatedImages: string[];
  
  // UI状态
  openDialog: string | null;
  openUploadDialog: boolean;
}

const initialState: ImageGenerationState = {
  selectedCharacter: null,
  selectedGender: "male",
  selectedStyle: "realistic",
  uploadedImage: null,
  selectedScene: null,
  selectedClothing: null,
  selectedPose: null,
  selectedAngle: null,
  imageCount: 1,
  isGenerating: false,
  generatedImages: [],
  openDialog: null,
  openUploadDialog: false,
};

export function useImageGeneration() {
  const [state, setState] = useState<ImageGenerationState>(initialState);

  // 更新状态的通用函数
  const updateState = useCallback((updates: Partial<ImageGenerationState>) => {
    setState(prev => ({ ...prev, ...updates }));
  }, []);

  // 选择角色
  const selectCharacter = useCallback((characterId: string) => {
    updateState({ 
      selectedCharacter: characterId,
      uploadedImage: null // 清除上传的图片
    });
  }, [updateState]);

  // 选择性别
  const selectGender = useCallback((gender: "male" | "female") => {
    updateState({ 
      selectedGender: gender,
      selectedClothing: null // 重置服装选择
    });
  }, [updateState]);

  // 选择风格
  const selectStyle = useCallback((style: "realistic" | "anime") => {
    updateState({ selectedStyle: style });
  }, [updateState]);

  // 上传图片
  const uploadImage = useCallback((imageUrl: string) => {
    updateState({ 
      uploadedImage: imageUrl,
      selectedCharacter: null // 清除角色选择
    });
  }, [updateState]);

  // 选择场景
  const selectScene = useCallback((scene: string) => {
    updateState({ selectedScene: scene });
  }, [updateState]);

  // 选择服装
  const selectClothing = useCallback((clothing: string) => {
    updateState({ selectedClothing: clothing });
  }, [updateState]);

  // 选择姿势
  const selectPose = useCallback((pose: string) => {
    updateState({ selectedPose: pose });
  }, [updateState]);

  // 选择角度
  const selectAngle = useCallback((angle: string) => {
    updateState({ selectedAngle: angle });
  }, [updateState]);

  // 设置图片数量
  const setImageCount = useCallback((count: number) => {
    updateState({ imageCount: count });
  }, [updateState]);

  // 随机选择所有选项
  const randomizeAll = useCallback(() => {
    const clothingOptions = getClothingOptions(state.selectedGender);
    
    updateState({
      selectedScene: getRandomOption(SCENE_OPTIONS),
      selectedClothing: getRandomOption(clothingOptions),
      selectedPose: getRandomOption(POSE_OPTIONS),
      selectedAngle: getRandomOption(ANGLE_OPTIONS),
    });
  }, [state.selectedGender, updateState]);

  // 生成图片
  const generateImages = useCallback(async () => {
    // 验证必要参数
    if (!state.selectedCharacter && !state.selectedGender && !state.uploadedImage) {
      throw new Error("请选择角色或上传图片");
    }

    updateState({ isGenerating: true });

    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 3000));
      
      // 生成模拟图片
      const mockImages = Array(state.imageCount)
        .fill(0)
        .map((_, i) => `/placeholder.svg?height=400&width=300&text=Generated-${i + 1}`);
      
      updateState({ 
        generatedImages: mockImages,
        isGenerating: false 
      });
      
      return mockImages;
    } catch (error) {
      updateState({ isGenerating: false });
      throw error;
    }
  }, [state, updateState]);

  // 清除生成的图片
  const clearGeneratedImages = useCallback(() => {
    updateState({ generatedImages: [] });
  }, [updateState]);

  // 重置所有选择
  const resetAll = useCallback(() => {
    setState(initialState);
  }, []);

  // UI相关的方法
  const openDialog = useCallback((dialogType: string) => {
    updateState({ openDialog: dialogType });
  }, [updateState]);

  const closeDialog = useCallback(() => {
    updateState({ openDialog: null });
  }, [updateState]);

  const toggleUploadDialog = useCallback((open?: boolean) => {
    updateState({ openUploadDialog: open ?? !state.openUploadDialog });
  }, [state.openUploadDialog, updateState]);

  // 验证是否可以生成
  const canGenerate = state.selectedCharacter || state.uploadedImage || state.selectedGender;

  // 获取当前服装选项
  const currentClothingOptions = getClothingOptions(state.selectedGender);

  return {
    // 状态
    ...state,
    
    // 衍生状态
    canGenerate,
    currentClothingOptions,
    
    // 操作方法
    selectCharacter,
    selectGender,
    selectStyle,
    uploadImage,
    selectScene,
    selectClothing,
    selectPose,
    selectAngle,
    setImageCount,
    randomizeAll,
    generateImages,
    clearGeneratedImages,
    resetAll,
    openDialog,
    closeDialog,
    toggleUploadDialog,
    updateState,
  };
}