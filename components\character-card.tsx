'use client';

import Link from 'next/link';
import { Heart } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { useState } from 'react';
import { getCharacterImageUrl } from '@/lib/image-utils';
import SmartImage from '@/components/SmartImage';
import { followCharacter } from '@/lib/api';
import { getFakeStatsForCharacter } from '@/lib/fake-stats';
import { useUser } from '@/contexts/UserContext';
import { toast } from '@/components/ui/use-toast';

export interface Character {
  id: number | string;
  name: string;
  description: string;
  chatCount: string;
  likeCount: string;
  followers?: string; // 收藏数量
  imageSrc: string;
  altImageSrc?: string; // 新增：悬停时显示的第二张图
  tags: string[];
  mappedTags?: string[]; // 映射后的标签
  creator: {
    id: string;
    name: string;
    likeCount: string;
  };
  isFollowing?: boolean; // 关注状态
  isFollowLoading?: boolean; // 关注加载状态
  onFollowToggle?: () => void; // 关注切换回调
}

export default function CharacterCard({ character }: { character: Character }) {
  const [isHovered, setIsHovered] = useState(false);
  const { isLoggedIn } = useUser();

  // 生成假统计数据
  const fakeStats = getFakeStatsForCharacter(character);

  // 游客模式下的关注处理
  const handleGuestFollow = () => {
    toast({
      title: 'Sign in required',
      description: 'Sign in to follow your favorite characters',
    });
  };

  // 改进创作者信息获取逻辑
  const creatorName = character?.creator?.name || 'Unknown Creator';
  const creatorId = character?.creator?.id || 'unknown';
  const creatorLikeCount = fakeStats.creatorLikeCount;

  // console.log('角色卡片数据:', {
  //   characterId: character.id,
  //   characterName: character.name,
  //   creator: character.creator,
  //   creatorName,
  //   creatorId,
  //   hasAltImage: !!character.altImageSrc,
  //   altImageSrc: character.altImageSrc
  // });

  return (
    <div
      className="relative rounded-lg overflow-hidden bg-[#1a0a24] border border-[#3a1a44] transition-all duration-300 hover:scale-[1.03] shadow-lg"
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {/* 图片区域，支持主图+悬浮副图 */}
      <Link href={`/chat/${character.id}`}>
        <div className="relative aspect-[3/4]">
          {/* 主图（默认显示，悬停时渐隐） */}
          <SmartImage
            src={getCharacterImageUrl(character.imageSrc) || '/placeholder.svg'}
            alt={character.name}
            fill
            className={`object-cover transition-opacity duration-300 ${
              isHovered && character.altImageSrc ? 'opacity-0' : 'opacity-100'
            }`}
            priority
          />
          {/* 副图（有altImageSrc时，悬停渐显） */}
          {character.altImageSrc && (
            <SmartImage
              src={getCharacterImageUrl(character.altImageSrc)}
              alt={`${character.name} alt`}
              fill
              className={`object-cover transition-opacity duration-300 ${
                isHovered ? 'opacity-100' : 'opacity-0'
              }`}
              priority
            />
          )}
          {/* 左下角数据 - 移动端优化 */}
          <div className="absolute bottom-1 left-1 md:bottom-2 md:left-2 flex items-center space-x-0.5 md:space-x-1">
            <div className="bg-black/70 rounded-full px-1.5 md:px-2 py-0.5 text-[10px] md:text-xs text-white flex items-center">
              💬 {fakeStats.chatCount}
            </div>
            <div className="bg-black/70 rounded-full px-1.5 md:px-2 py-0.5 text-[10px] md:text-xs text-white flex items-center">
              ❤️ {fakeStats.likeCount}
            </div>
            <div className="bg-black/70 rounded-full px-1.5 md:px-2 py-0.5 text-[10px] md:text-xs text-white flex items-center">
              ⭐ {fakeStats.followers}
            </div>
          </div>

          {/* 游客模式免费体验标签 */}
          {!isLoggedIn && (
            <div className="absolute top-1 left-1 md:top-2 md:left-2">
              <div className="bg-gradient-to-r from-purple-500 to-pink-500 rounded-full px-1.5 md:px-2 py-0.5 text-[10px] md:text-xs text-white font-medium">
                Free Trial
              </div>
            </div>
          )}

          {/* 右上角关注按钮 - 移动端优化 */}
          <div className="absolute top-1 right-1 md:top-2 md:right-2">
            <Button
              size="sm"
              variant={
                isLoggedIn && character.isFollowing ? 'secondary' : 'default'
              }
              className={`h-6 md:h-7 px-1.5 md:px-2 text-[10px] md:text-xs ${
                isLoggedIn && character.isFollowing
                  ? 'bg-gray-600/80 hover:bg-gray-500/80 text-white'
                  : 'bg-pink-500/90 hover:bg-pink-600/90 text-white'
              } backdrop-blur-sm`}
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                if (isLoggedIn) {
                  character.onFollowToggle?.();
                } else {
                  handleGuestFollow();
                }
              }}
              disabled={isLoggedIn && character.isFollowLoading}
            >
              {isLoggedIn && character.isFollowLoading
                ? '...'
                : isLoggedIn && character.isFollowing
                ? '✓'
                : '+'}
            </Button>
          </div>
        </div>
      </Link>

      {/* 内容区域 - 移动端优化 */}
      <div className="p-2 md:p-3">
        {/* 标题和描述 */}
        <h3 className="text-xs md:text-sm lg:text-base font-medium mb-0.5 md:mb-1">
          {character.name}
        </h3>
        <p className="text-[10px] md:text-xs lg:text-sm text-gray-400 mb-1.5 md:mb-2 line-clamp-1">
          {character.description}
        </p>

        {/* 标签 - 移动端优化 */}
        <div className="flex flex-wrap gap-0.5 md:gap-1 lg:gap-2">
          {/* 优先显示映射后的标签，如果没有则显示原始标签 */}
          {(character.mappedTags || character.tags || [])
            .slice(0, 3)
            .map((tag: string, index: number) => (
              <Badge
                key={index}
                variant="outline"
                className="bg-[#2a1a34] text-[10px] md:text-xs px-1.5 md:px-2 lg:px-3 py-0.5 md:py-1 rounded-full border-none hover:bg-[#3a1a44]"
              >
                {tag}
              </Badge>
            ))}
        </div>

        {/* 创作者信息 - 移动端优化 */}
        <div className="mt-1.5 md:mt-2 flex items-center">
          <Link
            href={`/creator/${creatorId}`}
            className="flex items-center text-[10px] md:text-xs lg:text-sm text-gray-400 hover:text-pink-400 min-h-[20px] md:min-h-[24px]"
          >
            <div className="w-3 h-3 md:w-4 md:h-4 lg:w-5 lg:h-5 rounded-full bg-gray-600 mr-0.5 md:mr-1 flex items-center justify-center">
              <span className="text-white text-[8px] md:text-xs">@</span>
            </div>
            <span className="truncate">{creatorName}</span>
          </Link>
          <div className="ml-auto flex items-center text-[10px] md:text-xs lg:text-sm text-pink-500">
            <Heart className="h-2.5 w-2.5 md:h-3 md:w-3 mr-0.5 md:mr-1" />
            <span className="truncate">{creatorLikeCount}</span>
          </div>
        </div>
      </div>
    </div>
  );
}
