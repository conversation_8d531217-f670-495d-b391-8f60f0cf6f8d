'use client';

import { useState, useCallback } from 'react';
import Link from 'next/link';
import { getGalleryImageUrl } from '@/lib/image-utils';
import SmartImage from '@/components/SmartImage';
import {
  Search,
  Filter,
  Clock,
  Lock,
  CheckCircle,
  ChevronDown,
  Star,
  FlameIcon as Fire,
  ArrowRight,
  ArrowLeft,
  Heart,
  BookmarkIcon,
  CreditCard,
} from 'lucide-react';
import Sidebar from '@/components/sidebar';
import MobileBottomNav from '@/components/mobile-bottom-nav';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
// 引入新的API hooks
import { 
  useGalleryPackages, 
  usePurchasedPackages, 
  useFavoriteResources,
  useFavoriteAction, 
  usePurchaseAction 
} from '@/hooks/useGallery';

export default function SneakyPage() {
  // State for active filters
  const [activeStyle, setActiveStyle] = useState<string>('All');
  const [activeIntimacy, setActiveIntimacy] = useState<string>('All');
  const [activeTag, setActiveTag] = useState<string>('All');
  const [activeSort, setActiveSort] = useState<string>('Most Popular');
  const [currentPage, setCurrentPage] = useState(1);
  const [hoveredPack, setHoveredPack] = useState<number | null>(null);
  const [paymentStatus, setPaymentStatus] = useState<string | null>(null);
  const [processingPackageId, setProcessingPackageId] = useState<number | null>(null);

  // 集成API hooks
  const { 
    packages, 
    loading: packagesLoading, 
    error: packagesError, 
    totalPages,
    refetch: refetchPackages 
  } = useGalleryPackages({
    page: currentPage,
    pageSize: 12,
    style: activeStyle,
    intimacy: activeIntimacy,
    tag: activeTag,
    sortBy: getSortByValue(activeSort),
    sortDirection: getSortDirection(activeSort),
  });

  const { 
    purchasedPackages, 
    loading: purchasedLoading 
  } = usePurchasedPackages();

  const { 
    favorites, 
    loading: favoritesLoading,
    refetch: refetchFavorites 
  } = useFavoriteResources();

  const { 
    toggleFavorite, 
    loading: favoriteLoading 
  } = useFavoriteAction();

  const { 
    purchasePackage, 
    loading: purchaseLoading 
  } = usePurchaseAction();

  // 辅助函数：转换排序参数
  function getSortByValue(sortOption: string): string {
    switch (sortOption) {
      case 'Most Popular':
        return 'price'; // 后端不支持popularity，用price代替
      case 'Newest':
        return 'createdAt';
      case 'Price ↓':
      case 'Price ↑':
        return 'price';
      default:
        return 'price'; // 默认使用price排序
    }
  }

  function getSortDirection(sortOption: string): 'asc' | 'desc' {
    switch (sortOption) {
      case 'Price ↑':
        return 'asc';
      case 'Price ↓':
      case 'Most Popular':
      case 'Newest':
      default:
        return 'desc';
    }
  }

  // 处理收藏操作
  const handleFavoriteToggle = useCallback(async (resourceId: number, currentState: boolean) => {
    try {
      await toggleFavorite(resourceId, currentState);
      // 刷新相关数据
      refetchPackages();
      refetchFavorites();
      // 可以在这里添加成功提示
    } catch (error) {
      console.error('收藏操作失败:', error);
      // 可以在这里添加错误提示
    }
  }, [toggleFavorite, refetchPackages, refetchFavorites]);

  // 处理购买操作
  const handlePurchase = useCallback(async (packageId: number) => {
    setProcessingPackageId(packageId);
    setPaymentStatus('正在处理购买请求...');
    
    try {
      const result = await purchasePackage(packageId);
      
      console.log('购买API响应:', result); // 调试日志
      
      if (result.paymentWindow) {
        // 支付窗口已打开，显示提示信息
        setPaymentStatus('支付页面已在新窗口中打开，请完成支付');
        console.log('支付窗口已打开:', result.paymentUrl);
        
        const paymentWindow = result.paymentWindow;
        
        // 监听支付窗口关闭事件
        const checkClosed = setInterval(() => {
          if (paymentWindow.closed) {
            clearInterval(checkClosed);
            console.log('支付窗口已关闭');
            
            // 支付窗口关闭后询问用户支付结果
            setPaymentStatus('支付窗口已关闭，正在确认支付状态...');
            
            // 3秒后询问用户支付结果
            setTimeout(() => {
              const paymentSuccess = window.confirm('支付是否成功？\n\n点击"确定"如果支付成功\n点击"取消"如果支付失败或取消');
              
              if (paymentSuccess) {
                setPaymentStatus('支付成功！正在刷新数据...');
                refetchPackages(); // 刷新数据
                setTimeout(() => {
                  setPaymentStatus(null);
                  setProcessingPackageId(null);
                }, 2000);
              } else {
                setPaymentStatus('支付已取消或失败');
                setTimeout(() => {
                  setPaymentStatus(null);
                  setProcessingPackageId(null);
                }, 3000);
              }
            }, 3000);
          }
        }, 1000);
        
        // 监听支付窗口消息（如果支付页面支持postMessage）
        const handlePaymentMessage = (event: MessageEvent) => {
          if (result.paymentUrl && event.origin !== new URL(result.paymentUrl).origin) return;
          
          if (event.data.type === 'PAYMENT_SUCCESS') {
            console.log('支付成功');
            setPaymentStatus('支付成功！正在刷新数据...');
            refetchPackages(); // 刷新数据
            clearInterval(checkClosed); // 清除窗口监听
            setTimeout(() => {
              setPaymentStatus(null);
              setProcessingPackageId(null);
            }, 2000);
            window.removeEventListener('message', handlePaymentMessage);
          } else if (event.data.type === 'PAYMENT_FAILED') {
            console.log('支付失败');
            setPaymentStatus('支付失败，请重试');
            clearInterval(checkClosed); // 清除窗口监听
            setTimeout(() => {
              setPaymentStatus(null);
              setProcessingPackageId(null);
            }, 3000);
            window.removeEventListener('message', handlePaymentMessage);
          }
        };
        
        window.addEventListener('message', handlePaymentMessage);
        
        // 10分钟后自动清理监听器和状态
        setTimeout(() => {
          window.removeEventListener('message', handlePaymentMessage);
          clearInterval(checkClosed);
          if (processingPackageId === packageId) {
            setPaymentStatus(null);
            setProcessingPackageId(null);
          }
        }, 10 * 60 * 1000);
        
      } else {
        // 普通响应，购买成功
        setPaymentStatus('购买成功！');
        refetchPackages();
        console.log('购买成功:', result);
        setTimeout(() => {
          setPaymentStatus(null);
          setProcessingPackageId(null);
        }, 2000);
      }
    } catch (error) {
      console.error('购买失败:', error);
      setPaymentStatus(`购买失败: ${(error as Error).message}`);
      setTimeout(() => {
        setPaymentStatus(null);
        setProcessingPackageId(null);
      }, 3000);
    }
  }, [purchasePackage, refetchPackages, processingPackageId]);

  // Filter options
  const styleOptions = [
    'All',
    'Bedroom',
    'Cosplay',
    'Outdoor',
    'Shower',
    'Fantasy',
  ];
  const intimacyOptions = ['All', 'Soft', 'Sexy', 'NSFW', 'VIP'];
  const tagOptions = ['All', 'HOT 🔥', 'Limited ⏰', 'New 🆕'];
  const sortOptions = ['Most Popular', 'Newest', 'Price ↓', 'Price ↑'];

  // 转换API数据为UI需要的格式
  const photoPacks = packages.map((pkg) => ({
    id: pkg.id,
    name: pkg.title,
    description: pkg.description,
    images: pkg.images.length,
    price: pkg.price,
    isOwned: pkg.isPurchased,
    isFavorited: pkg.isFavorited,
    isHot: pkg.tags.includes('HOT'),
    isLimited: pkg.tags.includes('Limited'),
    isNew: pkg.tags.includes('New'),
    limitedTime: pkg.tags.includes('Limited') ? '24h' : undefined, // 默认限时
    style: pkg.style,
    intimacy: pkg.intimacy,
    coverImage: pkg.coverImage,
    previewImages: pkg.images, // 使用API返回的图片列表
  }));

  // API数据转换为组件展示数据（只在有数据时使用）
  const displayPhotoPacks = packagesError ? [] : photoPacks;
  
  // 集成API数据到已有集合和库页面数据结构（保持兼容性）
  const collectionItems = favorites.map((fav) => ({
    id: fav.id,
    title: fav.resource.title,
    image: fav.resource.coverImage,
    packName: fav.resource.title,
    resourceId: fav.resourceId,
    isFavorited: true,
  }));
  const libraryItems = purchasedPackages.map((pkg) => ({
    id: pkg.packageId,
    name: pkg.package.title,
    description: pkg.package.description,
    images: pkg.package.images.length,
    purchaseDate: pkg.purchaseDate,
    lastUpdated: pkg.package.updatedAt,
    coverImage: pkg.package.coverImage,
  }));

  return (
    <div className="flex min-h-screen">
      {/* PC端侧边栏 */}
      <div className="hidden md:block">
        <Sidebar />
      </div>

      <main className="flex-1 p-4 md:p-6 pb-16 md:pb-6">
        {/* Top Navigation */}
        <Tabs defaultValue="packs" className="w-full">
          <div className="flex flex-col md:flex-row items-start md:items-center justify-between mb-6 gap-4">
            <TabsList className="bg-[#1a0a24] border border-[#3a1a44]">
              <TabsTrigger
                value="packs"
                className="data-[state=active]:bg-pink-500"
              >
                Packs
              </TabsTrigger>
              <TabsTrigger
                value="collection"
                className="data-[state=active]:bg-pink-500"
              >
                Collection
              </TabsTrigger>
              <TabsTrigger
                value="library"
                className="data-[state=active]:bg-pink-500"
              >
                Library
              </TabsTrigger>
            </TabsList>
            <div className="flex items-center space-x-4 w-full md:w-auto">
              <div className="relative flex-1 md:flex-none md:w-64">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Search className="h-4 w-4 text-gray-400" />
                </div>
                <input
                  type="text"
                  placeholder="Search..."
                  className="w-full pl-10 pr-4 py-2 rounded-full bg-[#1a0a24] border border-[#3a1a44] focus:outline-none focus:ring-2 focus:ring-pink-400 text-sm"
                />
              </div>
              <div className="h-8 w-8 rounded-full overflow-hidden">
                <SmartImage
                  src="/placeholder.svg?height=32&width=32&text=U"
                  alt="User"
                  width={32}
                  height={32}
                />
              </div>
            </div>
          </div>

          <TabsContent value="packs" className="mt-0">
            {/* Payment Status Banner */}
            {paymentStatus && (
              <div className="mb-6 bg-gradient-to-r from-pink-500/20 to-purple-500/20 rounded-xl p-4 border border-pink-500/30">
                <div className="flex items-center justify-center">
                  <div className="text-center">
                    <div className="text-lg font-medium text-pink-300 mb-2">
                      {paymentStatus}
                    </div>
                    {paymentStatus.includes('支付页面已在新窗口中打开') && (
                      <div className="text-sm text-gray-300">
                        请在新窗口中完成支付，支付完成后页面将自动刷新
                      </div>
                    )}
                  </div>
                </div>
              </div>
            )}

            {/* Filters Section */}
            <div className="mb-6 bg-[#1a0a24] rounded-xl p-4 border border-[#3a1a44]">
              <div className="flex items-center mb-3">
                <Filter className="h-4 w-4 mr-2 text-pink-400" />
                <h2 className="text-lg font-semibold">Filters</h2>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                {/* Style Filter */}
                <div>
                  <label className="block text-sm text-gray-400 mb-2">
                    Style
                  </label>
                  <div className="flex flex-wrap gap-2">
                    {styleOptions.map((style) => (
                      <Badge
                        key={style}
                        variant={style === activeStyle ? 'default' : 'outline'}
                        className={`cursor-pointer ${
                          style === activeStyle
                            ? 'bg-pink-500 hover:bg-pink-600'
                            : 'bg-[#2a1a34] hover:bg-[#3a2a44]'
                        }`}
                        onClick={() => setActiveStyle(style)}
                      >
                        {style}
                      </Badge>
                    ))}
                  </div>
                </div>

                {/* Intimacy Filter */}
                <div>
                  <label className="block text-sm text-gray-400 mb-2">
                    Intimacy
                  </label>
                  <div className="flex flex-wrap gap-2">
                    {intimacyOptions.map((level) => (
                      <Badge
                        key={level}
                        variant={
                          level === activeIntimacy ? 'default' : 'outline'
                        }
                        className={`cursor-pointer ${
                          level === activeIntimacy
                            ? 'bg-pink-500 hover:bg-pink-600'
                            : 'bg-[#2a1a34] hover:bg-[#3a2a44]'
                        }`}
                        onClick={() => setActiveIntimacy(level)}
                      >
                        {level}
                      </Badge>
                    ))}
                  </div>
                </div>

                {/* Tags Filter */}
                <div>
                  <label className="block text-sm text-gray-400 mb-2">
                    Tags
                  </label>
                  <div className="flex flex-wrap gap-2">
                    {tagOptions.map((tag) => (
                      <Badge
                        key={tag}
                        variant={tag === activeTag ? 'default' : 'outline'}
                        className={`cursor-pointer ${
                          tag === activeTag
                            ? 'bg-pink-500 hover:bg-pink-600'
                            : 'bg-[#2a1a34] hover:bg-[#3a2a44]'
                        }`}
                        onClick={() => setActiveTag(tag)}
                      >
                        {tag}
                      </Badge>
                    ))}
                  </div>
                </div>

                {/* Sort Filter */}
                <div>
                  <label className="block text-sm text-gray-400 mb-2">
                    Sort
                  </label>
                  <div className="relative">
                    <select
                      className="w-full bg-[#2a1a34] border border-[#3a1a44] rounded-md py-2 px-3 appearance-none focus:outline-none focus:ring-2 focus:ring-pink-400"
                      value={activeSort}
                      onChange={(e) => setActiveSort(e.target.value)}
                    >
                      {sortOptions.map((option) => (
                        <option key={option} value={option}>
                          {option}
                        </option>
                      ))}
                    </select>
                    <ChevronDown className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400 pointer-events-none" />
                  </div>
                </div>
              </div>
            </div>

            {/* Loading State */}
            {packagesLoading && (
              <div className="flex justify-center items-center py-12">
                <div className="text-white">
                  <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-pink-500 mx-auto mb-4"></div>
                  <p className="text-lg">Loading photo packs...</p>
                </div>
              </div>
            )}

            {/* Error State */}
            {packagesError && (
              <div className="flex flex-col items-center justify-center py-12">
                <div className="text-gray-400 mb-4">📦</div>
                <div className="text-lg font-medium mb-2">暂无图包数据</div>
                <div className="text-gray-400 text-sm text-center max-w-md">
                  {packagesError.includes('403') || packagesError.includes('访问被拒绝') 
                    ? '您暂时没有权限访问图包内容' 
                    : '图包内容暂时无法加载'}
                </div>
              </div>
            )}

            {/* Photo Packs Grid */}
            {!packagesLoading && !packagesError && (
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
                {displayPhotoPacks.length === 0 ? (
                  <div className="col-span-full text-center py-12">
                    <div className="text-gray-400 text-lg">No photo packs found</div>
                    <div className="text-gray-500 text-sm mt-2">Try adjusting your filters</div>
                  </div>
                ) : (
                  displayPhotoPacks.map((pack) => (
                <div
                  key={pack.id}
                  className="block"
                  onMouseEnter={() => setHoveredPack(pack.id)}
                  onMouseLeave={() => setHoveredPack(null)}
                >
                  <div className="bg-[#1a0a24] rounded-xl overflow-hidden border border-[#3a1a44] transition-all duration-300 hover:shadow-lg hover:shadow-pink-500/20 hover:border-pink-500/50 hover:scale-[1.02]">
                    {/* Pack Cover Image with Overlay */}
                    <div className="relative h-64 overflow-hidden">
                      {/* Main Cover Image */}
                      <div
                        className={`absolute inset-0 transition-opacity duration-500 ${
                          hoveredPack === pack.id ? 'opacity-0' : 'opacity-100'
                        }`}
                      >
                        <SmartImage
                          src={getGalleryImageUrl(pack.coverImage) || '/placeholder.svg'}
                          alt={pack.name}
                          width={300}
                          height={400}
                          className="w-full h-full object-cover filter brightness-75"
                        />
                        {/* Blur/Grayscale effect for non-owned packs */}
                        {!pack.isOwned && (
                          <div className="absolute inset-0 bg-black/30 backdrop-blur-sm"></div>
                        )}
                      </div>

                      {/* Preview Images Carousel (shown on hover) */}
                      {hoveredPack === pack.id && (
                        <div className="absolute inset-0 flex items-center justify-center">
                          <div className="relative w-full h-full">
                            {pack.previewImages.map((img, index) => (
                              <div
                                key={index}
                                className="absolute inset-0 transition-opacity duration-500"
                                style={{
                                  opacity: index === 0 ? 1 : 0,
                                  animation:
                                    index === 0
                                      ? 'fadeInOut 6s infinite'
                                      : `fadeInOut 6s infinite ${index * 2}s`,
                                }}
                              >
                                <SmartImage
                                  src={getGalleryImageUrl(img) || '/placeholder.svg'}
                                  alt={`${pack.name} preview ${index + 1}`}
                                  width={300}
                                  height={400}
                                  className="w-full h-full object-cover filter brightness-90"
                                />
                                {!pack.isOwned && (
                                  <div className="absolute inset-0 bg-black/20 backdrop-blur-[2px]"></div>
                                )}
                              </div>
                            ))}
                          </div>
                        </div>
                      )}

                      {/* Status Badges */}
                      <div className="absolute top-2 left-2 flex flex-col gap-2">
                        {pack.isHot && (
                          <Badge className="bg-red-500 text-white">
                            <Fire className="h-3 w-3 mr-1" /> HOT
                          </Badge>
                        )}
                        {pack.isLimited && (
                          <Badge className="bg-amber-500 text-white">
                            <Clock className="h-3 w-3 mr-1" />{' '}
                            {pack.limitedTime}
                          </Badge>
                        )}
                        {pack.isNew && (
                          <Badge className="bg-blue-500 text-white">NEW</Badge>
                        )}
                      </div>

                      {/* 收藏按钮 */}
                      <button 
                        className="absolute top-2 right-2 h-8 w-8 rounded-full bg-black/50 flex items-center justify-center hover:bg-pink-500/70 transition-colors"
                        onClick={(e) => {
                          e.preventDefault();
                          e.stopPropagation();
                          handleFavoriteToggle(pack.id, pack.isFavorited);
                        }}
                        disabled={favoriteLoading}
                      >
                        <Heart className={`h-4 w-4 transition-colors ${
                          pack.isFavorited ? 'text-pink-400 fill-pink-400' : 'text-white'
                        }`} />
                      </button>

                      {/* Overlay with info */}
                      <div className="absolute inset-0 bg-gradient-to-t from-black/90 via-black/30 to-transparent flex items-end p-4">
                        <div>
                          <h3 className="text-xl font-semibold">{pack.name}</h3>
                          <p className="text-sm text-gray-300 line-clamp-2 mt-1">
                            {pack.description}
                          </p>
                          <div className="flex items-center mt-2">
                            <span className="text-xs text-gray-400">
                              {pack.images} images
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Pack Footer */}
                    <div className="p-4 border-t border-[#3a1a44]">
                      <div className="flex justify-between items-center">
                        <div className="flex items-center">
                          {pack.isOwned ? (
                            <div className="flex items-center text-green-400">
                              <CheckCircle className="h-4 w-4 mr-1" />
                              <span>Owned</span>
                            </div>
                          ) : (
                            <div className="font-semibold text-pink-400">
                              ${pack.price}
                            </div>
                          )}
                        </div>
                        {pack.isOwned ? (
                          <Link href={`/sneaky/gallery/${pack.id}`}>
                            <Button
                              size="sm"
                              className="bg-green-500/20 text-green-400 hover:bg-green-500/30"
                            >
                              View Gallery
                            </Button>
                          </Link>
                        ) : (
                          <Button
                            size="sm"
                            className="bg-pink-500 hover:bg-pink-600 text-white"
                            onClick={() => handlePurchase(pack.id)}
                            disabled={purchaseLoading || processingPackageId === pack.id}
                          >
                            {processingPackageId === pack.id ? 'Processing...' : 'Unlock Now'} <Lock className="h-3 w-3 ml-1" />
                          </Button>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
                  ))
                )}
              </div>
            )}

            {/* Pagination */}
            {totalPages > 1 && (
              <div className="flex justify-center items-center space-x-4 mt-8">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() =>
                    setCurrentPage((prev) => Math.max(prev - 1, 1))
                  }
                  disabled={currentPage === 1}
                  className="bg-[#1a0a24] border-[#3a1a44] hover:bg-[#2a1a34]"
                >
                  <ArrowLeft className="h-4 w-4 mr-1" /> Previous
                </Button>
                <div className="text-sm">
                  Page {currentPage} of {totalPages}
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() =>
                    setCurrentPage((prev) => Math.min(prev + 1, totalPages))
                  }
                  disabled={currentPage === totalPages}
                  className="bg-[#1a0a24] border-[#3a1a44] hover:bg-[#2a1a34]"
                >
                  Next <ArrowRight className="h-4 w-4 ml-1" />
                </Button>
              </div>
            )}

            {/* Premium CTA */}
            <div className="mt-12 bg-gradient-to-r from-pink-500/20 to-purple-500/20 rounded-xl p-6 text-center">
              <h2 className="text-xl font-bold mb-2">Unlock All Packs</h2>
              <p className="text-gray-300 mb-4">
                Subscribe to Premium for unlimited access to all photo packs,
                including VIP content.
              </p>
              <div className="flex justify-center items-center gap-4">
                <Link
                  href="/premium"
                  className="inline-block bg-pink-500 hover:bg-pink-600 text-white px-6 py-3 rounded-full font-medium transition-colors"
                >
                  <Star className="h-4 w-4 inline mr-2" /> Subscribe Now
                </Link>
                <span className="text-gray-400">or</span>
                <Button
                  variant="outline"
                  className="bg-[#1a0a24] border-[#3a1a44] hover:bg-[#2a1a34]"
                >
                  Learn More
                </Button>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="collection" className="mt-0">
            <div className="mb-6 bg-[#1a0a24] rounded-xl p-4 border border-[#3a1a44]">
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center">
                  <Heart className="h-4 w-4 mr-2 text-pink-400" />
                  <h2 className="text-lg font-semibold">Your Collection</h2>
                </div>
                <div className="text-sm text-gray-400">
                  {favoritesLoading ? 'Loading...' : `${collectionItems.length} saved images`}
                </div>
              </div>
            </div>

            {favoritesLoading ? (
              <div className="flex justify-center items-center py-12">
                <div className="text-white">
                  <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-pink-500 mx-auto mb-4"></div>
                  <p className="text-lg">Loading your collection...</p>
                </div>
              </div>
            ) : !favoritesLoading && collectionItems.length > 0 ? (
              <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                {collectionItems.map((item) => (
                  <div key={item.id} className="group relative">
                    <div className="aspect-[3/4] rounded-lg overflow-hidden bg-[#1a0a24] border border-[#3a1a44]">
                      <SmartImage
                        src={getGalleryImageUrl(item.image) || '/placeholder.svg'}
                        alt={item.title}
                        width={300}
                        height={400}
                        className="w-full h-full object-cover transition-transform group-hover:scale-105"
                      />
                    </div>
                    <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/30 to-transparent opacity-0 group-hover:opacity-100 transition-opacity flex flex-col justify-end p-3">
                      <h3 className="text-sm font-medium">{item.title}</h3>
                      <p className="text-xs text-gray-300">{item.packName}</p>
                    </div>
                    {/* 取消收藏按钮 */}
                    <button 
                      className="absolute top-2 right-2 h-8 w-8 rounded-full bg-black/50 flex items-center justify-center hover:bg-pink-500/70 transition-colors"
                      onClick={() => handleFavoriteToggle(item.resourceId, true)}
                      disabled={favoriteLoading}
                    >
                      <Heart className="h-4 w-4 text-pink-400 fill-pink-400" />
                    </button>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-12">
                <Heart className="h-12 w-12 mx-auto text-gray-500 mb-4" />
                <h3 className="text-xl font-medium mb-2">
                  Your collection is empty
                </h3>
                <p className="text-gray-400 mb-6">
                  Save your favorite images by clicking the heart icon
                </p>
                <Button
                  variant="outline"
                  className="bg-[#1a0a24] border-[#3a1a44] hover:bg-[#2a1a34]"
                >
                  Browse Packs
                </Button>
              </div>
            )}
          </TabsContent>

          <TabsContent value="library" className="mt-0">
            <div className="mb-6 bg-[#1a0a24] rounded-xl p-4 border border-[#3a1a44]">
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center">
                  <BookmarkIcon className="h-4 w-4 mr-2 text-pink-400" />
                  <h2 className="text-lg font-semibold">Your Library</h2>
                </div>
                <div className="text-sm text-gray-400">
                  {purchasedLoading ? 'Loading...' : `${libraryItems.length} purchased packs`}
                </div>
              </div>
            </div>

            {purchasedLoading ? (
              <div className="flex justify-center items-center py-12">
                <div className="text-white">
                  <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-pink-500 mx-auto mb-4"></div>
                  <p className="text-lg">Loading your library...</p>
                </div>
              </div>
            ) : !purchasedLoading && libraryItems.length > 0 ? (
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
                {libraryItems.map((pack) => (
                  <Link
                    href={`/sneaky/gallery/${pack.id}`}
                    key={pack.id}
                    className="block"
                  >
                    <div className="bg-[#1a0a24] rounded-xl overflow-hidden border border-[#3a1a44] transition-all duration-300 hover:shadow-lg hover:shadow-pink-500/20 hover:border-pink-500/50 hover:scale-[1.02]">
                      <div className="relative h-48 overflow-hidden">
                        <SmartImage
                          src={getGalleryImageUrl(pack.coverImage) || '/placeholder.svg'}
                          alt={pack.name}
                          width={300}
                          height={200}
                          className="w-full h-full object-cover"
                        />
                        <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/30 to-transparent flex items-end p-4">
                          <div>
                            <h3 className="text-xl font-semibold">
                              {pack.name}
                            </h3>
                            <div className="flex items-center mt-2">
                              <span className="text-xs text-gray-400">
                                {pack.images} images
                              </span>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div className="p-4">
                        <div className="flex justify-between items-center text-sm text-gray-400">
                          <div>
                            Purchased:{' '}
                            {new Date(pack.purchaseDate).toLocaleDateString()}
                          </div>
                          <div>
                            Updated:{' '}
                            {new Date(pack.lastUpdated).toLocaleDateString()}
                          </div>
                        </div>
                        <Button className="w-full mt-4 bg-pink-500 hover:bg-pink-600">
                          View Gallery
                        </Button>
                      </div>
                    </div>
                  </Link>
                ))}
              </div>
            ) : (
              <div className="text-center py-12">
                <CreditCard className="h-12 w-12 mx-auto text-gray-500 mb-4" />
                <h3 className="text-xl font-medium mb-2">
                  Your library is empty
                </h3>
                <p className="text-gray-400 mb-6">
                  Purchase photo packs to access exclusive content
                </p>
                <Button
                  variant="outline"
                  className="bg-[#1a0a24] border-[#3a1a44] hover:bg-[#2a1a34]"
                >
                  Browse Packs
                </Button>
              </div>
            )}
          </TabsContent>
        </Tabs>
      </main>

      <style jsx global>{`
        @keyframes fadeInOut {
          0%, 33% { opacity: 1; }
          50%, 83% { opacity: 0; }
          100% { opacity: 1; }
        }
      `}</style>
      
      {/* 移动端底部导航 */}
      <MobileBottomNav />
    </div>
  );
}
