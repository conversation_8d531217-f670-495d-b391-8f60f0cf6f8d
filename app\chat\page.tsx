"use client"

import { useEffect, useState } from "react"
import { useRouter } from "next/navigation"
import Sidebar from "@/components/sidebar"
import MobileBottomNav from "@/components/mobile-bottom-nav"
import { useIsMobile } from "@/components/ui/use-mobile"
import { getRecentChats } from "@/lib/api/characters"
import { getAvatarUrl } from "@/lib/image-utils"
import { useUser } from "@/contexts/UserContext"
import { GuestHeader } from "@/components/auth/GuestPrompt"
import Image from "next/image"
import Link from "next/link"
import { X } from "lucide-react"

interface ChatItem {
  id: number
  name: string
  lastMessage: string
  imageSrc: string
  timestamp: string
  unread: boolean
  unreadCount: number
  isPrivate: boolean
}

export default function ChatIndexPage() {
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(true)
  const [recentChats, setRecentChats] = useState<ChatItem[]>([])
  const [recentChatsLoading, setRecentChatsLoading] = useState(false)
  const isMobile = useIsMobile()
  const { isLoggedIn, isLoading: authLoading } = useUser()

  // 获取最近聊天列表或默认角色列表
  useEffect(() => {
    const fetchRecentChats = async () => {
      // 等待认证状态确定
      if (authLoading) {
        return
      }

      if (!isLoggedIn) {
        // 游客模式：不显示任何聊天列表
        console.log('Guest mode: no chat list to show')
        setRecentChats([])
        setIsLoading(false)
        return
      }

      const storedToken = typeof window !== 'undefined' ? localStorage.getItem('token') : null;
      if (!storedToken) {
        // 登录状态异常
        router.push('/login')
        return
      }

      setRecentChatsLoading(true)
      try {
        const response = await getRecentChats(20) // 获取最多20个最近聊天
        
        if (response.success && response.data.characters) {
          // 转换API数据格式为页面需要的格式
          const formattedChats: ChatItem[] = response.data.characters.map((char: any) => ({
            id: parseInt(char.id),
            name: char.name,
            lastMessage: char.lastChatContent || 'Start chatting...',
            imageSrc: getAvatarUrl(char.avatarSrc),
            timestamp: char.lastChatAt
              ? new Date(char.lastChatAt).toLocaleDateString()
              : 'Now',
            unread: char.unreadCount > 0,
            unreadCount: char.unreadCount,
            isPrivate: char.private || false,
          }));
          setRecentChats(formattedChats)
        } else {
          console.error('获取最近聊天失败')
          setRecentChats([])
        }
      } catch (error) {
        console.error('获取最近聊天错误:', error)
        setRecentChats([])
      } finally {
        setRecentChatsLoading(false)
        setIsLoading(false)
      }
    }

    fetchRecentChats()
  }, [router, isLoggedIn, authLoading])

  // PC端重定向逻辑 - 仅对登录用户生效
  useEffect(() => {
    if (!isMobile && !isLoading && !recentChatsLoading && isLoggedIn) {
      const redirectToFirstChat = async () => {
        if (recentChats.length > 0) {
          const firstChat = recentChats[0]
          router.push(`/chat/${firstChat.id}`)
        } else {
          // 没有聊天记录，重定向到首页
          router.push('/')
        }
      }
      redirectToFirstChat()
    }
  }, [recentChats, isLoading, recentChatsLoading, router, isMobile, isLoggedIn])

  // 移动端显示聊天列表页面
  if (isMobile) {
    return (
      <div className="flex flex-col h-screen bg-[#0e0314]">
        {/* 游客模式头部提示 */}
        {!isLoggedIn && <GuestHeader />}
        
        {/* 头部 */}
        <div className="flex-shrink-0 p-4 border-b border-[#3a1a44] bg-[#120518]">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-bold">
              {isLoggedIn ? 'Chat' : 'Explore Characters'}
            </h2>
            <button
              className="min-h-[44px] min-w-[44px] p-2 rounded-full hover:bg-[#2a1a34] flex items-center justify-center"
              onClick={() => router.push('/')}
            >
              <X className="h-5 w-5 text-gray-400" />
            </button>
          </div>
        </div>

        {/* 聊天列表内容 */}
        <div className="flex-1 overflow-y-auto p-4 space-y-2">
          {recentChatsLoading ? (
            <div className="flex justify-center items-center py-12">
              <div className="text-gray-400 text-sm">
                Loading chat list...
              </div>
            </div>
          ) : recentChats.length > 0 ? (
            recentChats.map((chat) => (
              <Link
                href={`/chat/${chat.id}`}
                key={chat.id}
                className="block p-4 hover:bg-[#2a1a34] transition-colors rounded-xl"
              >
                <div className="flex items-start">
                  <div className="relative mr-4 flex-shrink-0">
                    <div className="h-14 w-14 rounded-full overflow-hidden">
                      <Image
                        src={getAvatarUrl(chat.imageSrc || '/placeholder.svg')}
                        alt={chat.name}
                        width={56}
                        height={56}
                        className="object-cover w-full h-full"
                      />
                    </div>
                    {/* 显示未读消息数量 */}
                    {chat.unreadCount > 0 && (
                      <div className="absolute -top-1 -right-1 bg-red-500 rounded-full h-5 w-5 flex items-center justify-center text-xs font-bold">
                        {chat.unreadCount > 99 ? '99+' : chat.unreadCount}
                      </div>
                    )}
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="flex justify-between items-start">
                      <h4 className="text-base font-medium truncate">
                        {chat.name}
                      </h4>
                      <span className="text-sm text-gray-400 flex-shrink-0 ml-2">
                        {chat.timestamp}
                      </span>
                    </div>
                    <p className="text-sm text-gray-400 truncate">
                      {chat.lastMessage}
                    </p>
                  </div>
                </div>
              </Link>
            ))
          ) : (
            <div className="text-center text-gray-400 py-12">
              <div className="mb-2">💬</div>
              <div className="text-sm">
                {isLoggedIn ? 'No chat history' : 'Ready to Chat'}
              </div>
              <div className="text-xs mt-1">
                {isLoggedIn 
                  ? 'Start a conversation to see chats here'
                  : 'Sign in to see your chat history'
                }
              </div>
            </div>
          )}
        </div>

        {/* 移动端底部导航 */}
        <MobileBottomNav />
      </div>
    )
  }

  // PC端显示
  return (
    <div className="flex min-h-screen">
      <Sidebar />
      <div className="flex-1 flex items-center justify-center h-screen bg-[#0e0314]">
        {isLoading || authLoading ? (
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-pink-500 mx-auto mb-4"></div>
            <p className="text-gray-400">
              {isLoading ? "Loading conversations..." : "Checking auth..."}
            </p>
          </div>
        ) : !isLoggedIn ? (
          <div className="max-w-md text-center">
            {/* 游客模式PC端展示 */}
            <div className="mb-6">
              <h2 className="text-2xl font-bold text-white mb-4">Chat History</h2>
              <p className="text-gray-300 mb-6">
                Sign in to view and manage your conversations
              </p>
            </div>
            <GuestHeader />
          </div>
        ) : (
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-pink-500 mx-auto mb-4"></div>
            <p className="text-gray-400">Redirecting...</p>
          </div>
        )}
      </div>
    </div>
  )
}
