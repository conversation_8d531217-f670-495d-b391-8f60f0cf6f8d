"use client";

import type React from "react";
import { useState, useEffect } from "react";
import { MessageCircle, ImageIcon, Mic, Heart } from "lucide-react";

interface MessageInputProps {
  inputValue: string;
  setInputValue: (value: string) => void;
  handleSendMessage: () => void;
  handleKeyDown: (e: React.KeyboardEvent) => void;
  characterName: string;
  isLoading: boolean;
  replyType: string;
  setReplyType: (type: string) => void;
  showReplyOptions: boolean;
  pictureQuota: number;
  voiceQuota: number;
  quickReplyMode?: 'bubble' | 'picture' | 'voice';
  onQuickReplyModeChange?: (mode: 'bubble' | 'picture' | 'voice') => void;
}

export default function MessageInput({
  inputValue,
  setInputValue,
  handleSendMessage,
  handleKeyDown,
  characterName,
  isLoading,
  replyType,
  setReplyType,
  showReplyOptions,
  pictureQuota,
  voiceQuota,
  quickReplyMode = 'bubble',
  onQuickReplyModeChange,
}: MessageInputProps) {
  // 移动端键盘适配
  useEffect(() => {
    const handleViewportChange = () => {
      // 检测虚拟键盘是否打开（移动端特有）
      if (typeof window !== 'undefined' && window.visualViewport) {
        const keyboardOpen = window.visualViewport.height < window.innerHeight * 0.75;
        
        if (keyboardOpen) {
          // 调整布局以避免被键盘遮挡
          const offset = window.innerHeight - window.visualViewport.height;
          document.documentElement.style.setProperty('--keyboard-offset', `${offset}px`);
          // 添加键盘打开时的样式
          document.body.classList.add('keyboard-open');
        } else {
          document.documentElement.style.removeProperty('--keyboard-offset');
          document.body.classList.remove('keyboard-open');
        }
      }
    };

    if (typeof window !== 'undefined' && window.visualViewport) {
      window.visualViewport.addEventListener('resize', handleViewportChange);
      return () => {
        window.visualViewport?.removeEventListener('resize', handleViewportChange);
        // 清理样式
        document.documentElement.style.removeProperty('--keyboard-offset');
        document.body.classList.remove('keyboard-open');
      };
    }
  }, []);

  return (
    <div className="p-2 sm:p-3 md:p-4 border-t border-[#3a1a44] pb-2">
      {/* 加载状态提示 */}
      {isLoading && (
        <div className="mb-2 sm:mb-3 flex items-center justify-center space-x-2 text-gray-400">
          <div className="flex space-x-1">
            <div className="w-1.5 h-1.5 sm:w-2 sm:h-2 bg-pink-400 rounded-full animate-pulse"></div>
            <div className="w-1.5 h-1.5 sm:w-2 sm:h-2 bg-pink-400 rounded-full animate-pulse" style={{animationDelay: '0.2s'}}></div>
            <div className="w-1.5 h-1.5 sm:w-2 sm:h-2 bg-pink-400 rounded-full animate-pulse" style={{animationDelay: '0.4s'}}></div>
          </div>
          <span className="text-xs sm:text-sm">AI is responding...</span>
        </div>
      )}

      {/* PC端输入框 - 保持原有布局 */}
      <div className="hidden md:block relative">
        <textarea
          value={inputValue}
          onChange={(e) => setInputValue(e.target.value)}
          onKeyDown={handleKeyDown}
          placeholder={`Message ${characterName}...`}
          className={`w-full p-3 sm:p-4 ${onQuickReplyModeChange ? 'pr-44' : 'pr-16'} bg-[#1a0a24] border border-[#3a1a44] rounded-xl focus:outline-none focus:ring-2 focus:ring-pink-400 resize-none text-base min-h-[44px]`}
          rows={1}
          disabled={isLoading}
        />
        <div className="absolute right-3 bottom-3 flex items-center space-x-2">
          {/* Quick Reply Mode Tabs */}
          {onQuickReplyModeChange && (
            <>
              {/* Bubble Mode Button */}
              <button
                className={`min-h-[40px] min-w-[40px] p-2 rounded-full flex items-center justify-center ${
                  quickReplyMode === 'bubble'
                    ? "bg-pink-500 text-white"
                    : "hover:bg-[#2a1a34] text-gray-400"
                }`}
                onClick={() => onQuickReplyModeChange('bubble')}
                title="Heart Bubbles"
              >
                <Heart className="h-5 w-5" />
              </button>
              
              {/* Picture Mode Button */}
              <button
                className={`min-h-[40px] min-w-[40px] p-2 rounded-full flex items-center justify-center ${
                  quickReplyMode === 'picture'
                    ? "bg-pink-500 text-white"
                    : "hover:bg-[#2a1a34] text-gray-400"
                }`}
                onClick={() => onQuickReplyModeChange('picture')}
                title="Picture Replies"
              >
                <ImageIcon className="h-5 w-5" />
              </button>
              
              {/* Voice Mode Button */}
              <button
                className={`min-h-[40px] min-w-[40px] p-2 rounded-full flex items-center justify-center ${
                  quickReplyMode === 'voice'
                    ? "bg-pink-500 text-white"
                    : "hover:bg-[#2a1a34] text-gray-400"
                }`}
                onClick={() => onQuickReplyModeChange('voice')}
                title="Voice Replies"
              >
                <Mic className="h-5 w-5" />
              </button>
            </>
          )}
          <button
            className={`min-h-[44px] min-w-[44px] p-2 rounded-full flex items-center justify-center ${
              isLoading 
                ? "bg-gray-500 cursor-not-allowed" 
                : "bg-pink-500 hover:bg-pink-600"
            } text-white`}
            onClick={handleSendMessage}
            disabled={isLoading}
          >
            {isLoading ? (
              <div className="w-6 h-6 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
            ) : (
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              >
                <path d="m22 2-7 20-4-9-9-4Z" />
                <path d="M22 2 11 13" />
              </svg>
            )}
          </button>
        </div>
      </div>

      {/* 移动端输入框 - 重构布局 */}
      <div className="md:hidden">
        {/* 输入框区域 - 占满横向空间 */}
        <div className="relative mb-2 sm:mb-3">
          <textarea
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
            onKeyDown={handleKeyDown}
            placeholder={`Message ${characterName}...`}
            className="w-full p-2 sm:p-3 bg-[#1a0a24] border border-[#3a1a44] rounded-xl focus:outline-none focus:ring-2 focus:ring-pink-400 resize-none text-sm sm:text-base min-h-[40px] sm:min-h-[44px] pr-12 sm:pr-16"
            rows={1}
            disabled={isLoading}
          />
          {/* 发送按钮 - 固定在右侧 */}
          <button
            className={`absolute right-2 sm:right-3 bottom-2 sm:bottom-3 min-h-[36px] min-w-[36px] sm:min-h-[44px] sm:min-w-[44px] p-1.5 sm:p-2 rounded-full flex items-center justify-center ${
              isLoading
                ? "bg-gray-500 cursor-not-allowed"
                : "bg-pink-500 hover:bg-pink-600"
            } text-white`}
            onClick={handleSendMessage}
            disabled={isLoading}
          >
            {isLoading ? (
              <div className="w-4 h-4 sm:w-6 sm:h-6 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
            ) : (
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="16"
                height="16"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="sm:w-5 sm:h-5"
              >
                <path d="m22 2-7 20-4-9-9-4Z" />
                <path d="M22 2 11 13" />
              </svg>
            )}
          </button>
        </div>

        {/* 功能按钮区域 - 优化布局和样式 */}
        {onQuickReplyModeChange && (
          <div className="flex justify-between items-center gap-3 mt-3 mb-2 px-2">
            {/* Quick Text 按钮 */}
            <button
              className={`flex-1 h-[52px] rounded-xl flex flex-col items-center justify-center transition-all duration-300 transform hover:scale-105 ${
                quickReplyMode === 'bubble'
                  ? "bg-gradient-to-br from-pink-500 via-pink-600 to-rose-600 text-white shadow-lg shadow-pink-500/30 border border-pink-400/50"
                  : "bg-gradient-to-br from-[#1a0a24] via-[#2a1a34] to-[#1a0a24] text-gray-300 border border-[#3a1a44] hover:border-pink-500/60 hover:bg-gradient-to-br hover:from-[#2a1a34] hover:via-[#3a1a44] hover:to-[#2a1a34] hover:text-white"
              }`}
              onClick={() => onQuickReplyModeChange('bubble')}
              title="Quick Text Messages"
            >
              <Heart className={`h-5 w-5 mb-1 transition-all duration-300 ${
                quickReplyMode === 'bubble' ? 'text-white' : 'text-pink-400'
              }`} />
              <span className="text-xs font-semibold tracking-wide">Quick Text</span>
            </button>

            {/* Snap 按钮 */}
            <button
              className={`flex-1 h-[52px] rounded-xl flex flex-col items-center justify-center transition-all duration-300 transform hover:scale-105 ${
                quickReplyMode === 'picture'
                  ? "bg-gradient-to-br from-purple-500 via-purple-600 to-indigo-600 text-white shadow-lg shadow-purple-500/30 border border-purple-400/50"
                  : "bg-gradient-to-br from-[#1a0a24] via-[#2a1a34] to-[#1a0a24] text-gray-300 border border-[#3a1a44] hover:border-purple-500/60 hover:bg-gradient-to-br hover:from-[#2a1a34] hover:via-[#3a1a44] hover:to-[#2a1a34] hover:text-white"
              }`}
              onClick={() => onQuickReplyModeChange('picture')}
              title="Send Photos"
            >
              <ImageIcon className={`h-5 w-5 mb-1 transition-all duration-300 ${
                quickReplyMode === 'picture' ? 'text-white' : 'text-purple-400'
              }`} />
              <span className="text-xs font-semibold tracking-wide">Snap</span>
            </button>

            {/* Voice 按钮 */}
            <button
              className={`flex-1 h-[52px] rounded-xl flex flex-col items-center justify-center transition-all duration-300 transform hover:scale-105 ${
                quickReplyMode === 'voice'
                  ? "bg-gradient-to-br from-emerald-500 via-emerald-600 to-teal-600 text-white shadow-lg shadow-emerald-500/30 border border-emerald-400/50"
                  : "bg-gradient-to-br from-[#1a0a24] via-[#2a1a34] to-[#1a0a24] text-gray-300 border border-[#3a1a44] hover:border-emerald-500/60 hover:bg-gradient-to-br hover:from-[#2a1a34] hover:via-[#3a1a44] hover:to-[#2a1a34] hover:text-white"
              }`}
              onClick={() => onQuickReplyModeChange('voice')}
              title="Voice Messages"
            >
              <Mic className={`h-5 w-5 mb-1 transition-all duration-300 ${
                quickReplyMode === 'voice' ? 'text-white' : 'text-emerald-400'
              }`} />
              <span className="text-xs font-semibold tracking-wide">Voice</span>
            </button>
          </div>
        )}
      </div>

      {/* Reply type selector - 仅在PC端显示 */}
      {showReplyOptions && (
        <div className="hidden md:block mt-3 p-5 bg-[#1a0a24] rounded-xl border border-[#3a1a44]">
          <div className="space-y-4">
            <div className="flex items-center space-x-4">
              <div
                className={`w-6 h-6 rounded-full ${
                  replyType === "text" ? "bg-pink-500" : "bg-[#2a1a34]"
                } flex items-center justify-center`}
              >
                {replyType === "text" && (
                  <div className="w-3 h-3 rounded-full bg-white"></div>
                )}
              </div>
              <div className="flex-1">
                <div className="flex justify-between">
                  <h4 className="text-base font-medium">Text reply</h4>
                  <span className="text-sm text-gray-400">∞</span>
                </div>
                <p className="text-sm text-gray-400">
                  Let the character send text
                </p>
              </div>
              <button
                className={`px-3 py-1.5 rounded ${
                  replyType === "text"
                    ? "bg-pink-500 text-white"
                    : "bg-[#2a1a34] text-gray-300"
                }`}
                onClick={() => setReplyType("text")}
              >
                ✓
              </button>
            </div>

            <div className="flex items-center space-x-4">
              <div
                className={`w-6 h-6 rounded-full ${
                  replyType === "picture" ? "bg-pink-500" : "bg-[#2a1a34]"
                } flex items-center justify-center`}
              >
                {replyType === "picture" && (
                  <div className="w-3 h-3 rounded-full bg-white"></div>
                )}
              </div>
              <div className="flex-1">
                <div className="flex justify-between">
                  <h4 className="text-base font-medium">
                    Picture reply{" "}
                    <span className="text-sm text-gray-400">(PRO)</span>
                  </h4>
                  <span className="text-sm text-gray-400">
                    {pictureQuota}/3
                  </span>
                </div>
                <p className="text-sm text-gray-400">
                  Let the character generate image and send to you.
                </p>
              </div>
              <button
                className={`px-3 py-1.5 rounded ${
                  replyType === "picture"
                    ? "bg-pink-500 text-white"
                    : "bg-[#2a1a34] text-gray-300"
                }`}
                onClick={() => setReplyType("picture")}
              >
                ✓
              </button>
            </div>

            <div className="flex items-center space-x-4">
              <div
                className={`w-6 h-6 rounded-full ${
                  replyType === "voice" ? "bg-pink-500" : "bg-[#2a1a34]"
                } flex items-center justify-center`}
              >
                {replyType === "voice" && (
                  <div className="w-3 h-3 rounded-full bg-white"></div>
                )}
              </div>
              <div className="flex-1">
                <div className="flex justify-between">
                  <h4 className="text-base font-medium">
                    Voice reply{" "}
                    <span className="text-sm text-gray-400">(PRO)</span>
                  </h4>
                  <span className="text-sm text-gray-400">
                    {voiceQuota}/3
                  </span>
                </div>
                <p className="text-sm text-gray-400">
                  Let the character send voice to you.
                </p>
              </div>
              <button
                className={`px-3 py-1.5 rounded ${
                  replyType === "voice"
                    ? "bg-pink-500 text-white"
                    : "bg-[#2a1a34] text-gray-300"
                }`}
                onClick={() => setReplyType("voice")}
              >
                ✓
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}