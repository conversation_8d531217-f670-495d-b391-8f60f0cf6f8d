// 硬编码的快速回复响应，主要用于图片类回复
export const HARDCODED_RESPONSES = {
  "👙 Sexy Wet": {
    text: 'Shower just finished... still dripping for you. Wanna see more?',
    imageSrc: "/alexander_sexywet.png",
    audioDuration: 8
  },
  "🧼 Bath Time": {
    text: 'Caught me in the bath… think you can handle this much temptation?',
    imageSrc: "/alexander_bathtime.png",
    audioDuration: 10
  },
  "🛌 Lying in Bed": {
    text: 'Lying here, wishing you were next to me… what would you do if you were?',
    imageSrc: "/alexander_lyingbed.png",
    audioDuration: 9
  },
  "🧥 Favorite Outfit": {
    text: 'Slipped into my favorite outfit just for you… Do I make it look irresistible?',
    imageSrc: "/alexander_favoriteoutfit.png",
    audioDuration: 7
  }
} as const;

// 类型定义
export type HardcodedResponseKey = keyof typeof HARDCODED_RESPONSES;

export interface HardcodedResponse {
  text: string;
  imageSrc: string;
  audioDuration: number;
}

// 检查是否为硬编码响应的工具函数
export const isHardcodedResponse = (reply: string): reply is HardcodedResponseKey => {
  return reply in HARDCODED_RESPONSES;
};

// 获取硬编码响应的工具函数
export const getHardcodedResponse = (reply: HardcodedResponseKey): HardcodedResponse => {
  return HARDCODED_RESPONSES[reply];
};