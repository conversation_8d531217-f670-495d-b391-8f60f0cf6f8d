/**
 * 套餐/订阅相关API接口
 * Subscription/Plan 页面的所有接口集成
 */

import { API_CONFIG } from '../config';
import { mainApiClient } from './client';

const API_BASE_URL = API_CONFIG.BASE_URL;

// ====================
// 类型定义
// ====================

// 套餐权限
export interface PlanPrivilege {
  text: number; // 文本消息额度
  image: number; // 图片消息额度
  voice_plays: number; // 语音播放额度
  voice_call: number; // 语音通话额度（秒）
  video: number; // 视频额度
  image_gen: boolean; // 图片生成功能
  nsfw: boolean; // NSFW内容访问
  max_gen_character: number; // 最大生成角色数
}

// API返回的套餐数据
export interface ApiPlan {
  id: number;
  name: string;
  currency: string;
  price: number;
  description: Record<string, any>;
  subscriptionLevel: 'Free' | 'Lite' | 'Basic' | 'Premium';
  privilege: PlanPrivilege;
  planPeriod: number; // 套餐周期（天）
  createdAt: string;
  updateAt: string;
  privilegeObj: PlanPrivilege; // 与privilege相同，可能是冗余字段
}

// 转换后的套餐数据（用于前端）
export interface SubscriptionPlan {
  id: number;
  name: string;
  level: 'Free' | 'Lite' | 'Basic' | 'Premium';
  price: number;
  currency: string;
  period: number; // 周期（天）
  periodLabel: string; // 周期标签（如 "Monthly", "Yearly"）
  features: PlanPrivilege;
  isPopular?: boolean; // 是否为推荐套餐
  savings?: string; // 节省金额提示
  createdAt: string;
  updatedAt: string;
}

// API返回的套餐列表响应
export interface ApiPlanListResponse {
  success: boolean;
  data: {
    planList: ApiPlan[];
  };
  message: string;
}

// 订阅套餐请求参数
export interface SubscribePlanParams {
  planId: number;
}

// API返回的订阅响应（参考购买图包接口，会302重定向）
export interface ApiSubscribeResponse {
  success: boolean;
  data: any; // TODO: 等待确认具体的响应数据格式（可能会重定向）
  message: string;
}

// 前端使用的响应格式
export interface PlanListResponse {
  success: boolean;
  data: {
    plans: SubscriptionPlan[];
    total: number;
  };
  message: string;
}

export interface SubscribeResponse {
  success: boolean;
  data: {
    orderId: string;
    subscribeDate: string;
    paymentUrl?: string; // 支付页面URL
    planId: number;
    paymentWindow?: Window | null; // 支付窗口引用
  };
  message: string;
}

// ====================
// API接口函数
// ====================

/**
 * 1. 获取套餐列表
 */
export async function getSubscriptionPlans(): Promise<PlanListResponse> {
  try {
    const result: ApiPlanListResponse = await mainApiClient.get('/plan/list');

    if (!result.success) {
      throw new Error(result.message || 'Failed to fetch subscription plans');
    }

    // 转换API数据为前端数据格式
    const plans: SubscriptionPlan[] = result.data.planList.map(transformApiPlan);

    return {
      success: true,
      message: result.message,
      data: {
        plans,
        total: plans.length,
      },
    };
  } catch (error) {
    console.error('获取套餐列表失败:', error);
    throw error;
  }
}

/**
 * 2. 订阅套餐 - 使用Clinkbill支付，拦截重定向并在新窗口打开支付页面
 * @param planId 套餐ID
 */
export async function subscribePlan(planId: number): Promise<SubscribeResponse> {
  const requestBody: SubscribePlanParams = {
    planId,
  };

  // 获取token
  const token = mainApiClient.getAuthToken();
  if (!token) {
    throw new Error('用户未登录');
  }

  try {
    console.log('🔄 开始订阅套餐:', requestBody);
    
    // 使用Clinkbill专用的Next.js API代理
    const response = await fetch('/api/clinkbill-subscribe-proxy', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`,
      },
      body: JSON.stringify(requestBody),
    });

    console.log('📊 Clinkbill订阅API响应状态:', response.status);

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || `HTTP ${response.status}`);
    }

    const result = await response.json();
    console.log('📄 Clinkbill订阅API响应数据:', result);

    // 检查是否需要重定向到支付页面
    if (result.redirect && result.paymentUrl) {
      console.log('🔄 检测到Clinkbill支付重定向，支付URL:', result.paymentUrl);
      
      // 在新窗口中打开支付页面
      const paymentWindow = window.open(
        result.paymentUrl,
        '_blank',
        'width=800,height=600,scrollbars=yes,resizable=yes'
      );

      if (!paymentWindow) {
        throw new Error('无法打开支付页面，请检查浏览器弹窗设置');
      }

      console.log('✅ Clinkbill支付窗口已打开，等待用户完成支付');

      return {
        success: true,
        message: '正在跳转到Clinkbill支付页面...',
        data: {
          orderId: `temp-order-plan-${planId}-${Date.now()}`,
          subscribeDate: new Date().toISOString(),
          paymentUrl: result.paymentUrl,
          planId: planId,
          paymentWindow: paymentWindow,
        },
      };
    } else {
      // 直接订阅成功的情况
      return {
        success: true,
        message: result.message || '订阅成功',
        data: {
          orderId: result.orderId || `order-plan-${planId}-${Date.now()}`,
          subscribeDate: new Date().toISOString(),
          paymentUrl: '',
          planId: planId,
          paymentWindow: null,
        },
      };
    }
  } catch (error) {
    console.error('订阅套餐失败:', error);
    throw error;
  }
}

// ====================
// 辅助函数
// ====================

/**
 * 转换API套餐数据为前端格式
 * @param apiPlan API返回的套餐数据
 */
function transformApiPlan(apiPlan: ApiPlan): SubscriptionPlan {
  // 计算周期标签
  const getPeriodLabel = (period: number): string => {
    if (period === 30) return 'Monthly';
    if (period === 365) return 'Yearly';
    return `${period} Days`;
  };

  // 判断是否为推荐套餐（可根据业务逻辑调整）
  const isPopular = apiPlan.subscriptionLevel === 'Basic';

  // 计算年费节省提示
  const getSavings = (plan: ApiPlan): string | undefined => {
    if (plan.planPeriod === 365) {
      // 这里可以根据月费计算年费节省，暂时返回示例
      return 'Save 20%';
    }
    return undefined;
  };

  return {
    id: apiPlan.id,
    name: apiPlan.name,
    level: apiPlan.subscriptionLevel,
    price: apiPlan.price,
    currency: apiPlan.currency,
    period: apiPlan.planPeriod,
    periodLabel: getPeriodLabel(apiPlan.planPeriod),
    features: apiPlan.privilege,
    isPopular,
    savings: getSavings(apiPlan),
    createdAt: apiPlan.createdAt,
    updatedAt: apiPlan.updateAt,
  };
}

/**
 * 将API的privilege对象转换为前端的features数组
 * @param privilege API返回的权限对象
 * @param level 套餐级别
 */
export function convertPrivilegeToFeatures(privilege: PlanPrivilege, level: string): string[] {
  const features: string[] = [];

  // 文本消息
  if (privilege.text === 9999999) {
    features.push("💗 Unlimited Text messages");
  } else {
    features.push(`💗 ${privilege.text} Text messages / month`);
  }

  // 图片消息
  features.push(`💗 ${privilege.image} Pictures / month`);

  // 语音消息
  if (privilege.voice_plays === 9999999) {
    features.push("💗 Unlimited Voice messages");
  } else {
    features.push(`💗 ${privilege.voice_plays} Voice messages / month`);
  }

  // 角色创建
  if (privilege.max_gen_character === 9999999) {
    features.push("💗 Create unlimited characters");
  } else if (privilege.max_gen_character === 1) {
    features.push("💗 Create 1 character");
  } else {
    features.push(`💗 Create up to ${privilege.max_gen_character} characters`);
  }

  // 语音通话时间
  if (privilege.voice_call === 9999999) {
    features.push("💗 Unlimited voice call time");
  } else {
    const minutes = Math.floor(privilege.voice_call / 60);
    if (minutes < 1) {
      features.push(`💗 ${privilege.voice_call}-second voice call trial`);
    } else {
      features.push(`💗 ${minutes}-minute voice call / month`);
    }
  }

  // NSFW功能
  if (privilege.nsfw) {
    features.push("✅ Unlock NSFW and advanced character traits");
  } else {
    if (level === 'Free') {
      features.push("🚫 Cannot unlock NSFW traits");
    } else {
      features.push("❌ NSFW traits not available");
    }
  }

  // 多图生成功能
  if (privilege.image_gen) {
    features.push("✅ Enable 4 / 9 picture generation");
  } else {
    if (level === 'Free') {
      features.push("🚫 Cannot use multi-picture (4/9) generation");
    } else {
      features.push("❌ Multi-picture generation not supported");
    }
  }

  // 响应速度（根据级别设置）
  if (level === 'Premium') {
    features.push("💗 VIP response speed");
  } else {
    features.push("🚀 Standard response speed");
  }

  return features;
}

// 导出用于其他文件的类型
// PlanPrivilege 和 ApiPlan 已在上面以 export interface 形式导出，无需重复导出