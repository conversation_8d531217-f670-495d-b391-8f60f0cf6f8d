"use client"

import Image from "next/image"
import { useState } from "react"
import { isFullUrl } from "@/lib/image-utils"

interface SmartImageProps {
  src: string
  alt: string
  width?: number
  height?: number
  fill?: boolean
  className?: string
  priority?: boolean
  style?: React.CSSProperties
  onClick?: () => void
  onMouseEnter?: () => void
  onMouseLeave?: () => void
  onError?: () => void
  showFallback?: boolean // 是否显示兜底内容（用于头像等场景）
}

/**
 * 智能图片组件：
 * - 如果src是完整URL，使用原生img标签避免Next.js代理
 * - 如果src是相对路径，使用Next.js Image组件获得优化
 */
export default function SmartImage({
  src,
  alt,
  width,
  height,
  fill,
  className,
  priority,
  style,
  onClick,
  onMouseEnter,
  onMouseLeave,
  onError,
  showFallback = true,
}: SmartImageProps) {
  const [hasError, setHasError] = useState(false)
  const isExternal = isFullUrl(src)
  
  // 检查是否需要显示兜底内容
  const shouldShowFallback = hasError || !src || src.trim() === '' || src === '/placeholder-avatar.png'

  // 处理图片加载错误
  const handleError = () => {
    setHasError(true)
    onError?.()
  }

  // 如果图片加载失败或为空，显示兜底内容
  if (shouldShowFallback && showFallback) {
    return (
      <div 
        className={`bg-gradient-to-br from-gray-400 to-gray-500 flex items-center justify-center text-white font-bold ${className}`}
        style={{
          width: width || '100%',
          height: height || '100%',
          objectFit: 'cover',
          ...style,
          ...(fill && { 
            position: 'absolute', 
            inset: 0, 
            width: '100%', 
            height: '100%' 
          })
        }}
        onClick={onClick}
        onMouseEnter={onMouseEnter}
        onMouseLeave={onMouseLeave}
      >
        {alt.split(' ').map(word => word.charAt(0)).join('').slice(0, 2).toUpperCase() || alt.charAt(0)?.toUpperCase() || '?'}
      </div>
    )
  }

  // 如果没有有效的src，且不显示兜底，返回null
  if (!src || src.trim() === '') {
    return null
  }

  if (isExternal) {
    // 对于完整URL，使用原生img标签避免代理
    return (
      <img
        src={src}
        alt={alt}
        width={width}
        height={height}
        className={className}
        style={{
          objectFit: 'cover',
          ...style,
          ...(fill && { 
            position: 'absolute', 
            inset: 0, 
            width: '100%', 
            height: '100%' 
          })
        }}
        onClick={onClick}
        onMouseEnter={onMouseEnter}
        onMouseLeave={onMouseLeave}
        onError={handleError}
        loading={priority ? 'eager' : 'lazy'}
      />
    )
  }

  // 对于相对路径，使用Next.js Image组件
  return (
    <Image
      src={src}
      alt={alt}
      width={width}
      height={height}
      fill={fill}
      className={className}
      style={style}
      priority={priority}
      onClick={onClick}
      onMouseEnter={onMouseEnter}
      onMouseLeave={onMouseLeave}
      onError={handleError}
    />
  )
}