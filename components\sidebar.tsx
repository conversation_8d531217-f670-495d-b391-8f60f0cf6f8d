import Link from "next/link"
import Image from "next/image"
import { MessageSquare, ImageIcon, PlusCircle, Heart, DiscIcon as Discord, FileText } from "lucide-react"

export default function Sidebar() {
  const menuItems = [
    { icon: "🏠", label: "Explore", href: "/" },
    { icon: "💬", label: "Chat", href: "/chat" },
    { icon: "🔞", label: "Sneaky", href: "/sneaky" },
    { icon: "📷", label: "Image Generator", href: "/create" },
    { icon: "💘", label: "Create Your Lover", href: "/create-lover" },
    { icon: "🌟", label: "Become Premium", href: "/payment" },
  ]

  const bottomItems: Array<{ icon: React.ReactNode; label: string; href: string }> = []

  return (
    <div className="w-16 md:w-64 bg-[#120518] border-r border-[#3a1a44] flex flex-col h-full">
      <div className="p-3 sm:p-4 md:p-6">
        <Link href="/" className="flex items-center mb-4 sm:mb-6 md:mb-10">
          <div className="h-8 w-8 sm:h-10 sm:w-10 flex items-center justify-center">
            <Image
              src="/logo-lumi-love.png"
              alt="LumiLove Logo"
              width={40}
              height={40}
              className="h-8 w-8 sm:h-10 sm:w-10 object-contain"
            />
          </div>
          <span className="ml-2 text-lg sm:text-xl font-bold text-white hidden md:block">LumiLove.ai</span>
        </Link>

        <nav className="space-y-3 sm:space-y-4">
          {menuItems.map((item, index) => (
            <Link
              key={index}
              href={item.href}
              className="flex items-center min-h-[44px] p-2 sm:p-3 rounded-lg hover:bg-[#2a1a34] transition-colors"
            >
              <div className="text-gray-400 text-base sm:text-lg flex-shrink-0">{item.icon}</div>
              <span className="ml-2 sm:ml-3 text-sm sm:text-base text-gray-300 hidden md:block">{item.label}</span>
            </Link>
          ))}
        </nav>
      </div>

      <div className="mt-auto p-3 sm:p-4 md:p-6">
        <div className="space-y-1 sm:space-y-2">
          {bottomItems.map((item, index) => (
            <Link
              key={index}
              href={item.href}
              className="flex items-center min-h-[44px] p-2 sm:p-3 rounded-lg hover:bg-[#2a1a34] transition-colors"
            >
              <div className="text-gray-400 flex-shrink-0">{item.icon}</div>
              <span className="ml-2 sm:ml-3 text-sm sm:text-base text-gray-300 hidden md:block">{item.label}</span>
            </Link>
          ))}
        </div>
      </div>
    </div>
  )
}
