'use client';

import type React from 'react';

import { useState, useRef, useEffect, useMemo, useCallback } from 'react';
import { useParams } from 'next/navigation';
import { useRouter } from 'next/navigation';
import { useChatState } from '@/hooks/useChatState';
import { useMessages, type Message } from '@/hooks/useMessages';
import { useUserSubscription } from '@/hooks/useSubscription';
import {
  HARDCODED_RESPONSES,
  isHardcodedResponse,
  getHardcodedResponse,
} from '@/constants/chatResponses';
import { QUICK_REPLY_BUTTONS } from '@/constants/quickReplyConfig';

// ... 其余代码保持不变

import Image from 'next/image';
import Link from 'next/link';
import {
  Heart,
  Share2,
  Phone,
  Play,
  Pause,
  ImageIcon,
  MessageCircle,
  MessageSquare,
  X,
  Menu,
  ChevronLeft,
  ChevronRight,
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import { ChatAPI } from '@/lib/api/chat';
import type { ChatHistoryItem } from '@/lib/api/types';
import {
  followCharacter,
  sendChatForCounting,
  type ChatSendRequest,
} from '@/lib/api';
import {
  getCharacterById,
  getUserCreatedCharacters,
  charactersData,
} from '@/lib/characters';
import {
  getDefaultMessages,
  hasDefaultMessages,
} from '@/lib/characters-message';
import { getAvatarUrl, getAudioUrl } from '@/lib/image-utils';
import { useAudioPlayer } from '@/hooks/useAudioPlayer';
import { useOpeningMessage } from '@/hooks/useOpeningMessage';
import { useChatHistory } from '@/hooks/useChatHistory';
import { getFakeStatsForCharacter } from '@/lib/fake-stats';
import { useUser } from '@/contexts/UserContext';

import Sidebar from '@/components/sidebar';
import MessageInput from '@/components/chat/MessageInput';
import GuestMessageInput from '@/components/chat/GuestMessageInput';
import GuestMessageList from '@/components/chat/GuestMessageList';
import ChatHeader from '@/components/chat/ChatHeader';
import MessageList from '@/components/chat/MessageList';
import QuickReplies from '@/components/chat/QuickReplies';
import CharacterProfile from '@/components/chat/CharacterProfile';
import CallDialog from '@/components/chat/CallDialog';
import ProfileModal from '@/components/chat/ProfileModal';
import ShareDialog from '@/components/chat/ShareDialog';
import { FloatingHeartBubbles } from '@/components/heart-bubbles/floating-heart-bubbles';

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '@/components/ui/dialog';

interface UiState {
  isTyping: boolean;
  isFavorite: boolean;
  isFollowing: boolean;
  isFollowLoading: boolean;
  showShareDialog: boolean;
  showProfileModal: boolean;
  showFullImage: boolean;
  showReplyOptions: boolean;
  showRecommendation: boolean;
  showMobileSidebar: boolean;
  showMobileChatList: boolean;
  showChatList: boolean;
  showCallDialog: boolean;
  showClearDialog: boolean;
  showSubscriptionModal: boolean;
  isMuted: boolean;
  isClearingHistory: boolean;
  isPlaying: number | null;
  replyType: string;
  showHeartBubbles: boolean;
}

export default function ChatPage() {
  const router = useRouter();
  const params = useParams();
  const chatId = typeof params.id === 'string' ? params.id : '';

  // 添加游客模式支持
  const { isLoggedIn, isLoading: authLoading } = useUser();

  // 使用新的消息管理hook
  const {
    messages,
    updateMessage,
    addMessage,
    addMessages,
    replaceMessages,
    clearMessages,
    updateStreamingMessage,
  } = useMessages();

  // 使用统一的状态管理（移除messages相关）
  const {
    uiState,
    callState,
    chatState,
    updateUiState,
    updateCallState,
    updateChatState,
    inputValue,
    setInputValue,
    replyType,
    setReplyType,
    isLoading,
    token,
    characterData,
    characterLoading,
    recentChats,
    recentChatsLoading,
    pictureQuota,
    voiceQuota,
    callStatus,
    callTime,
    callText,
    displayedText,
    setSelectedImage,
    selectedImage,
  } = useChatState();

  // 获取用户订阅状态
  const {
    subscriptionLevel,
    isPremiumUser,
    loading: subscriptionLoading,
  } = useUserSubscription();

  // 保留一些需要独立管理的状态
  const [showRecommendation, setShowRecommendation] = useState(false);

  // API接口切换状态 - V2设为默认
  const [useNewAPI, setUseNewAPI] = useState(true);

  // Heart Bubbles 收缩/展开状态
  const [isHeartBubblesCollapsed, setIsHeartBubblesCollapsed] = useState(false);

  // 快速回复模式状态：'bubble' | 'picture' | 'voice'
  const [quickReplyMode, setQuickReplyMode] = useState<
    'bubble' | 'picture' | 'voice'
  >('bubble');

  // 模式切换处理函数
  const handleQuickReplyModeChange = useCallback(
    (mode: 'bubble' | 'picture' | 'voice') => {
      setQuickReplyMode(mode);
      // 同时更新replyType以保持一致性
      if (mode === 'picture') {
        setReplyType('picture');
      } else if (mode === 'voice') {
        setReplyType('voice');
      } else {
        setReplyType('text'); // bubble模式对应text
      }
    },
    [setReplyType]
  );

  // 保存聊天历史到localStorage
  useEffect(() => {
    if (!chatId) return;

    const character = characters[chatId as keyof typeof characters];
    if (!character) return;

    // 获取现有的聊天历史
    const chatHistory = JSON.parse(localStorage.getItem('recentChats') || '[]');

    // 创建新的聊天记录，使用统一的头像资源
    const newChat = {
      id: chatId,
      name: character.name,
      imageSrc: character.images?.[0] || character.avatarSrc, // 保持原有的主图片
      timestamp: new Date().toISOString(),
      gender: character.occupation.toLowerCase().includes('businessman')
        ? 'male'
        : 'female',
    };

    // 检查是否已存在该角色的聊天记录
    const existingIndex = chatHistory.findIndex(
      (chat: any) => chat.id === chatId
    );
    if (existingIndex !== -1) {
      chatHistory.splice(existingIndex, 1);
    }

    chatHistory.unshift(newChat);
    const updatedHistory = chatHistory.slice(0, 10);
    localStorage.setItem('recentChats', JSON.stringify(updatedHistory));
  }, [chatId]);

  // 处理分享角色
  const handleShareCharacter = useCallback(() => {
    updateUiState({ showShareDialog: true });
  }, [updateUiState]);

  // 处理订阅按钮点击
  const handleSubscriptionButtonClick = useCallback(() => {
    console.log('唤起支付弹窗');
    updateUiState({ showSubscriptionModal: true });
  }, [updateUiState]);

  // 处理图片点击
  const handleImageClick = useCallback(
    (imageSrc: string) => {
      setSelectedImage(imageSrc);
      updateUiState({ showFullImage: true });
    },
    [setSelectedImage, updateUiState]
  );

  // 合并默认角色和用户创建的角色 - 使用统一的数据管理，优化依赖
  const characters = useMemo(() => {
    try {
      const userCreatedCharacters = getUserCreatedCharacters();
      // 使用统一的角色数据管理，包含默认角色和用户创建的角色
      return { ...charactersData, ...userCreatedCharacters };
    } catch (error) {
      console.error('合并角色数据失败:', error);
      return charactersData;
    }
  }, []); // 空依赖数组，因为这些数据相对静态

  // 优先使用API获取的角色数据，回退到本地数据 - 优化依赖和错误处理
  const character = useMemo(() => {
    // 优先使用API获取的角色数据
    if (characterData) {
      return characterData;
    }

    // 回退到本地数据
    try {
      const fallbackCharacter =
        characters[chatId as keyof typeof characters] || characters['1'];

      // 如果仍然是undefined，创建一个默认的角色对象
      if (!fallbackCharacter) {
        console.warn('未找到角色数据，使用默认角色:', chatId);
        return {
          id: chatId || '1',
          name: 'Character',
          age: 25,
          occupation: 'AI Assistant',
          tags: [],
          description: 'AI Character Assistant',
          chatCount: '0',
          likeCount: '0',
          imageSrc: '/placeholder.svg',
          avatarSrc: '/placeholder.svg',
          images: ['/placeholder.svg'],
          gender: 'female' as const,
          creator: { id: 'system', name: 'System', likeCount: '0' },
        };
      }

      return fallbackCharacter;
    } catch (error) {
      console.error('获取角色数据失败:', error);
      // 返回一个安全的默认对象
      return {
        id: chatId || '1',
        name: 'Character',
        age: 25,
        occupation: 'AI Assistant',
        tags: [],
        description: 'AI Character Assistant',
        chatCount: '0',
        likeCount: '0',
        imageSrc: '/placeholder.svg',
        avatarSrc: '/placeholder.svg',
        images: ['/placeholder.svg'],
        gender: 'female' as const,
        creator: { id: 'system', name: 'System', likeCount: '0' },
      };
    }
  }, [characterData, chatId, characters]);

  // 在组件顶部添加 token 状态

  // 🎭 使用自定义 hook 管理开场白逻辑
  const { createOpeningMessage, createNewConversationWithOpening } =
    useOpeningMessage();

  // 📚 使用自定义 hook 管理聊天历史
  const { loadChatHistory, reloadChatHistory, clearChatHistory } =
    useChatHistory();

  // 创建新对话的本地实现 - 使用 hook 简化逻辑
  const createNewConversation = useCallback(
    (characterId: number, character: any) => {
      try {
        createNewConversationWithOpening(
          characterId,
          character,
          replaceMessages
        );
      } catch (error) {
        console.error('❌ 创建新对话失败:', error);
        // 如果创建失败，至少清空消息避免显示错误状态
        clearMessages();
      }
    },
    [createNewConversationWithOpening, replaceMessages, clearMessages]
  );

  // 修改 initializeChat 函数 - 支持游客模式
  useEffect(() => {
    const initializeChat = async () => {
      try {
        console.log('开始初始化聊天...');

        // 检查用户登录状态 - 支持游客模式
        if (authLoading) {
          console.log('认证状态检查中，等待...');
          return;
        }

        if (!isLoggedIn) {
          console.log('User not logged in, enabling guest mode');
          // 游客模式：只显示角色的开场白
          const openingMessage = createOpeningMessage(character);
          replaceMessages([openingMessage]);
          return;
        }

        // 登录用户的原有逻辑
        const storedToken =
          typeof window !== 'undefined' ? localStorage.getItem('token') : null;
        if (!storedToken) {
          console.log('登录状态异常，重新登录');
          router.push('/login');
          return;
        }

        // 获取当前角色ID（移动到更高作用域）
        const currentCharacterId =
          typeof character.id === 'string'
            ? parseInt(character.id, 10)
            : character.id;
        console.log('当前角色ID:', currentCharacterId);

        // 验证token是否有效（可选：调用API验证）
        try {
          // 可以添加token验证API调用
          console.log('使用已存在的token:', storedToken);
          updateChatState({ token: storedToken });

          // 🎭 无论是否有历史记录，都先添加开场白
          const openingMessage = createOpeningMessage(character);
          const allMessages: Message[] = [openingMessage];

          try {
            // 📚 加载聊天历史
            const convertedMessages = await loadChatHistory(currentCharacterId);

            if (convertedMessages.length > 0) {
              console.log('📚 有历史记录，在开场白后添加历史消息');
              allMessages.push(...convertedMessages);
              console.log('📚 历史消息已添加到开场白后:', {
                开场白: 1,
                历史消息: convertedMessages.length,
                总消息数: allMessages.length,
              });
            } else {
              console.log('📝 没有历史消息，只显示开场白');
            }
          } catch (historyLoadError) {
            console.warn(
              '📚 历史消息加载失败，只显示开场白:',
              historyLoadError
            );
          }

          // 设置所有消息（开场白 + 历史记录）
          replaceMessages(allMessages);
        } catch (historyError) {
          console.error('加载历史消息失败:', historyError);
          // 如果token无效，清除本地存储并跳转到登录页面
          if ((historyError as any)?.message?.includes('401')) {
            if (typeof window !== 'undefined') {
              localStorage.removeItem('token');
              localStorage.removeItem('user');
            }
            router.push('/login');
            return;
          }
          // 即使加载历史失败，也显示开场白
          console.log('🎭 历史加载失败，显示开场白');
          const openingMessage = createOpeningMessage(character);
          replaceMessages([openingMessage]);
        }
      } catch (error) {
        console.error('初始化聊天失败:', error);
        // 如果初始化失败，可能需要重新登录
        router.push('/login');
      }
    };

    if (character && character.id) {
      initializeChat();
    }
  }, [
    chatId,
    character.id,
    character.name,
    character.resource?.opening,
    router,
    isLoggedIn,
    authLoading,
  ]);

  // 获取最近聊天列表
  useEffect(() => {
    const fetchRecentChats = async () => {
      const storedToken =
        typeof window !== 'undefined' ? localStorage.getItem('token') : null;
      if (!storedToken) {
        // 如果没有token，设置空的聊天列表
        updateChatState({ recentChats: [] });
        return;
      }

      updateChatState({ recentChatsLoading: true });
      try {
        const { getRecentChats: getRecentChatsAPI } = await import(
          '@/lib/api/characters'
        );
        const response = await getRecentChatsAPI(10); // 获取最多10个最近聊天

        if (response.success && response.data.characters) {
          // 转换API数据格式为页面需要的格式
          const formattedChats = response.data.characters.map((char: any) => ({
            id: parseInt(char.id),
            name: char.name,
            lastMessage: char.lastChatContent || 'Start chatting...',
            imageSrc: getAvatarUrl(char.avatarSrc),
            timestamp: char.lastChatAt
              ? new Date(char.lastChatAt).toLocaleDateString()
              : 'Now',
            unread: char.unreadCount > 0,
            unreadCount: char.unreadCount,
            isPrivate: char.private || false,
          }));
          updateChatState({ recentChats: formattedChats });
        } else {
          console.error('获取最近聊天失败');
          // API失败时设置空列表
          updateChatState({ recentChats: [] });
        }
      } catch (error) {
        console.error('获取最近聊天错误:', error);
        // 错误时设置空列表
        updateChatState({ recentChats: [] });
      } finally {
        updateChatState({ recentChatsLoading: false });
      }
    };

    fetchRecentChats();
  }, []);

  // 获取角色详情数据
  useEffect(() => {
    const fetchCharacterDetail = async () => {
      if (!chatId) return;

      updateChatState({ characterLoading: true });
      try {
        const { getCharacterDetail, transformCharacterDetail } = await import(
          '@/lib/api/characters'
        );
        const response = await getCharacterDetail(chatId);

        if (response.success) {
          const transformedCharacter = transformCharacterDetail(
            response.data.character
          );
          updateChatState({ characterData: transformedCharacter });
        } else {
          console.error('获取角色详情失败');
          // 回退到本地数据
          const fallbackCharacter =
            characters[chatId as keyof typeof characters];
          updateChatState({ characterData: fallbackCharacter });
        }
      } catch (error) {
        console.error('获取角色详情错误:', error);
        // 回退到本地数据
        const fallbackCharacter = characters[chatId as keyof typeof characters];
        updateChatState({ characterData: fallbackCharacter });
      } finally {
        updateChatState({ characterLoading: false });
      }
    };

    fetchCharacterDetail();
  }, [chatId, characters]);

  // 根据快速回复按钮获取角色的动态回复内容
  const getCharacterResponse = useCallback(
    (replyText: string) => {
      if (!character?.resource) {
        return null; // 没有resource数据，返回null使用默认处理
      }

      // 查找按钮配置
      const buttonConfig = QUICK_REPLY_BUTTONS.find(
        (btn) => btn.text === replyText
      );
      if (!buttonConfig || buttonConfig.type === 'text') {
        return null; // 文本类型或未找到配置
      }

      const { resourceType, resourceIndex } = buttonConfig;
      if (!resourceType || resourceIndex === undefined) {
        return null;
      }

      const resource = character.resource[resourceType];
      if (!resource || !resource[resourceIndex]) {
        return null; // 资源不存在
      }

      const responseData = {
        type: buttonConfig.type,
        text: resource[resourceIndex].description,
        url: resource[resourceIndex].url,
        msgType: buttonConfig.type as
          | 'text'
          | 'image'
          | 'voice'
          | 'call'
          | 'video',
      };

      console.log('🎯 快速回复按钮映射结果:', {
        按钮文本: replyText,
        按钮类型: buttonConfig.type,
        资源类型: resourceType,
        资源索引: resourceIndex,
        响应数据: responseData,
      });

      return responseData;
    },
    [character?.resource]
  );

  // 发送聊天计数的函数 - 移到这里避免初始化顺序问题
  const sendChatCounting = useCallback(
    async (messageText: string, characterId: number, response?: string) => {
      if (!token) return;

      try {
        // const chatRequest: ChatSendRequest = {
        //   characterId: characterId,
        //   message: messageText,
        //   msgType: 'text', // 默认为文本类型
        //   response: response, // 可选的AI回复内容
        // };
        // console.log('🚀 发送聊天计数请求:', chatRequest);
        // const result = await sendChatForCounting(token, chatRequest);
        // if (result.success) {
        //   console.log('✅ 聊天计数成功:', result.message);
        // } else {
        //   console.warn('⚠️ 聊天计数失败:', result.message);
        //   // 暂时禁用支付跳转
        // }
      } catch (error) {
        console.error('❌ 聊天计数API调用失败:', error);
        // 暂时禁用支付跳转
      }
    },
    [token, updateUiState]
  );

  const handleQuickReply = useCallback(
    async (reply: string) => {
      // 如果是文字类型的快速回复，只将文案放入输入框，不触发AI回应
      if (replyType === 'text') {
        setInputValue(reply);
        return;
      }

      // 尝试获取角色的动态回复内容（图片/语音类型）
      const characterResponse = getCharacterResponse(reply);

      if (characterResponse) {
        // 找到了映射的动态内容，创建对应的消息
        const userMessage: Message = {
          id: Date.now(),
          sender: 'user',
          text: reply,
          timestamp: new Date().toISOString(),
        };

        const aiMessage: Message = {
          id: Date.now() + 1,
          sender: 'ai',
          text: characterResponse.text,
          timestamp: new Date().toISOString(),
          ...(characterResponse.type === 'image'
            ? {
                hasImage: true,
                imageSrc: characterResponse.url,
              }
            : {
                hasAudio: true,
                audioSrc: characterResponse.url, // 原始URL，会在播放时添加AWS前缀
                // audioDuration 会在音频加载后动态设置
              }),
        };

        // 更新消息列表
        addMessage(userMessage);
        addMessage(aiMessage);

        // 发送聊天计数请求
        const characterId =
          typeof character.id === 'string'
            ? parseInt(character.id, 10)
            : character.id;

        // 构建完整的响应数据结构，用于历史记录的正确展示
        const responseData = {
          type: characterResponse.type,
          text: characterResponse.text,
          url: characterResponse.url,
          msgType: characterResponse.msgType,
          // audioDuration 会在音频加载后动态获取，不在这里硬编码
        };

        const chatRequest: ChatSendRequest = {
          characterId: characterId,
          message: reply,
          msgType: characterResponse.msgType,
          response: JSON.stringify(responseData), // 传递完整的数据结构
        };

        try {
          const result = await sendChatForCounting(token!, chatRequest);
          if (!result.success) {
            console.log('聊天计数失败');
          }
        } catch (error) {
          if (error instanceof Error && error.message === 'NEED_SUBSCRIPTION') {
            console.log('💰 快速回复检测到余额不足，唤起支付弹窗');
            updateUiState({ showSubscriptionModal: true });
          } else {
            console.error('❌ 聊天计数失败:', error);
          }
        }

        return;
      }

      // 检查是否是旧版硬编码响应（向后兼容）
      if (isHardcodedResponse(reply)) {
        const response = getHardcodedResponse(reply);

        const userMessage: Message = {
          id: Date.now(),
          sender: 'user',
          text: reply,
          timestamp: new Date().toISOString(),
        };

        const aiMessage: Message = {
          id: Date.now() + 1,
          sender: 'ai',
          text: response.text,
          timestamp: new Date().toISOString(),
          hasImage: true,
          imageSrc: response.imageSrc,
        };

        addMessage(userMessage);
        addMessage(aiMessage);

        const characterId =
          typeof character.id === 'string'
            ? parseInt(character.id, 10)
            : character.id;

        // 构建完整的响应数据结构，用于历史记录的正确展示
        const responseData = {
          type: 'image',
          text: response.text,
          url: response.imageSrc,
          msgType: 'image' as const,
        };

        const chatRequest: ChatSendRequest = {
          characterId: characterId,
          message: reply,
          msgType: 'image',
          response: JSON.stringify(responseData), // 传递完整的数据结构
        };

        try {
          const result = await sendChatForCounting(token!, chatRequest);
          if (!result.success) {
            console.log('硬编码图片聊天计数失败');
          }
        } catch (error) {
          if (error instanceof Error && error.message === 'NEED_SUBSCRIPTION') {
            console.log('💰 硬编码图片快速回复检测到余额不足，唤起支付弹窗');
            updateUiState({ showSubscriptionModal: true });
          } else {
            console.error('❌ 硬编码图片聊天计数失败:', error);
          }
        }

        return;
      }

      // 如果到这里，说明是图片/语音类型但没有找到对应的资源数据
      // 这种情况下不执行任何操作，避免将按钮文本错误地设置到输入框
      console.warn('⚠️ 快速回复按钮没有找到对应的资源数据:', reply);
    },
    [
      replyType,
      setInputValue,
      addMessage,
      character,
      getCharacterResponse,
      sendChatCounting,
      token,
      updateUiState,
    ]
  );

  const handleSendMessage = async () => {
    if (!inputValue.trim() || !token) return;

    const messageText = inputValue.trim();
    const characterId =
      typeof character.id === 'string'
        ? parseInt(character.id, 10)
        : character.id; // 转换为数字类型
    setInputValue('');

    // 添加用户消息
    const userMessage: Message = {
      id: Date.now(),
      sender: 'user',
      text: messageText,
      timestamp: new Date().toISOString(),
    };

    // 创建AI消息占位
    const aiMessageId = Date.now() + 1;
    const tempAiMessage: Message = {
      id: aiMessageId,
      sender: 'ai',
      text: '',
      timestamp: new Date().toISOString(),
      isThinking: true,
    };

    addMessage(userMessage);
    addMessage(tempAiMessage);
    updateChatState({ isLoading: true });

    try {
      let accumulatedContent = '';

      // 使用V2接口作为默认
      const streamMethod = useNewAPI
        ? ChatAPI.sendStreamMessageV2
        : ChatAPI.sendStreamMessage;

      await streamMethod(
        messageText,
        characterId, // 使用变量
        (content: string) => {
          accumulatedContent += content;
          updateStreamingMessage(aiMessageId, accumulatedContent);
        },
        async () => {
          // 流式完成后，发送聊天计数请求
          console.log(
            `✅ ${
              useNewAPI ? '新接口V2' : '现有接口V1'
            }流式对话完成，发送聊天计数请求`
          );
          await sendChatCounting(messageText, characterId, accumulatedContent);

          updateChatState({ isLoading: false });
          console.log(
            `✅ ${
              useNewAPI ? '新接口V2' : '现有接口V1'
            }流式对话完成，保持当前消息状态`
          );

          // 可选：延迟很久后再同步，避免覆盖用户刚发送的消息
          // setTimeout(async () => {
          //   console.log('🔄 后台同步历史记录...');
          //   await reloadChatHistory();
          // }, 30000); // 30秒后再同步
        },
        (error: string) => {
          // Check for insufficient balance error (New API V2 only)
          if (error === 'NEED_SUBSCRIPTION' && useNewAPI) {
            console.log('💰 V2接口检测到余额不足，唤起支付弹窗');
            console.log(
              '🔄 当前uiState.showSubscriptionModal状态:',
              uiState.showSubscriptionModal
            );
            updateMessage(aiMessageId, {
              text: `Insufficient account balance. Please recharge to continue.`,
              isThinking: false,
            });
            // 唤起支付弹窗
            console.log(
              '🚀 正在调用updateUiState设置showSubscriptionModal为true'
            );
            updateUiState({ showSubscriptionModal: true });
            console.log('✅ updateUiState调用完成');
          } else {
            updateMessage(aiMessageId, {
              text: `Error: ${error}`,
              isThinking: false,
            });
          }
          updateChatState({ isLoading: false });
        }
      );
    } catch (error) {
      console.error('流式处理失败:', error);

      // Check for insufficient balance error (New API V2 only)
      if (
        error instanceof Error &&
        error.message === 'NEED_SUBSCRIPTION' &&
        useNewAPI
      ) {
        console.log('💰 V2接口捕获余额不足错误，唤起支付弹窗');
        console.log(
          '🔄 当前uiState.showSubscriptionModal状态:',
          uiState.showSubscriptionModal
        );
        updateMessage(aiMessageId, {
          text: `Insufficient account balance. Please recharge to continue.`,
          isThinking: false,
        });
        // 唤起支付弹窗
        console.log('🚀 正在调用updateUiState设置showSubscriptionModal为true');
        updateUiState({ showSubscriptionModal: true });
        console.log('✅ updateUiState调用完成');
      } else {
        updateMessage(aiMessageId, {
          text: `流式处理失败: ${
            error instanceof Error ? error.message : String(error)
          }`,
          isThinking: false,
        });
      }

      updateChatState({ isLoading: false });
    }
  };

  // API切换处理函数
  const handleAPIToggle = useCallback(() => {
    setUseNewAPI(!useNewAPI);
    console.log(`🔄 切换到${!useNewAPI ? '新接口V2' : '现有接口V1'}`);
  }, [useNewAPI]);

  const messagesEndRef = useRef<HTMLDivElement>(null);

  // 动态生成快速回复按钮文本 - 根据角色资源数据
  const quickReplies = useMemo(() => {
    const resource = character?.resource;

    // 按类型分组可用按钮
    const availableButtons = {
      text: QUICK_REPLY_BUTTONS.filter((btn) => btn.type === 'text'),
      picture: QUICK_REPLY_BUTTONS.filter(
        (btn) =>
          btn.type === 'image' &&
          resource?.image &&
          btn.resourceIndex !== undefined &&
          btn.resourceIndex < resource.image.length
      ),
      voice: QUICK_REPLY_BUTTONS.filter(
        (btn) =>
          btn.type === 'voice' &&
          resource?.voice_plays &&
          btn.resourceIndex !== undefined &&
          btn.resourceIndex < resource.voice_plays.length
      ),
    };

    const result = {
      text: availableButtons.text.map((btn) => btn.text),
      picture: availableButtons.picture.map((btn) => btn.text),
      voice: availableButtons.voice.map((btn) => btn.text),
    };

    console.log('🎯 快速回复按钮生成结果:', {
      角色: character?.name,
      图片资源数量: resource?.image?.length || 0,
      语音资源数量: resource?.voice_plays?.length || 0,
      可用按钮数量: {
        文本: result.text.length,
        图片: result.picture.length,
        语音: result.voice.length,
      },
    });

    return result;
  }, [character?.resource, character?.name]);

  const handleKeyDown = (e: React.KeyboardEvent) => {
    // 🔧 修复：检查是否正在使用输入法进行组合输入（如中文拼音输入）
    if (e.key === 'Enter' && !e.shiftKey && !e.nativeEvent.isComposing) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  // 🎵 使用自定义 hook 管理音频播放
  const { handleAudioPlay, handleAudioDurationUpdate } = useAudioPlayer({
    messages,
    updateMessage,
    updateUiState,
    isPlaying: uiState.isPlaying,
  });

  const handleProfileClick = useCallback(() => {
    updateUiState({ showProfileModal: true });
  }, [updateUiState]);

  const handleCallButtonClick = useCallback(() => {
    updateUiState({ showCallDialog: true });
  }, [updateUiState]);

  const handleFollowToggle = useCallback(async () => {
    if (!token) {
      console.error('用户未登录');
      return;
    }

    const characterId = parseInt(chatId);
    if (!characterId) {
      console.error('无效的角色ID');
      return;
    }

    try {
      // 设置加载状态
      updateUiState({ isFollowLoading: true });

      // 调用关注/取关API
      const response = await followCharacter(
        token,
        characterId,
        !uiState.isFollowing
      );

      if (response.success) {
        // 更新关注状态
        updateUiState({
          isFollowing: !uiState.isFollowing,
          isFollowLoading: false,
        });

        // 显示成功消息
        console.log('关注状态更新成功:', response.message);

        // 可以在这里添加toast提示
        // toast.success(response.message);
      } else {
        // 处理失败情况
        console.error('关注操作失败:', response.message);
        updateUiState({ isFollowLoading: false });

        // 可以在这里添加toast提示
        // toast.error(response.message);
      }
    } catch (error) {
      console.error('关注操作出错:', error);
      updateUiState({ isFollowLoading: false });

      // 可以在这里添加toast提示
      // toast.error('关注操作失败，请稍后重试');
    }
  }, [token, chatId, uiState.isFollowing, updateUiState]);

  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  // Show recommendation after a delay
  useEffect(() => {
    const timer = setTimeout(() => {
      if (Math.random() > 0.5) {
        setShowRecommendation(true);
      }
    }, 15000);

    return () => clearTimeout(timer);
  }, []);

  // Get a random character ID that's different from the current one
  const getRandomCharacterId = () => {
    const characterIds = Object.keys(characters).filter(
      (id) => id !== chatId.toString()
    );
    const randomIndex = Math.floor(Math.random() * characterIds.length);
    return characterIds[randomIndex];
  };

  const recommendedCharacterId = getRandomCharacterId();
  const recommendedCharacter =
    characters[recommendedCharacterId as keyof typeof characters];

  // 添加清除聊天记录的函数 - 使用 hook 简化
  const handleClearChatHistory = async () => {
    if (!token) return;

    updateUiState({ isClearingHistory: true });
    try {
      const characterId =
        typeof character.id === 'string'
          ? parseInt(character.id, 10)
          : character.id;

      const success = await clearChatHistory(characterId, clearMessages);
      if (success) {
        updateUiState({ showClearDialog: false });
      }
    } catch (error) {
      console.error('清除聊天记录失败:', error);
      // 可以添加错误提示
    } finally {
      updateUiState({ isClearingHistory: false });
    }
  };

  // 重新加载历史记录函数（从 hook 中获取）
  const handleReloadChatHistory = useCallback(async () => {
    if (!token) return;

    const characterId =
      typeof character.id === 'string'
        ? parseInt(character.id, 10)
        : character.id;

    await reloadChatHistory(characterId, replaceMessages);
  }, [token, character.id, reloadChatHistory, replaceMessages]);

  // 处理心形泡泡文本选择的回调函数
  const handleHeartBubbleTextSelect = useCallback(
    (text: string) => {
      // 直接用选中的文本覆盖输入框内容
      setInputValue(text);
    },
    [setInputValue]
  );

  // 切换心形泡泡收缩/展开状态
  const handleToggleHeartBubbles = useCallback(() => {
    setIsHeartBubblesCollapsed((prev) => !prev);
  }, []);

  return (
    <div className="flex h-screen overflow-hidden chat-layout">
      {/* PC端侧边栏 */}
      <div className="hidden md:block flex-shrink-0 h-full">
        <Sidebar />
      </div>

      <div className="flex flex-1 h-full overflow-hidden min-h-0">
        {/* Left sidebar - Chat list */}
        <div
          className={`${uiState.showMobileChatList ? 'block' : 'hidden'} ${
            uiState.showChatList ? 'sm:block' : 'sm:hidden'
          } w-full sm:w-80 lg:w-96 xl:w-80 bg-[#120518] border-r border-[#3a1a44] flex flex-col h-full min-h-0 md:max-w-xs lg:max-w-sm xl:max-w-none transition-all duration-300 flex-shrink-0`}
        >
          {/* Header - 固定高度 */}
          <div className="flex-shrink-0 p-2 sm:p-3 md:p-4 lg:p-6 border-b border-[#3a1a44]">
            <div className="flex items-center justify-between">
              <h2 className="text-lg sm:text-xl lg:text-2xl font-bold">Chat</h2>
              <div className="flex items-center space-x-2">
                {/* 桌面端缩放按钮 */}
                <button
                  className="hidden sm:flex min-h-[44px] min-w-[44px] p-2 rounded-full hover:bg-[#2a1a34] items-center justify-center"
                  onClick={() =>
                    updateUiState({ showChatList: !uiState.showChatList })
                  }
                  title={
                    uiState.showChatList ? 'Hide chat list' : 'Show chat list'
                  }
                >
                  <ChevronLeft className="h-5 w-5 text-gray-400" />
                </button>
                {/* 移动端关闭按钮 */}
                <button
                  className="min-h-[44px] min-w-[44px] p-2 rounded-full hover:bg-[#2a1a34] sm:hidden flex items-center justify-center"
                  onClick={() => updateUiState({ showMobileChatList: false })}
                >
                  <X className="h-5 w-5 text-gray-400" />
                </button>
              </div>
            </div>
          </div>

          {/* Scrollable content area - 占据剩余空间 */}
          <div className="flex-1 overflow-y-auto overflow-x-hidden chat-list-scroll min-h-0">
            <div className="p-2 space-y-1 sm:space-y-2">
              {recentChatsLoading ? (
                <div className="flex justify-center items-center py-8 sm:py-12">
                  <div className="text-gray-400 text-xs sm:text-sm">
                    Loading chat list...
                  </div>
                </div>
              ) : (
                <>
                  {/* 临时对话窗口 - 显示当前对话但不在历史列表中的情况 */}
                  {chatId &&
                    character &&
                    !recentChats.find(
                      (chat) => chat.id.toString() === chatId
                    ) && (
                      <div className="p-2 sm:p-3 lg:p-4 bg-[#3a1a44] border border-pink-500/30 rounded-xl">
                        <div className="flex items-start">
                          <div className="relative mr-2 sm:mr-3 lg:mr-4 flex-shrink-0">
                            <div className="h-10 w-10 sm:h-12 sm:w-12 lg:h-14 lg:w-14 rounded-full overflow-hidden">
                              <Image
                                src={getAvatarUrl(
                                  character.avatarSrc ||
                                    character.imageSrc ||
                                    '/placeholder.svg'
                                )}
                                alt={character.name}
                                width={56}
                                height={56}
                                className="object-cover w-full h-full"
                              />
                            </div>
                            {/* 新对话标识 */}
                            <div className="absolute -top-1 -right-1 bg-green-500 rounded-full h-3 w-3 sm:h-4 sm:w-4 lg:h-5 lg:w-5 flex items-center justify-center">
                              <span className="text-white text-xs">N</span>
                            </div>
                          </div>
                          <div className="flex-1 min-w-0">
                            <div className="flex justify-between items-start">
                              <h4 className="text-xs sm:text-sm lg:text-base font-medium truncate text-pink-400">
                                {character.name}
                              </h4>
                              <span className="text-xs lg:text-sm text-green-400 flex-shrink-0 ml-2">
                                New Chat
                              </span>
                            </div>
                            <p className="text-xs lg:text-sm text-gray-300 truncate">
                              Click to start chatting with {character.name}...
                            </p>
                          </div>
                        </div>
                      </div>
                    )}

                  {recentChats.map((chat) => (
                    <Link
                      href={`/chat/${chat.id}`}
                      key={chat.id}
                      className={`block p-2 sm:p-3 lg:p-4 hover:bg-[#2a1a34] transition-colors rounded-xl ${
                        chat.id.toString() === chatId ? 'bg-[#2a1a34]' : ''
                      }`}
                    >
                      <div className="flex items-start">
                        <div className="relative mr-2 sm:mr-3 lg:mr-4 flex-shrink-0">
                          <div className="h-10 w-10 sm:h-12 sm:w-12 lg:h-14 lg:w-14 rounded-full overflow-hidden">
                            <Image
                              src={getAvatarUrl(
                                chat.imageSrc || '/placeholder.svg'
                              )}
                              alt={chat.name}
                              width={56}
                              height={56}
                              className="object-cover w-full h-full"
                            />
                          </div>
                          {/* 显示未读消息数量 */}
                          {chat.unreadCount > 0 && (
                            <div className="absolute -top-1 -right-1 bg-red-500 rounded-full h-3 w-3 sm:h-4 sm:w-4 lg:h-5 lg:w-5 flex items-center justify-center text-xs font-bold">
                              {chat.unreadCount > 99 ? '99+' : chat.unreadCount}
                            </div>
                          )}
                        </div>
                        <div className="flex-1 min-w-0">
                          <div className="flex justify-between items-start">
                            <h4 className="text-xs sm:text-sm lg:text-base font-medium truncate">
                              {chat.name}
                            </h4>
                            <span className="text-xs lg:text-sm text-gray-400 flex-shrink-0 ml-2">
                              {chat.timestamp}
                            </span>
                          </div>
                          <p className="text-xs lg:text-sm text-gray-400 truncate">
                            {chat.lastMessage}
                          </p>
                        </div>
                      </div>
                    </Link>
                  ))}

                  {recentChats.length === 0 && (
                    <div className="text-center text-gray-400 py-8 sm:py-12">
                      <div className="mb-2">💬</div>
                      <div className="text-xs sm:text-sm">No chat history</div>
                      <div className="text-xs mt-1">
                        Start a conversation to see chats here
                      </div>
                    </div>
                  )}
                </>
              )}
            </div>
          </div>
        </div>

        {/* 当聊天列表隐藏时的展开按钮 */}
        {!uiState.showChatList && (
          <div className="hidden sm:flex items-center justify-center w-12 bg-[#120518] border-r border-[#3a1a44] hover:bg-[#1a0a24] transition-colors">
            <button
              className="min-h-[44px] min-w-[44px] p-2 rounded-full hover:bg-[#2a1a34] transition-all duration-300 flex items-center justify-center"
              onClick={() => updateUiState({ showChatList: true })}
              title="Show chat list"
            >
              <ChevronRight className="h-5 w-5 text-gray-400" />
            </button>
          </div>
        )}

        {/* Main chat area */}
        <div className="flex-1 flex flex-col h-full min-w-0 min-h-0 pb-4 md:pb-0">
          {/* Chat header - 固定高度不被压缩 */}
          <div className="flex-shrink-0">
            <ChatHeader
              character={character}
              characterLoading={characterLoading}
              showProfile={uiState.showProfile}
              onProfileClick={handleProfileClick}
              onCallClick={() => updateUiState({ showCallDialog: true })}
              onClearHistoryClick={() =>
                updateUiState({ showClearDialog: true })
              }
              onMobileSidebarClick={() => {
                // 移动端导航已改为底部导航，这里不再需要侧边栏
                console.log(
                  'Mobile sidebar no longer needed with bottom navigation'
                );
              }}
              onMobileChatListClick={() => {
                // 移动端返回按钮现在直接跳转到/chat页面，不再需要这个回调
                console.log(
                  'Mobile back button now navigates to /chat directly'
                );
              }}
              onProfileToggle={() =>
                updateUiState({ showProfile: !uiState.showProfile })
              }
            />
          </div>

          {/* Messages - 根据登录状态显示不同内容 */}
          {isLoggedIn ? (
            <MessageList
              messages={messages}
              isPlaying={uiState.isPlaying}
              onAudioPlay={handleAudioPlay}
              onAudioDurationUpdate={handleAudioDurationUpdate}
            />
          ) : (
            <GuestMessageList
              sampleMessages={messages}
              character={{
                id: character.id,
                name: character.name,
                avatarSrc: character.avatarSrc,
              }}
            />
          )}

          {/* Recommendation bubble */}
          {uiState.showRecommendation && recommendedCharacter && (
            <div className="relative">
              <div className="absolute bottom-16 sm:bottom-20 right-3 sm:right-6 bg-[#1a0a24] border border-pink-500 rounded-xl p-3 sm:p-5 max-w-xs sm:max-w-sm animate-pulse shadow-lg shadow-pink-500/20">
                <div className="flex items-start space-x-3 sm:space-x-4">
                  <div className="relative">
                    <div className="h-16 w-16 sm:h-20 sm:w-20 rounded-lg overflow-hidden">
                      <Image
                        src={`/placeholder-80x80.png?height=80&width=80&text=${recommendedCharacter.name.charAt(
                          0
                        )}`}
                        alt={recommendedCharacter.name}
                        width={80}
                        height={80}
                        className="object-cover"
                      />
                    </div>
                    <Link href={`/chat/${recommendedCharacterId}`}>
                      <div className="absolute -bottom-1 -right-1 sm:-bottom-2 sm:-right-2 bg-pink-500 rounded-full p-1.5 sm:p-2 shadow-lg">
                        <Phone className="h-3 w-3 sm:h-5 sm:w-5 text-white" />
                      </div>
                    </Link>
                  </div>
                  <div className="flex-1">
                    <p className="text-sm sm:text-base font-medium mb-1 sm:mb-2">
                      Someone new is interested in you 💕
                    </p>
                    <p className="text-xs sm:text-sm text-gray-300 mb-2 sm:mb-3">
                      "{recommendedCharacter.description.split('.')[0]}... Want
                      to meet{' '}
                      <span className="text-pink-400 font-medium">
                        {recommendedCharacter.name}
                      </span>
                      ?"
                    </p>
                    <Link
                      href={`/chat/${recommendedCharacterId}`}
                      className="text-xs sm:text-sm text-pink-400 font-medium"
                    >
                      Meet them now
                    </Link>
                  </div>
                  <button
                    className="text-gray-400 hover:text-white"
                    onClick={() => updateUiState({ showRecommendation: false })}
                  >
                    <X className="h-4 w-4 sm:h-5 sm:w-5" />
                  </button>
                </div>
              </div>
            </div>
          )}

          {/* Heart Bubbles - 快速回复泡泡 (仅登录用户可用) */}
          {isLoggedIn && quickReplyMode === 'bubble' && (
            <div className="flex-shrink-0 px-2 sm:px-4 pb-2">
              <div
                className={`rounded-lg overflow-hidden transition-all duration-300 ease-in-out ${
                  isHeartBubblesCollapsed ? 'h-8 sm:h-10' : 'h-24 sm:h-32'
                }`}
              >
                <FloatingHeartBubbles
                  onSelectText={handleHeartBubbleTextSelect}
                  onToggleCollapse={handleToggleHeartBubbles}
                  isCollapsed={isHeartBubblesCollapsed}
                  className="w-full h-full"
                />
              </div>
            </div>
          )}

          {/* Quick reply suggestions (仅登录用户可用) */}
          {isLoggedIn &&
            (quickReplyMode === 'picture' || quickReplyMode === 'voice') && (
              <QuickReplies
                replyType={quickReplyMode}
                onQuickReply={handleQuickReply}
                quickReplies={quickReplies}
              />
            )}

          {/* Input area - 根据登录状态显示不同输入框 */}
          {isLoggedIn ? (
            <MessageInput
              inputValue={inputValue}
              setInputValue={setInputValue}
              handleSendMessage={handleSendMessage}
              handleKeyDown={handleKeyDown}
              characterName={character?.name || 'Character'}
              isLoading={isLoading}
              replyType={replyType}
              setReplyType={setReplyType}
              showReplyOptions={uiState.showReplyOptions}
              pictureQuota={pictureQuota}
              voiceQuota={voiceQuota}
              quickReplyMode={quickReplyMode}
              onQuickReplyModeChange={handleQuickReplyModeChange}
            />
          ) : (
            <GuestMessageInput characterName={character?.name || 'Character'} />
          )}
        </div>

        {/* 微信风格悬浮通话按钮 - 当通话进行中且对话框关闭时显示 */}
        {callState.status === "active" && !uiState.showCallDialog && (
          <div className="fixed right-4 top-1/2 transform -translate-y-1/2 z-30">
            <button
              onClick={() => updateUiState({ showCallDialog: true })}
              className="group relative w-14 h-14 sm:w-16 sm:h-16 bg-black/40 hover:bg-black/60 backdrop-blur-md rounded-xl border border-white/10 hover:border-white/20 transition-all duration-300 shadow-lg hover:shadow-xl"
            >
              {/* 通话图标 */}
              <div className="absolute inset-0 flex items-center justify-center">
                <Phone className="w-6 h-6 sm:w-7 sm:h-7 text-white group-hover:text-pink-300 transition-colors" />
              </div>

              {/* 通话状态指示器 */}
              <div className="absolute -top-1 -right-1 w-4 h-4 bg-gradient-to-r from-pink-500 to-red-500 rounded-full flex items-center justify-center">
                <div className="w-2 h-2 bg-white rounded-full animate-pulse"></div>
              </div>

              {/* 通话时长显示 */}
              <div className="absolute -bottom-6 left-1/2 transform -translate-x-1/2 bg-black/60 backdrop-blur-sm px-2 py-0.5 rounded text-xs text-white font-mono whitespace-nowrap">
                {Math.floor(callState.time / 60)}:{(callState.time % 60).toString().padStart(2, '0')}
              </div>

              {/* 悬停提示 */}
              <div className="absolute right-full mr-3 top-1/2 transform -translate-y-1/2 bg-black/80 backdrop-blur-sm text-white text-xs px-2 py-1 rounded whitespace-nowrap opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none">
                Return to call
              </div>
            </button>
          </div>
        )}

        {/* Right sidebar - Character Profile */}
        <CharacterProfile
          character={{
            ...character,
            favoriteCount: getFakeStatsForCharacter(character).likeCount, // 修正：使用likeCount作为收藏数量
          }}
          characterLoading={characterLoading}
          isFollowing={uiState.isFollowing}
          isFollowLoading={uiState.isFollowLoading}
          isFavorite={uiState.isFavorite}
          userSubscriptionLevel={subscriptionLevel}
          showProfile={uiState.showProfile}
          onFollowToggle={handleFollowToggle}
          onFavoriteToggle={() =>
            updateUiState({ isFavorite: !uiState.isFavorite })
          }
          onShareCharacter={handleShareCharacter}
        />
      </div>

      {/* Share Character Dialog */}
      <ShareDialog
        isOpen={uiState.showShareDialog}
        onClose={() => updateUiState({ showShareDialog: false })}
        characterName={character.name}
        characterId={character.id}
      />

      {/* Character Profile Modal */}
      <ProfileModal
        isOpen={uiState.showProfileModal}
        onClose={() => updateUiState({ showProfileModal: false })}
        character={character}
        isFavorite={uiState.isFavorite}
        onSubscriptionButtonClick={handleSubscriptionButtonClick}
      />

      {/* Call Dialog */}
      <CallDialog
        isOpen={uiState.showCallDialog}
        onClose={() => updateUiState({ showCallDialog: false })}
        onMinimize={() => {
          updateUiState({ showCallDialog: false });
        }}
        character={{
          id: character.id,
          name: character.name,
          avatarSrc: character.avatarSrc,
          resource: character.resource, // 传递资源信息用于语音通话
        }}
        callState={callState}
        isMuted={uiState.isMuted}
        onMuteToggle={() => updateUiState({ isMuted: !uiState.isMuted })}
        onCallStateUpdate={updateCallState}
        onSubscriptionModalOpen={() => {
          console.log('通话唤起支付弹窗');
          updateUiState({ showSubscriptionModal: true });
        }}
      />

      {/* 清除聊天记录确认对话框 */}
      <Dialog
        open={uiState.showClearDialog}
        onOpenChange={() => updateUiState({ showClearDialog: false })}
      >
        <DialogContent className="bg-[#1a0a24] border-[#3a1a44] text-white">
          <DialogHeader>
            <DialogTitle className="text-lg sm:text-xl">
              Clear Chat History
            </DialogTitle>
            <DialogDescription className="text-gray-400 text-sm">
              Are you sure you want to clear all chat history with{' '}
              {character.name}? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter className="flex gap-3">
            <Button
              variant="outline"
              onClick={() => updateUiState({ showClearDialog: false })}
              disabled={uiState.isClearingHistory}
            >
              Cancel
            </Button>
            <Button
              className="bg-red-500 hover:bg-red-600"
              onClick={handleClearChatHistory}
              disabled={uiState.isClearingHistory}
            >
              {uiState.isClearingHistory ? 'Clearing...' : 'Confirm Clear'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 支付弹窗 */}
      <Dialog
        open={uiState.showSubscriptionModal}
        onOpenChange={() => {
          console.log('🎭 支付弹窗onOpenChange被调用');
          updateUiState({ showSubscriptionModal: false });
        }}
      >
        <DialogContent className="bg-[#1a0a24] border-[#3a1a44] text-white max-w-md">
          <DialogHeader>
            <DialogTitle className="text-center text-xl">
              💎 Upgrade Required
            </DialogTitle>
            <DialogDescription className="text-center text-gray-400">
              You've run out of usage. Upgrade to continue enjoying unlimited
              conversations and features.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="text-center">
              <div className="text-2xl mb-2">🚀</div>
              <h3 className="text-lg font-semibold mb-2">Premium Features</h3>
              <ul className="text-sm text-gray-300 space-y-1 text-left">
                <li>• Unlimited conversations</li>
                <li>• Voice calls & messages</li>
                <li>• Exclusive content</li>
                <li>• Priority support</li>
              </ul>
            </div>
          </div>
          <DialogFooter className="flex gap-3">
            <Button
              variant="outline"
              onClick={() => updateUiState({ showSubscriptionModal: false })}
              className="flex-1"
            >
              Maybe Later
            </Button>
            <Button
              className="bg-pink-500 hover:bg-pink-600 flex-1"
              onClick={() => {
                updateUiState({ showSubscriptionModal: false });
                router.push('/premium');
              }}
            >
              Upgrade Now
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
