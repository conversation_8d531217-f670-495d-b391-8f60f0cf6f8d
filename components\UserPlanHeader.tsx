'use client';

import React from 'react';
import Link from 'next/link';
import { Crown, LogOut } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import SmartImage from '@/components/SmartImage';
import { getAvatarUrl } from '@/lib/image-utils';
import { useUser, useUserPlan } from '@/contexts/UserContext';

interface UserPlanHeaderProps {
  className?: string;
  showLogout?: boolean;
  showUpgradeLink?: boolean;
}

export default function UserPlanHeader({ 
  className = "", 
  showLogout = false, 
  showUpgradeLink = true 
}: UserPlanHeaderProps) {
  const { user, isLoggedIn, isLoading, logout } = useUser();
  const { planDisplayName, planBadgeStyle, expireTimeFormatted, isPremium } = useUserPlan();

  // 防止hydration不匹配，在客户端挂载前显示加载状态
  const [mounted, setMounted] = React.useState(false);

  React.useEffect(() => {
    setMounted(true);
  }, []);

  // 如果未挂载或正在加载，显示加载状态
  if (!mounted || isLoading) {
    return (
      <div className={`flex items-center space-x-3 ${className}`}>
        <div className="animate-pulse">
          <div className="h-8 w-8 bg-gray-600 rounded-full"></div>
        </div>
        <div className="animate-pulse">
          <div className="h-6 w-20 bg-gray-600 rounded-full"></div>
        </div>
      </div>
    );
  }


  if (!isLoggedIn) {
    return (
      <div className={`flex items-center space-x-3 ${className}`}>
        <Link href="/login">
          <Button className="bg-pink-500 hover:bg-pink-600 text-white">
            Sign in
          </Button>
        </Link>
        <Link href="/register">
          <Button className="bg-pink-500 hover:bg-pink-600 text-white">
            Sign up
          </Button>
        </Link>
      </div>
    );
  }

  return (
    <div className={`flex items-center space-x-3 ${className}`}>
      {/* 用户头像和套餐信息 */}
      <Link href="/profile">
        <div className="flex items-center space-x-2 bg-[#1a0a24] px-3 py-1 rounded-full text-sm hover:bg-[#2a1a34] transition-colors cursor-pointer">
          <div className="h-8 w-8 relative">
            <SmartImage
              src={getAvatarUrl(user?.avatar)}
              alt={user?.username || user?.email || "User"}
              width={32}
              height={32}
              className="rounded-full object-cover"
            />
          </div>
          <Badge
            variant="outline"
            className={planBadgeStyle}
          >
            <Crown className="h-3 w-3 mr-1" />
            {planDisplayName}
          </Badge>
        </div>
      </Link>

      {/* 到期时间显示（如果不是免费套餐） */}
      {isPremium && expireTimeFormatted !== "No expiration" && (
        <span className="text-xs text-gray-400 hidden md:block">
          {expireTimeFormatted}
        </span>
      )}

      {/* 升级链接（仅对免费用户显示） */}
      {showUpgradeLink && planDisplayName === "Free Plan" && (
        <Link href="/premium" className="text-pink-400 hover:text-pink-300 text-sm font-medium transition-colors hidden md:block">
          Upgrade Plan →
        </Link>
      )}

      {/* 用户名和登出按钮 */}
      <span className="text-white hidden lg:block">
        Welcome, {user?.username || user?.email?.split('@')[0]}
      </span>

      {/* 登出按钮 */}
      {showLogout && (
        <Button
          onClick={logout}
          variant="outline"
          size="sm"
          className="border-red-500/30 text-red-400 hover:bg-red-500/20"
        >
          <LogOut className="h-4 w-4 mr-2" />
          Logout
        </Button>
      )}
    </div>
  );
}