/**
 * 套餐订阅相关的React Hooks
 * 用于管理Subscription/Payment页面的状态和数据
 */

import { useState, useEffect, useCallback } from 'react';
import {
  getSubscriptionPlans,
  subscribePlan,
  type SubscriptionPlan,
} from '@/lib/api/subscription';
import { UserAPI } from '@/lib/api/user';

// ====================
// 套餐列表Hook
// ====================

export function useSubscriptionPlans() {
  const [plans, setPlans] = useState<SubscriptionPlan[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchPlans = useCallback(async () => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await getSubscriptionPlans();
      setPlans(response.data.plans);
      
    } catch (err) {
      // 出现错误时设置空数据，避免循环调用
      setPlans([]);
      setError(err instanceof Error ? err.message : 'Failed to fetch subscription plans');
      console.error('获取套餐列表失败:', err);
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchPlans();
  }, [fetchPlans]);

  return {
    plans,
    loading,
    error,
    refetch: fetchPlans,
  };
}

// ====================
// 订阅操作Hook
// ====================

export function useSubscribeAction() {
  const [loading, setLoading] = useState(false);

  const subscribe = useCallback(async (planId: number) => {
    const token = localStorage.getItem('token');
    if (!token) {
      throw new Error('用户未登录');
    }

    setLoading(true);
    try {
      const response = await subscribePlan(planId);
      return response.data;
      
    } catch (err) {
      console.error('订阅套餐失败:', err);
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  return {
    subscribe,
    loading,
  };
}

// ====================
// 用户订阅状态Hook
// ====================

export function useUserSubscription() {
  const [subscriptionStatus, setSubscriptionStatus] = useState<{
    isSubscribed: boolean;
    plan: string;
    expiresAt?: string;
  }>(() => {
    // 从 localStorage 读取缓存的订阅状态
    if (typeof window !== 'undefined') {
      const cached = localStorage.getItem('userSubscriptionStatus');
      if (cached) {
        try {
          return JSON.parse(cached);
        } catch (e) {
          console.error('解析订阅状态缓存失败:', e);
        }
      }
    }
    return { isSubscribed: false, plan: 'Free' };
  });
  
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchSubscriptionStatus = useCallback(async () => {
    // 检查用户是否登录
    const token = localStorage.getItem('token');
    if (!token) {
      // 未登录用户，设置为Free
      setSubscriptionStatus({ isSubscribed: false, plan: 'Free' });
      setLoading(false);
      return;
    }

    setLoading(true);
    setError(null);
    
    try {
      const status = await UserAPI.getSubscriptionStatus();
      
      // 标准化plan字段 - 将小写转换为首字母大写
      const standardizedPlan = status.plan.charAt(0).toUpperCase() + status.plan.slice(1).toLowerCase();
      
      const updatedStatus = {
        ...status,
        plan: standardizedPlan
      };
      
      setSubscriptionStatus(updatedStatus);
      
      // 缓存到 localStorage
      localStorage.setItem('userSubscriptionStatus', JSON.stringify(updatedStatus));
      
      console.log('📊 用户订阅状态已更新:', updatedStatus);
      
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch subscription status');
      console.error('获取订阅状态失败:', err);
      
      // 设置默认值，但不覆盖可能有效的缓存数据
      if (!subscriptionStatus.plan || subscriptionStatus.plan === 'Free') {
        setSubscriptionStatus({ isSubscribed: false, plan: 'Free' });
      }
    } finally {
      setLoading(false);
    }
  }, [subscriptionStatus.plan]);

  // 获取格式化的订阅级别
  const getSubscriptionLevel = useCallback((): 'Free' | 'Lite' | 'Basic' | 'Premium' => {
    const plan = subscriptionStatus.plan;
    
    // 处理各种可能的plan值
    switch (plan?.toLowerCase()) {
      case 'free':
        return 'Free';
      case 'lite':
        return 'Lite';
      case 'basic':
        return 'Basic';
      case 'premium':
        return 'Premium';
      default:
        return 'Free';
    }
  }, [subscriptionStatus.plan]);

  // 检查是否是Premium用户（Basic和Premium都算Premium权限）
  const isPremiumUser = useCallback(() => {
    const level = getSubscriptionLevel();
    return level === 'Basic' || level === 'Premium';
  }, [getSubscriptionLevel]);

  // 检查特定功能权限
  const hasFeature = useCallback((feature: 'nsfw' | 'multiImage' | 'voiceCall' | 'unlimited') => {
    const level = getSubscriptionLevel();
    
    switch (feature) {
      case 'nsfw':
        return level !== 'Free'; // Lite及以上都有NSFW权限
      case 'multiImage':
        return level === 'Basic' || level === 'Premium'; // Basic及以上有多图生成
      case 'voiceCall':
        return level !== 'Free'; // Lite及以上有语音通话
      case 'unlimited':
        return level === 'Premium'; // 只有Premium有无限制
      default:
        return false;
    }
  }, [getSubscriptionLevel]);

  useEffect(() => {
    fetchSubscriptionStatus();
  }, [fetchSubscriptionStatus]);

  return {
    subscriptionStatus,
    subscriptionLevel: getSubscriptionLevel(),
    isPremiumUser: isPremiumUser(),
    hasFeature,
    loading,
    error,
    refetch: fetchSubscriptionStatus,
  };
}

// ====================
// 套餐数据处理Hook
// ====================

export function usePlanData() {
  // TODO: 实现套餐数据的分组、排序、过滤逻辑
  
  const groupPlansByLevel = useCallback((plans: SubscriptionPlan[]) => {
    // 按订阅级别分组套餐
    const grouped = plans.reduce((acc, plan) => {
      if (!acc[plan.level]) {
        acc[plan.level] = [];
      }
      acc[plan.level].push(plan);
      return acc;
    }, {} as Record<string, SubscriptionPlan[]>);

    return grouped;
  }, []);

  const sortPlansByPrice = useCallback((plans: SubscriptionPlan[]) => {
    // 按价格排序套餐
    return [...plans].sort((a, b) => a.price - b.price);
  }, []);

  const getMonthlyAndYearlyPlans = useCallback((plans: SubscriptionPlan[]) => {
    // 分离月付和年付套餐
    const monthly = plans.filter(plan => plan.period === 30);
    const yearly = plans.filter(plan => plan.period === 365);
    
    return { monthly, yearly };
  }, []);

  return {
    groupPlansByLevel,
    sortPlansByPrice,
    getMonthlyAndYearlyPlans,
  };
}