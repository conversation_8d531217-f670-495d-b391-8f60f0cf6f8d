/**
 * 角色标签相关的React Hooks
 * 用于管理标签数据和过滤标签功能
 */

import { useState, useEffect, useCallback } from 'react';
import {
  getCharacterLabels,
  convertLabelsToFilterTags,
  type Label,
} from '@/lib/api/labels';

// ====================
// 标签列表Hook
// ====================

export function useCharacterLabels() {
  const [labels, setLabels] = useState<Label[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchLabels = useCallback(async () => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await getCharacterLabels();
      setLabels(response.data.labels);
      
    } catch (err) {
      // 出现错误时设置空数据，避免循环调用
      setLabels([]);
      setError(err instanceof Error ? err.message : 'Failed to fetch character labels');
      console.error('获取标签列表失败:', err);
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchLabels();
  }, [fetchLabels]);

  return {
    labels,
    loading,
    error,
    refetch: fetchLabels,
  };
}

// ====================
// 过滤标签Hook
// ====================

export function useFilterTags(gender: 'male' | 'female') {
  const [filterTags, setFilterTags] = useState<string[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchFilterTags = useCallback(async () => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await getCharacterLabels();
      const tags = convertLabelsToFilterTags(response.data.labels, gender);
      setFilterTags(tags);
      
    } catch (err) {
      // 出现错误时设置空标签数组
      setFilterTags([]);
      setError(err instanceof Error ? err.message : 'Failed to fetch filter tags');
      console.error('获取过滤标签失败:', err);
    } finally {
      setLoading(false);
    }
  }, [gender]);

  useEffect(() => {
    fetchFilterTags();
  }, [fetchFilterTags]);

  return {
    filterTags,
    loading,
    error,
    refetch: fetchFilterTags,
  };
}

// ====================
// 组合Hook - 标签数据管理
// ====================

export function useLabelsData() {
  const { labels, loading: labelsLoading, error: labelsError } = useCharacterLabels();
  
  // 根据性别获取过滤标签
  const getFilterTagsByGender = useCallback((gender: 'male' | 'female') => {
    if (labelsLoading || labelsError) {
      return []; // 加载中或出错时返回空数组
    }
    
    return convertLabelsToFilterTags(labels, gender);
  }, [labels, labelsLoading, labelsError]);

  // 根据标签名称查找标签详情
  const getLabelByName = useCallback((labelName: string) => {
    return labels.find(label => label.name === labelName);
  }, [labels]);

  // 检查标签是否存在
  const hasLabel = useCallback((labelName: string) => {
    return labels.some(label => label.name === labelName);
  }, [labels]);

  return {
    labels,
    loading: labelsLoading,
    error: labelsError,
    getFilterTagsByGender,
    getLabelByName,
    hasLabel,
  };
}