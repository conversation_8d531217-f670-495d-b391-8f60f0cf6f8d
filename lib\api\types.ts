// API 通用类型定义

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
  statusCode?: number;
}

export interface PaginatedResponse<T> {
  items: T[];
  totalCount: number;
  totalPages: number;
  currentPage: number;
  pageSize: number;
  hasNext: boolean;
  hasPrev: boolean;
}

// 认证相关类型
export interface LoginRequest {
  email: string;
  password: string;
}

export interface LoginResponse {
  accessToken: string;
  user: User;
  message?: string;
}

export interface User {
  id: number;
  username: string;
  email: string;
  avatar?: string; // 添加avatar字段支持base64头像上传
  createdAt?: string;
  updatedAt?: string;
  profile?: UserProfile;
  // 新增字段匹配后端UserResponse
  balance?: number;
  plan?: UserPlan;
  planExpireAt?: string;
  autoSubscribe?: boolean;
  usageText?: number;
  usageImage?: number;
  usageVoice?: number;
  usageCall?: number;
  usageVideo?: number;
  imageGen?: boolean;
  nsfw?: boolean;
  numGenCharacter?: number;
}

export interface UserPlan {
  id: number;
  name: string;
  subscriptionLevel: string; // Free, Lite, Basic, Premium
  privilege: {
    text: number;
    image: number;
    voice_plays: number;
    voice_call: number;
    video: number;
    image_gen: boolean;
    nsfw: boolean;
    max_gen_character: number;
  };
  planPeriod: number;
  price: number;
  currency: string;
}

export interface UserProfile {
  avatar?: string;
  bio?: string;
}

export interface RegisterRequest {
  email: string;
  password: string;
  username: string;
}

// 聊天相关类型
export interface ChatHistoryItem {
  id: number;
  userId: number;
  characterId: number;
  message: string;
  response: string;
  msgType: 'text' | 'image' | 'voice';
  createdAt: string;
}

export interface ChatHistoryResponse {
  success: boolean;
  histories: ChatHistoryItem[];
  totalCount: number;
  error?: string;
}

export interface StreamChatRequest {
  user_id: string;
  session_id: string;
  message: string;
}

// 角色相关类型
export interface Character {
  id: string | number;
  name: string;
  age?: number;
  occupation: string;
  tags: string[];
  mappedTags?: string[];
  description: string;
  chatCount: string;
  likeCount: string;
  followers?: string;
  imageSrc: string;
  avatarSrc: string;
  images?: string[];
  gender: 'male' | 'female';
  creator: {
    id: string;
    name: string;
    likeCount: string;
  };
  rank?: number;
  isPopular?: boolean;
  isNew?: boolean;
}

// HTTP方法类型
export type HttpMethod = 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';

// 请求配置
export interface RequestConfig {
  method?: HttpMethod;
  headers?: Record<string, string>;
  params?: Record<string, any>;
  data?: any;
  timeout?: number;
  signal?: AbortSignal;
}

// 错误类型
export interface ApiError {
  message: string;
  status?: number;
  code?: string;
  details?: any;
}

// 流式回调类型
export type StreamCallback = (content: string) => void;
export type StreamCompleteCallback = () => void;
export type StreamErrorCallback = (error: string) => void;

// 最近聊天相关类型 (简化统一接口)
export interface RecentChatItem {
  // 基础角色信息
  id: string;                           // 聊天/角色ID
  name: string;                         // 角色名称
  occupation?: string;                  // 职业
  age?: number;                         // 年龄
  
  // 图片信息
  imageSrc: string;                    // 主图片
  avatarSrc: string;                   // 头像图片
  
  // 基础属性
  gender: 'male' | 'female';           // 性别
  
  // 聊天状态信息
  lastChatAt: string;                  // 最后聊天时间 (ISO格式)
  lastMessage?: string;                // 最后一条消息内容
  unreadCount: number;                 // 未读消息数量，默认0
  isPrivate?: boolean;                 // 是否为私密聊天
  isPinned?: boolean;                  // 是否置顶
}

export interface RecentChatsResponse {
  success: boolean;
  data?: {
    chats: RecentChatItem[];
    totalCount: number;
    totalUnreadCount: number;           // 总未读消息数
  };
  error?: string;
}

// 获取最近聊天的查询参数 (简化)
export interface GetRecentChatsQuery {
  limit?: number;                       // 返回数量限制，默认10，最大50
}