'use client';

import Image from 'next/image';
import { Play, Pause } from 'lucide-react';
import { useRef, useEffect } from 'react';
import { getChatImageUrl, getAudioUrl } from '@/lib/image-utils';

// 解析文本中的星号包裹内容，将其渲染为灰色，并过滤不需要显示的内容
const parseTextWithAsterisks = (text: string) => {
  // 先过滤掉不需要显示的文本
  let filteredText = text
    .replace(/\[Request interrupted by user\]/g, '')
    .trim(); // 去除首尾空白字符

  const parts = [];
  let currentIndex = 0;
  let match;

  // 匹配 **内容** 和 *内容* 的正则表达式（优先匹配双星号）
  const asteriskRegex = /\*\*([^*]+)\*\*|\*([^*]+)\*/g;

  while ((match = asteriskRegex.exec(filteredText)) !== null) {
    // 添加星号之前的普通文本
    if (match.index > currentIndex) {
      parts.push({
        type: 'text',
        content: filteredText.slice(currentIndex, match.index),
        key: `text-${currentIndex}`,
      });
    }

    // 添加星号包裹的内容（灰色显示）
    // match[1] 是 **内容** 中的内容，match[2] 是 *内容* 中的内容
    const content = match[1] || match[2];
    parts.push({
      type: 'gray',
      content: content, // 去掉星号的内容
      key: `gray-${match.index}`,
    });

    currentIndex = match.index + match[0].length;
  }

  // 添加剩余的普通文本
  if (currentIndex < filteredText.length) {
    parts.push({
      type: 'text',
      content: filteredText.slice(currentIndex),
      key: `text-${currentIndex}`,
    });
  }

  return parts;
};

interface Message {
  id: number;
  sender: 'user' | 'ai';
  text: string;
  timestamp: string;
  audioDuration?: number;
  hasImage?: boolean;
  imageSrc?: string;
  hasAudio?: boolean;
  audioSrc?: string;
  isThinking?: boolean;
}

interface MessageListProps {
  messages: Message[];
  isPlaying: number | null;
  onAudioPlay: (messageId: number, audioElement?: HTMLAudioElement) => void;
  onAudioDurationUpdate?: (messageId: number, duration: number) => void;
}

export default function MessageList({
  messages,
  isPlaying,
  onAudioPlay,
  onAudioDurationUpdate,
}: MessageListProps) {
  const messagesEndRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    // 只有在消息容器存在且可见时才进行滚动
    const messagesContainer = messagesEndRef.current?.parentElement;
    if (messagesEndRef.current && messagesContainer && messages.length > 0) {
      // 使用容器的 scrollTop 而不是 scrollIntoView 避免影响整个页面
      messagesContainer.scrollTop = messagesContainer.scrollHeight;
    }
  }, [messages]);

  return (
    <div className="flex-1 overflow-y-auto p-3 sm:p-6 space-y-3 sm:space-y-6">
      {messages.map((message) => (
        <div
          key={message.id}
          className={`flex ${
            message.sender === 'user' ? 'justify-end' : 'justify-start'
          }`}
        >
          <div
            className={`max-w-[85%] sm:max-w-[80%] rounded-2xl p-3 sm:p-5 ${
              message.sender === 'user'
                ? 'bg-pink-500 text-white'
                : 'bg-[#1a0a24] text-white'
            }`}
          >
            {message.sender === 'ai' &&
              (message.hasAudio || message.audioDuration) && (
                <div className="flex items-center text-xs sm:text-sm text-gray-400 mb-2">
                  <button
                    onClick={() => {
                      // 创建音频元素用于播放
                      const audioElement = new Audio();
                      if (message.audioSrc) {
                        audioElement.src = getAudioUrl(message.audioSrc);
                        console.log('🎵 音频播放URL:', audioElement.src);

                        // 如果还没有时长信息，预加载获取时长
                        if (!message.audioDuration && onAudioDurationUpdate) {
                          audioElement.onloadedmetadata = () => {
                            if (
                              audioElement.duration &&
                              !isNaN(audioElement.duration) &&
                              isFinite(audioElement.duration)
                            ) {
                              const realDuration = Math.ceil(
                                audioElement.duration
                              );
                              console.log(
                                '⏰ 预加载获取音频时长:',
                                realDuration,
                                '秒'
                              );
                              onAudioDurationUpdate(message.id, realDuration);
                            }
                          };
                          // 开始预加载metadata
                          audioElement.preload = 'metadata';
                          audioElement.load();
                        }
                      }
                      onAudioPlay(message.id, audioElement);
                    }}
                    className="bg-pink-500 text-white rounded-full p-1 sm:p-1.5 mr-2 hover:bg-pink-600 transition-colors"
                    title={isPlaying === message.id ? 'pause' : 'play'}
                  >
                    {isPlaying === message.id ? (
                      <Pause className="h-3 w-3 sm:h-4 sm:w-4" />
                    ) : (
                      <Play className="h-3 w-3 sm:h-4 sm:w-4" />
                    )}
                  </button>
                  <span className="text-xs">
                    {message.audioDuration
                      ? `${message.audioDuration}s`
                      : 'loading...'}
                  </span>
                  {message.audioSrc && (
                    <span
                      className="ml-2 text-xs text-gray-500 truncate max-w-16 sm:max-w-20"
                      title={message.audioSrc}
                    >
                      🎧{' '}
                      {message.audioSrc.split('/').pop()?.split('.')[0] ||
                        'audio'}
                    </span>
                  )}
                </div>
              )}

            {/* 思考状态显示 */}
            {message.sender === 'ai' && message.isThinking && (
              <div className="flex items-center space-x-2 text-gray-400">
                <div className="flex space-x-1">
                  <div className="w-1.5 h-1.5 sm:w-2 sm:h-2 bg-pink-400 rounded-full animate-bounce"></div>
                  <div
                    className="w-1.5 h-1.5 sm:w-2 sm:h-2 bg-pink-400 rounded-full animate-bounce"
                    style={{ animationDelay: '0.1s' }}
                  ></div>
                  <div
                    className="w-1.5 h-1.5 sm:w-2 sm:h-2 bg-pink-400 rounded-full animate-bounce"
                    style={{ animationDelay: '0.2s' }}
                  ></div>
                </div>
                <span className="text-xs sm:text-sm italic">Thinking...</span>
              </div>
            )}

            {/* 正常消息内容 */}
            {!message.isThinking && (
              <>
                {message.sender === 'ai' && (
                  <div className="text-sm sm:text-base italic text-gray-300 mb-2">
                    {(() => {
                      const firstPart = message.text.split('"')[0];
                      const parsedParts = parseTextWithAsterisks(firstPart);

                      return parsedParts.map((part) => {
                        if (part.type === 'gray') {
                          return (
                            <span
                              key={part.key}
                              className="text-gray-400 italic"
                            >
                              {part.content}
                            </span>
                          );
                        } else {
                          return <span key={part.key}>{part.content}</span>;
                        }
                      });
                    })()}
                  </div>
                )}
                <div className="text-sm sm:text-base">
                  {(() => {
                    const textToProcess =
                      message.sender === 'ai'
                        ? message.text.split('"').slice(1).join('"')
                        : message.text;

                    const parsedParts = parseTextWithAsterisks(textToProcess);

                    return parsedParts.map((part) => {
                      if (part.type === 'gray') {
                        return (
                          <span key={part.key} className="text-gray-400 italic">
                            {part.content}
                          </span>
                        );
                      } else {
                        return <span key={part.key}>{part.content}</span>;
                      }
                    });
                  })()}
                </div>
                {message.sender === 'ai' && message.hasImage && (
                  <div className="mt-3 rounded-lg overflow-hidden">
                    <Image
                      src={
                        getChatImageUrl(message.imageSrc) ||
                        '/placeholder.svg?height=300&width=200'
                      }
                      alt="AI generated image"
                      width={300}
                      height={200}
                      className="object-cover"
                    />
                  </div>
                )}
              </>
            )}
          </div>
        </div>
      ))}
      <div ref={messagesEndRef} />
    </div>
  );
}
