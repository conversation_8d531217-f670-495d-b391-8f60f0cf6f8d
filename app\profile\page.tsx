"use client"

import Link from "next/link"
import { useRouter } from "next/navigation"
import {
  ArrowLeft,
  Edit3,
  Crown,
  MessageCircle,
  ImageIcon,
  Mic,
  Users,
  Phone,
  X,
  Upload,
  Trash2,
  Eye,
  EyeOff,
  MoreHorizontal,
  LogOut,
} from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON>bsList, TabsTrigger, TabsContent } from "@/components/ui/tabs"
import { Dialog, DialogContent, DialogTitle, DialogDescription } from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { getAvatarUrl, getCharacterImageUrl } from "@/lib/image-utils"
import SmartImage from "@/components/SmartImage"
import MobileBottomNav from "@/components/mobile-bottom-nav"
import { follow<PERSON><PERSON><PERSON> } from "@/lib/api"
import { UserAPI } from "@/lib/api/user"
import React from "react"
import { useUser, useUserPlan } from "@/contexts/UserContext"
import UserPlanHeader from "@/components/UserPlanHeader"
import { PageGuard, PageAccessLevel } from "@/components/auth/PageGuard"

// 套餐名称映射函数
const getPlanDisplayName = (plan?: { id: number; subscriptionLevel: string }): string => {
  if (!plan) return "Free Plan"
  
  // 根据plan ID映射到具体名称
  switch (plan.id) {
    case 1:
      return "Prime"
    case 2:
      return "Plus" 
    case 3:
      return "Pro"
    case 4:
      return "Lifetime"
    default:
      // 回退到subscriptionLevel
      switch (plan.subscriptionLevel) {
        case 'Lite':
          return "Prime"
        case 'Basic':
          return "Plus"
        case 'Premium':
          return "Pro"
        default:
          return "Free Plan"
      }
  }
}

// 获取套餐颜色样式
const getPlanBadgeStyle = (planName: string): string => {
  switch (planName) {
    case 'Prime':
      return "bg-gradient-to-r from-blue-500/20 to-cyan-500/20 border-blue-500/30 text-blue-300"
    case 'Plus':
      return "bg-gradient-to-r from-green-500/20 to-emerald-500/20 border-green-500/30 text-green-300"
    case 'Pro':
      return "bg-gradient-to-r from-purple-500/20 to-pink-500/20 border-purple-500/30 text-purple-300"
    case 'Lifetime':
      return "bg-gradient-to-r from-yellow-500/20 to-orange-500/20 border-yellow-500/30 text-yellow-300"
    default:
      return "bg-gradient-to-r from-gray-500/20 to-gray-600/20 border-gray-500/30 text-gray-300"
  }
}

// 格式化到期时间
const formatExpireTime = (expireAt?: string): string => {
  if (!expireAt) return "No expiration"
  
  try {
    const expireDate = new Date(expireAt)
    const now = new Date()
    
    if (expireDate.getFullYear() === 2099) {
      return "Lifetime"
    }
    
    if (expireDate < now) {
      return "Expired"
    }
    
    const diffTime = expireDate.getTime() - now.getTime()
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
    
    if (diffDays === 1) {
      return "Expires tomorrow"
    } else if (diffDays <= 7) {
      return `Expires in ${diffDays} days`
    } else {
      return expireDate.toLocaleDateString()
    }
  } catch (error) {
    return "Invalid date"
  }
}

// User data interface - 匹配后端API数据结构
interface UserData {
  id: string
  name: string
  email: string
  avatarSrc: string
  followers: string
  following: string
  interactions: string
  description: string
  privateCharacters: Character[]
  publicCharacters: Character[]
  // 真实API数据
  plan?: {
    id: number
    subscriptionLevel: string
    privilege: {
      text: number
      image: number
      voice_plays: number
      voice_call: number
      video: number
      image_gen: boolean
      nsfw: boolean
      max_gen_character: number
    }
  }
  planExpireAt?: string
  usageText?: number
  usageImage?: number
  usageVoice?: number
  usageCall?: number
  usageVideo?: number
  imageGen?: boolean
  nsfw?: boolean
  numGenCharacter?: number
  // 兼容旧数据结构
  isPremium: boolean
  usageStats: {
    textReply: { current: number; limit: number }
    pictureReply: { current: number; limit: number }
    voiceReply: { current: number; limit: number }
    createCharacter: { current: number; limit: number }
    voiceCall: { current: string; limit: string }
  }
}

interface Character {
  id: string
  name: string
  tags: string[]
  description: string
  chatCount: string
  likeCount: string
  imageSrc: string
  creator: { id: string; name: string; likeCount: string }
  isPrivate: boolean
  createdAt: string
  isFollowing?: boolean
  isFollowLoading?: boolean
  onFollowToggle?: () => void
}

export default function ProfilePage() {
  return (
    <PageGuard accessLevel={PageAccessLevel.AUTH_REQUIRED}>
      <ProfilePageContent />
    </PageGuard>
  )
}

function ProfilePageContent() {
  const router = useRouter()
  const { user, isLoggedIn, isLoading: userLoading, logout } = useUser()
  const { planDisplayName, planBadgeStyle, expireTimeFormatted, usageStats } = useUserPlan()
  const [isLoading, setIsLoading] = React.useState(true)
  const [isAuthenticated, setIsAuthenticated] = React.useState(false)
  const [userData, setUserData] = React.useState<UserData | null>(null)
  const [editDescriptionOpen, setEditDescriptionOpen] = React.useState(false)
  const [userDescription, setUserDescription] = React.useState("")
  const [view, setView] = React.useState<"profile" | "creator">("profile")
  const [editDialogOpen, setEditDialogOpen] = React.useState(false)
  const [userName, setUserName] = React.useState("")
  const [userAvatar, setUserAvatar] = React.useState("")
  const [deleteDialogOpen, setDeleteDialogOpen] = React.useState(false)
  const [characterToDelete, setCharacterToDelete] = React.useState<Character | null>(null)
  const [followStates, setFollowStates] = React.useState<Record<string, { isFollowing: boolean; isLoading: boolean }>>({})

  // 检查用户登录状态并加载用户数据
  React.useEffect(() => {
    const checkAuthAndLoadData = async () => {
      try {
        if (typeof window !== "undefined") {
          const user = JSON.parse(localStorage.getItem("user") || '{}')
          const isLoggedIn = localStorage.getItem("isLoggedIn") === "true"
          
          if (!isLoggedIn || !user.email) {
            // 用户未登录，重定向到登录页
            router.push("/login")
            return
          }

          setIsAuthenticated(true)
          
          // 加载用户数据
          const loadedUserData = await loadUserData(user)
          setUserData(loadedUserData)
          setUserName(loadedUserData.name)
          setUserAvatar(loadedUserData.avatarSrc)
          setUserDescription(loadedUserData.description)
        }
      } catch (error) {
        console.error("Error loading user data:", error)
        router.push("/login")
      } finally {
        setIsLoading(false)
      }
    }

    checkAuthAndLoadData()

    // 监听角色创建事件
    const handleCharacterCreated = (event: any) => {
      const newCharacter = event.detail
      const user = JSON.parse(localStorage.getItem("user") || '{}')
      if (user.email && newCharacter.creatorId === user.email) {
        // 重新加载用户数据以包含新角色
        loadUserData(user).then(setUserData)
      }
    }

    // 监听用户数据更新事件
    const handleUserDataUpdated = () => {
      const user = JSON.parse(localStorage.getItem("user") || '{}')
      if (user.email) {
        loadUserData(user).then(setUserData)
      }
    }

    window.addEventListener('characterCreated', handleCharacterCreated)
    window.addEventListener('userDataUpdated', handleUserDataUpdated)

    return () => {
      window.removeEventListener('characterCreated', handleCharacterCreated)
      window.removeEventListener('userDataUpdated', handleUserDataUpdated)
    }
  }, [router]) // 移除userData依赖，避免无限循环

  // 关注处理函数 - 移到条件渲染之前
  const handleFollowToggle = React.useCallback(async (characterId: string) => {
    if (!isAuthenticated) {
      console.error('用户未登录');
      return;
    }

    const token = localStorage.getItem('token');
    if (!token) {
      console.error('未找到认证token');
      return;
    }

    const currentState = followStates[characterId] || { isFollowing: false, isLoading: false };

    if (currentState.isLoading) {
      return; // 防止重复点击
    }

    try {
      // 设置加载状态
      setFollowStates(prev => ({
        ...prev,
        [characterId]: { ...currentState, isLoading: true }
      }));

      // 调用关注/取关API
      const response = await followCharacter(token, parseInt(characterId), !currentState.isFollowing);

      if (response.success) {
        // 更新关注状态
        setFollowStates(prev => ({
          ...prev,
          [characterId]: {
            isFollowing: !currentState.isFollowing,
            isLoading: false
          }
        }));
        
        console.log('关注状态更新成功:', response.message);
      } else {
        console.error('关注操作失败:', response.message);
        setFollowStates(prev => ({
          ...prev,
          [characterId]: { ...currentState, isLoading: false }
        }));
      }
    } catch (error) {
      console.error('关注操作出错:', error);
      setFollowStates(prev => ({
        ...prev,
        [characterId]: { ...currentState, isLoading: false }
      }));
    }
  }, [isAuthenticated, followStates]);

  // 从API加载用户数据
  const loadUserData = async (user: any): Promise<UserData> => {
    try {
      console.log('正在加载用户数据...')
      
      // 调用真实API获取用户数据
      const apiUserData = await UserAPI.getProfile()
      console.log('API返回数据:', apiUserData)
      
      // 获取用户创建的角色（从localStorage）
      const userCharacters = JSON.parse(localStorage.getItem("userCharacters") || "[]")
      const userCreatedCharacters = userCharacters.filter((char: any) => char.creatorId === user.email)
      
      // 构建用户数据，合并API数据和本地数据
      const userData: UserData = {
        id: apiUserData.id.toString(),
        name: apiUserData.username || user.username || user.email.split('@')[0],
        email: apiUserData.email,
        avatarSrc: apiUserData.avatar || user.avatar || "",
        followers: "0", // TODO: 这些数据后端暂无提供
        following: "0", 
        interactions: "0",
        description: "Exploring the world of AI characters.",
        
        // 角色数据（本地）
        privateCharacters: userCreatedCharacters.filter((char: any) => char.isPrivate),
        publicCharacters: userCreatedCharacters.filter((char: any) => !char.isPrivate),
        
        // API真实数据
        plan: apiUserData.plan,
        planExpireAt: apiUserData.planExpireAt,
        usageText: apiUserData.usageText,
        usageImage: apiUserData.usageImage,
        usageVoice: apiUserData.usageVoice,
        usageCall: apiUserData.usageCall,
        usageVideo: apiUserData.usageVideo,
        imageGen: apiUserData.imageGen,
        nsfw: apiUserData.nsfw,
        numGenCharacter: apiUserData.numGenCharacter,
        
        // 兼容旧格式（基于API数据生成）
        isPremium: apiUserData.plan?.subscriptionLevel !== 'Free',
        usageStats: {
          textReply: { 
            current: apiUserData.usageText || 0, 
            limit: apiUserData.plan?.privilege?.text || 30 
          },
          pictureReply: { 
            current: apiUserData.usageImage || 0, 
            limit: apiUserData.plan?.privilege?.image || 1 
          },
          voiceReply: { 
            current: apiUserData.usageVoice || 0, 
            limit: apiUserData.plan?.privilege?.voice_plays || 1 
          },
          createCharacter: { 
            current: userCreatedCharacters.length, 
            limit: apiUserData.plan?.privilege?.max_gen_character || 1 
          },
          voiceCall: { 
            current: `${Math.floor((apiUserData.usageCall || 0) / 60)}min`, 
            limit: `${Math.floor((apiUserData.plan?.privilege?.voice_call || 0) / 60)}min` 
          }
        }
      }
      
      console.log('处理后的用户数据:', userData)
      return userData
      
    } catch (error) {
      console.error('加载用户数据失败，使用默认数据:', error)
      
      // API调用失败时的默认数据
      const userCharacters = JSON.parse(localStorage.getItem("userCharacters") || "[]")
      const userCreatedCharacters = userCharacters.filter((char: any) => char.creatorId === user.email)
      
      return {
        id: user.email,
        name: user.username || user.email.split('@')[0],
        email: user.email,
        avatarSrc: user.avatar || "",
        followers: "0",
        following: "0", 
        interactions: "0",
        description: "New user exploring the world of AI characters.",
        privateCharacters: userCreatedCharacters.filter((char: any) => char.isPrivate),
        publicCharacters: userCreatedCharacters.filter((char: any) => !char.isPrivate),
        isPremium: false,
        usageStats: {
          textReply: { current: 0, limit: 30 },
          pictureReply: { current: 0, limit: 1 },
          voiceReply: { current: 0, limit: 1 },
          createCharacter: { current: userCreatedCharacters.length, limit: 1 },
          voiceCall: { current: "0min", limit: "0min" }
        }
      }
    }
  }

  // 保存用户数据（保留用于兼容性，主要更新本地状态）
  const saveUserData = (updatedData: UserData) => {
    // 不再保存到localStorage，因为我们使用实时API数据
    setUserData(updatedData)
  }

  // 登出功能
  const handleLogout = () => {
    localStorage.removeItem("isLoggedIn")
    localStorage.removeItem("user")
    localStorage.removeItem("token")
    router.push("/login")
  }

  const handleAvatarUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      // 检查文件大小（限制为 1MB）
      if (file.size > 1024 * 1024) {
        console.error('图片大小不能超过 1MB')
        return
      }

      // 检查文件类型
      if (!file.type.startsWith('image/')) {
        console.error('请上传图片文件')
        return
      }

      const reader = new FileReader()
      reader.onload = (e) => {
        if (e.target?.result) {
          // 压缩图片
          const img = document.createElement('img')
          img.onload = () => {
            const canvas = document.createElement('canvas')
            const MAX_WIDTH = 200
            const MAX_HEIGHT = 200
            let width = img.width
            let height = img.height

            if (width > height) {
              if (width > MAX_WIDTH) {
                height *= MAX_WIDTH / width
                width = MAX_WIDTH
              }
            } else {
              if (height > MAX_HEIGHT) {
                width *= MAX_HEIGHT / height
                height = MAX_HEIGHT
              }
            }

            canvas.width = width
            canvas.height = height
            const ctx = canvas.getContext('2d')
            if (ctx) {
              ctx.drawImage(img, 0, 0, width, height)
              // 转换为较低质量的 JPEG
              const compressedDataUrl = canvas.toDataURL('image/jpeg', 0.6)
              setUserAvatar(compressedDataUrl)
            }
          }
          img.src = e.target.result as string
        }
      }
      reader.readAsDataURL(file)
    }
  }

  const handleEditDescription = () => {
    if (userData) {
      const updatedData = { ...userData, description: userDescription }
      saveUserData(updatedData)
    }
    setEditDescriptionOpen(false)
  }

  const handleEditProfile = async () => {
    if (userData) {
      try {
        // 调用API更新用户资料 - 使用正确的base64头像上传方式
        await UserAPI.updateProfile({
          username: userName,
          profile: {
            avatar: userAvatar
          }
        });
        
        // API调用成功后，更新本地数据
        const updatedData = { 
          ...userData, 
          name: userName, 
          avatarSrc: userAvatar 
        }
        saveUserData(updatedData)
        
        // 立即更新页面显示状态，确保头像立即刷新
        setUserData(updatedData)
        
        // 同时更新localStorage中的用户信息
        const user = JSON.parse(localStorage.getItem("user") || '{}')
        user.username = userName
        user.avatar = userAvatar
        localStorage.setItem("user", JSON.stringify(user))
        
        // 触发全局用户数据更新事件，让其他组件知道用户数据已更新
        window.dispatchEvent(new CustomEvent('userDataUpdated'))
        
        console.log('用户资料更新成功')
      } catch (error) {
        console.error('更新用户资料失败:', error)
        // 可以在这里添加错误提示UI
      }
    }
    setEditDialogOpen(false)
  }

  const handleDeleteCharacter = (character: Character) => {
    setCharacterToDelete(character)
    setDeleteDialogOpen(true)
  }

  const confirmDeleteCharacter = () => {
    if (characterToDelete && userData) {
      // 从用户数据中删除角色
      const updatedData = { ...userData }
      
      if (characterToDelete.isPrivate) {
        updatedData.privateCharacters = updatedData.privateCharacters.filter(
          (c) => c.id !== characterToDelete.id
        )
      } else {
        updatedData.publicCharacters = updatedData.publicCharacters.filter(
          (c) => c.id !== characterToDelete.id
        )
      }
      
      // 更新创建角色的使用统计
      updatedData.usageStats.createCharacter.current = 
        updatedData.privateCharacters.length + updatedData.publicCharacters.length
      
      saveUserData(updatedData)
      
      // 同时从全局角色列表中删除
      const allCharacters = JSON.parse(localStorage.getItem("userCharacters") || "[]")
      const filteredCharacters = allCharacters.filter((char: any) => char.id !== characterToDelete.id)
      localStorage.setItem("userCharacters", JSON.stringify(filteredCharacters))
    }
    
    setDeleteDialogOpen(false)
    setCharacterToDelete(null)
  }

  // 如果正在加载，显示加载状态
  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-[#0a0a0f] via-[#1a0a24] to-[#2a1a34] flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-pink-500 mx-auto mb-4"></div>
          <p className="text-white text-lg">Loading your profile...</p>
        </div>
      </div>
    )
  }

  // 如果未认证，不显示任何内容（会被重定向）
  if (!isAuthenticated || !userData) {
    return null
  }

  const usageStatsList = [
    {
      icon: MessageCircle,
      label: "Text reply",
      current: usageStats.textReply.current,
      limit: usageStats.textReply.limit,
      color: "text-green-400",
    },
    {
      icon: ImageIcon,
      label: "Picture reply",
      current: usageStats.pictureReply.current,
      limit: usageStats.pictureReply.limit,
      color: "text-blue-400",
    },
    {
      icon: Mic,
      label: "Voice reply",
      current: usageStats.voiceReply.current,
      limit: usageStats.voiceReply.limit,
      color: "text-purple-400",
    },
    {
      icon: Users,
      label: "Create Character",
      current: usageStats.createCharacter.current,
      limit: usageStats.createCharacter.limit,
      color: "text-orange-400",
    },
    {
      icon: Phone,
      label: "Voice Call",
      current: usageStats.voiceCall.current,
      limit: usageStats.voiceCall.limit,
      color: "text-pink-400",
      isTime: true,
    },
  ]

  const CharacterCard = ({ character, showActions = false }: { character: Character; showActions?: boolean }) => (
    <div className="bg-white/5 rounded-xl overflow-hidden border border-white/10 hover:border-white/20 transition-all duration-200 group">
      <div className="relative aspect-[3/4]">
        <SmartImage src={getCharacterImageUrl(character.imageSrc)} alt={character.name} fill className="object-cover" />
        {character.isPrivate && (
          <div className="absolute top-2 left-2">
            <Badge className="bg-red-500/80 text-white text-xs">
              <EyeOff className="h-3 w-3 mr-1" />
              Private
            </Badge>
          </div>
        )}
        {!character.isPrivate && (
          <div className="absolute top-2 left-2">
            <Badge className="bg-green-500/80 text-white text-xs">
              <Eye className="h-3 w-3 mr-1" />
              Public
            </Badge>
          </div>
        )}
        
        {/* 关注按钮 - 只在非编辑模式显示 */}
        {!showActions && character.onFollowToggle && (
          <div className="absolute top-2 right-2">
            <Button
              size="sm"
              variant={character.isFollowing ? "secondary" : "default"}
              className={`h-7 px-2 text-xs ${
                character.isFollowing
                  ? "bg-gray-600/80 hover:bg-gray-500/80 text-white"
                  : "bg-pink-500/90 hover:bg-pink-600/90 text-white"
              } backdrop-blur-sm`}
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                character.onFollowToggle?.();
              }}
              disabled={character.isFollowLoading}
            >
              {character.isFollowLoading ? "..." : (character.isFollowing ? "✓" : "+")}
            </Button>
          </div>
        )}
        
        {showActions && (
          <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  size="sm"
                  variant="outline"
                  className="bg-black/50 border-white/20 text-white hover:bg-black/70"
                >
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="bg-[#1a0a24] border-[#3a1a44] text-white">
                <DropdownMenuItem
                  className="hover:!bg-red-500/20 text-red-400"
                  onClick={() => handleDeleteCharacter(character)}
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  Delete
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        )}
      </div>
      <div className="p-4">
        <h3 className="font-semibold text-white mb-2 line-clamp-1">{character.name}</h3>
        <div className="flex flex-wrap gap-1 mb-2">
          {character.tags.map((tag: string, index: number) => (
            <Badge key={index} variant="secondary" className="text-xs bg-pink-500/20 text-pink-300 border-pink-500/30">
              {tag}
            </Badge>
          ))}
        </div>
        <p className="text-gray-400 text-sm mb-3 line-clamp-2">{character.description}</p>
        <div className="flex items-center justify-between text-xs text-gray-500">
          <span>{character.chatCount} chats</span>
          <span>{character.likeCount} likes</span>
        </div>
      </div>
    </div>
  )

  // Creator Page View
  if (view === "creator") {
    return (
      <div className="min-h-screen bg-gradient-to-br from-[#0a0a0f] via-[#1a0a24] to-[#2a1a34] pb-16 md:pb-0">
        {/* Header */}
        <div className="sticky top-0 z-10 bg-black/20 backdrop-blur-md border-b border-white/10">
          <div className="flex items-center justify-between p-4">
            <button
              onClick={() => setView("profile")}
              className="flex items-center space-x-2 text-white hover:text-pink-400 transition-colors"
            >
              <ArrowLeft className="h-5 w-5" />
              <span className="text-sm font-medium">Back to Profile</span>
            </button>
            <div className="flex items-center space-x-3">
              <Badge
                variant="outline"
                className="bg-gradient-to-r from-green-500/20 to-blue-500/20 border-green-500/30 text-green-300"
              >
                <Users className="h-3 w-3 mr-1" />
                My Creator Page
              </Badge>
              <Button
                onClick={handleLogout}
                variant="outline"
                size="sm"
                className="border-red-500/30 text-red-400 hover:bg-red-500/20"
              >
                <LogOut className="h-4 w-4 mr-2" />
                Logout
              </Button>
            </div>
          </div>
        </div>

        <div className="max-w-6xl mx-auto p-6">
          {/* Profile Header */}
          <div className="flex flex-col md:flex-row items-end justify-between mb-8">
            <div className="flex items-end">
              <div className="relative h-32 w-32 md:h-40 md:w-40 rounded-full border-4 border-pink-500 p-1 bg-gradient-to-r from-pink-500 to-purple-500 overflow-hidden">
                <div className="h-full w-full rounded-full overflow-hidden bg-gray-800">
                  <SmartImage src={getAvatarUrl(userData?.avatarSrc)} alt={userName} fill className="object-cover" />
                </div>
              </div>
              <div className="ml-4 mb-2">
                <h1 className="text-2xl md:text-3xl font-bold text-white">{userName}</h1>
                <div className="flex items-center space-x-4 text-sm text-gray-400 mt-1">
                  <span>{userData.followers} Followers</span>
                  <span>{userData.following} Following</span>
                  <span>{userData.interactions} Interactions</span>
                </div>
              </div>
            </div>
            <div className="flex items-center space-x-3 mt-4 md:mt-0">
              <Button onClick={() => setEditDescriptionOpen(true)} className="bg-pink-500 hover:bg-pink-600 text-white">
                <Edit3 className="h-4 w-4 mr-2" /> Edit Profile
              </Button>
            </div>
          </div>

          {/* Profile Description */}
          <div className="bg-white/5 rounded-xl p-6 border border-white/10 backdrop-blur-md mb-8">
            <p className="text-gray-300">{userDescription}</p>
          </div>

          {/* Characters Section */}
          <Tabs defaultValue="all" className="w-full">
            <div className="flex justify-between items-center mb-4">
              <TabsList className="bg-white/10 p-1 rounded-full border border-white/20">
                <TabsTrigger
                  value="all"
                  className="px-4 py-1.5 text-sm data-[state=active]:bg-pink-500 data-[state=active]:text-white text-gray-300"
                >
                  All ({userData.privateCharacters.length + userData.publicCharacters.length})
                </TabsTrigger>
                <TabsTrigger
                  value="private"
                  className="px-4 py-1.5 text-sm data-[state=active]:bg-pink-500 data-[state=active]:text-white text-gray-300"
                >
                  Private ({userData.privateCharacters.length})
                </TabsTrigger>
                <TabsTrigger
                  value="public"
                  className="px-4 py-1.5 text-sm data-[state=active]:bg-pink-500 data-[state=active]:text-white text-gray-300"
                >
                  Public ({userData.publicCharacters.length})
                </TabsTrigger>
              </TabsList>
            </div>

            <TabsContent value="all">
              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                {[...userData.privateCharacters, ...userData.publicCharacters].map((char) => (
                  <CharacterCard key={char.id} character={char} showActions={true} />
                ))}
              </div>
            </TabsContent>

            <TabsContent value="private">
              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                {userData.privateCharacters.map((char) => (
                  <CharacterCard key={char.id} character={char} showActions={true} />
                ))}
              </div>
            </TabsContent>

            <TabsContent value="public">
              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                {userData.publicCharacters.map((char) => (
                  <CharacterCard key={char.id} character={char} showActions={true} />
                ))}
              </div>
            </TabsContent>
          </Tabs>

          {/* Edit Description Dialog for Creator Page */}
          <Dialog open={editDescriptionOpen} onOpenChange={setEditDescriptionOpen}>
            <DialogContent
              className="bg-gradient-to-br from-[#1a0a24] to-[#2a1a34] border-[#3a1a44] text-white sm:max-w-md p-0"
            >
              <div className="flex items-center justify-between p-6 pb-4">
                <DialogTitle className="text-xl font-bold text-white">Edit Profile</DialogTitle>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => setEditDescriptionOpen(false)}
                  className="text-gray-400 hover:text-white hover:bg-white/10 rounded-full"
                >
                  <X className="h-5 w-5" />
                </Button>
              </div>

              <div className="px-6 pb-6 space-y-6">
                <div>
                  <Label htmlFor="description" className="text-base font-medium text-white mb-3 block">
                    Profile Description
                  </Label>
                  <textarea
                    id="description"
                    value={userDescription}
                    onChange={(e) => setUserDescription(e.target.value)}
                    className="w-full bg-[#0e0314] border border-[#3a1a44] text-white placeholder-gray-400 focus:border-pink-500 focus:ring-pink-500 rounded-lg p-3 h-24 resize-none"
                    placeholder="Tell others about yourself..."
                  />
                </div>

                <Button
                  onClick={handleEditDescription}
                  className="w-full bg-gradient-to-r from-pink-500 to-purple-500 hover:from-pink-600 hover:to-purple-600 text-white py-3 rounded-full text-base font-medium h-12"
                >
                  Update Description
                </Button>
              </div>
            </DialogContent>
          </Dialog>

          {/* Delete Confirmation Dialog */}
          <Dialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
            <DialogContent className="bg-[#1a0a24] border-[#3a1a44] text-white sm:max-w-md">
              <DialogTitle className="text-xl font-bold text-red-400">Delete Character</DialogTitle>
              <div className="text-gray-300 mb-4">
                Are you sure you want to delete "{characterToDelete?.name}"? This action cannot be undone.
              </div>
              <div className="flex justify-end space-x-2">
                <Button
                  variant="outline"
                  onClick={() => setDeleteDialogOpen(false)}
                  className="border-gray-600 text-gray-300 hover:bg-gray-700"
                >
                  Cancel
                </Button>
                <Button onClick={confirmDeleteCharacter} className="bg-red-600 hover:bg-red-700 text-white">
                  <Trash2 className="h-4 w-4 mr-2" />
                  Delete
                </Button>
              </div>
            </DialogContent>
          </Dialog>
        </div>
        
        {/* 移动端底部导航 */}
        <MobileBottomNav />
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-[#0a0a0f] via-[#1a0a24] to-[#2a1a34] pb-16 md:pb-0">
      {/* Header */}
      <div className="sticky top-0 z-10 bg-black/20 backdrop-blur-md border-b border-white/10">
        <div className="flex items-center justify-between p-3 md:p-4">
          <Link href="/" className="flex items-center space-x-2 text-white hover:text-pink-400 transition-colors">
            <ArrowLeft className="h-5 w-5" />
            <span className="hidden sm:block text-sm font-medium">Back to home</span>
            <span className="sm:hidden text-sm font-medium">Back</span>
          </Link>

          {/* 移动端简化的右侧按钮 */}
          <div className="flex items-center space-x-2 md:space-x-3">
            <Badge
              variant="outline"
              className={`${planBadgeStyle} text-xs md:text-sm`}
            >
              <Crown className="h-3 w-3 mr-1" />
              <span className="hidden sm:inline">{planDisplayName}</span>
              <span className="sm:hidden">{planDisplayName.split(' ')[0]}</span>
            </Badge>

            {/* 移动端下拉菜单 */}
            <div className="md:hidden">
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="outline"
                    size="sm"
                    className="border-white/20 text-white hover:bg-white/10 p-2"
                  >
                    <MoreHorizontal className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="bg-black/90 border-white/20">
                  <DropdownMenuItem
                    onClick={handleLogout}
                    className="text-red-400 hover:bg-red-500/20 focus:bg-red-500/20"
                  >
                    <LogOut className="h-4 w-4 mr-2" />
                    Logout
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>

            {/* 桌面端显示完整按钮 */}
            <Button
              onClick={handleLogout}
              variant="outline"
              size="sm"
              className="hidden md:flex border-red-500/30 text-red-400 hover:bg-red-500/20"
            >
              <LogOut className="h-4 w-4 mr-2" />
              Logout
            </Button>
          </div>
        </div>
      </div>

      <div className="max-w-4xl mx-auto p-4 md:p-6">
        {/* Profile Section */}
        <div className="text-center mb-6 md:mb-8">
          <div className="relative inline-block mb-4">
            <button onClick={() => setView("creator")} className="relative group">
              <div className="h-20 w-20 md:h-24 md:w-24 rounded-full overflow-hidden border-4 border-gradient-to-r from-pink-500 to-purple-500 p-1 bg-gradient-to-r from-pink-500 to-purple-500 hover:scale-105 transition-transform duration-200">
                <div className="h-full w-full rounded-full overflow-hidden bg-gray-800">
                  <SmartImage
                    src={getAvatarUrl(userData?.avatarSrc)}
                    alt="Profile Avatar"
                    width={96}
                    height={96}
                    className="object-cover"
                  />
                </div>
              </div>
              <div className="absolute inset-0 rounded-full bg-black/20 opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex items-center justify-center">
                <span className="text-white text-xs font-medium hidden md:block">View Creator Page</span>
                <span className="text-white text-xs font-medium md:hidden">Creator</span>
              </div>
            </button>
            <div className="absolute -bottom-1 -right-1 h-5 w-5 md:h-6 md:w-6 bg-green-500 rounded-full border-2 border-black"></div>
          </div>

          <h1 className="text-xl md:text-2xl font-bold text-white mb-2">{userName}</h1>
          <p className="text-gray-400 mb-4 text-sm md:text-base">{userData.following} Following</p>

          <Button
            onClick={() => setEditDialogOpen(true)}
            variant="outline"
            size="sm"
            className="bg-white/10 border-white/20 text-white hover:bg-white/20 transition-all duration-200 text-sm md:text-base"
          >
            <Edit3 className="h-4 w-4 mr-2" />
            Edit Profile
          </Button>
        </div>

        {/* Usage Statistics */}
        <Card className="bg-white/5 border-white/10 backdrop-blur-md mb-6 md:mb-8">
          <CardContent className="p-4 md:p-6">
            {/* 移动端优化的标题部分 */}
            <div className="mb-4 md:mb-6">
              <div className="flex items-center justify-between mb-3">
                <h2 className="text-lg md:text-xl font-semibold text-white">Usage Statistics</h2>
                {planDisplayName === "Free Plan" && (
                  <Link href="/premium" className="text-pink-400 hover:text-pink-300 text-xs md:text-sm font-medium transition-colors">
                    Upgrade →
                  </Link>
                )}
              </div>

              {/* 套餐信息 - 移动端水平排列 */}
              <div className="flex items-center justify-between">
                <Badge className={`${planBadgeStyle} text-xs md:text-sm`} variant="outline">
                  {planDisplayName}
                </Badge>
                <span className="text-xs md:text-sm text-gray-400">
                  {expireTimeFormatted}
                </span>
              </div>
            </div>

            {/* 移动端：垂直列表布局，桌面端：网格布局 */}
            <div className="space-y-3 md:space-y-0 md:grid md:grid-cols-2 lg:grid-cols-3 md:gap-4">
              {usageStatsList.map((stat, index) => {
                const Icon = stat.icon
                const isAtLimit = stat.isTime ? false : stat.current >= stat.limit

                return (
                  <div
                    key={index}
                    className="bg-white/5 rounded-lg border border-white/10 hover:border-white/20 transition-all duration-200
                               p-3 md:p-4
                               flex md:block items-center md:items-start justify-between md:justify-start
                               space-x-3 md:space-x-0 md:space-y-2"
                  >
                    {/* 移动端：水平布局 */}
                    <div className="flex items-center space-x-3 md:space-x-2 flex-1 md:flex-none">
                      <div className="flex items-center space-x-2">
                        <Icon className={`h-4 w-4 md:h-5 md:w-5 ${stat.color} flex-shrink-0`} />
                        <span className="text-white text-sm md:text-sm font-medium">{stat.label}</span>
                      </div>

                      {/* 移动端显示数值 */}
                      <div className="md:hidden flex items-center space-x-2">
                        <span className={`text-sm font-bold ${stat.color}`}>{stat.current}</span>
                        <span className="text-gray-400 text-xs">/ {stat.limit}</span>
                      </div>
                    </div>

                    {/* 移动端：右侧警告图标 */}
                    {isAtLimit && (
                      <Badge className="md:hidden bg-gradient-to-r from-red-500/20 to-orange-500/20 border border-red-500/30 text-red-300 text-xs px-2 py-1 flex-shrink-0">
                        ⚠️
                      </Badge>
                    )}

                    {/* 桌面端：垂直布局 */}
                    <div className="hidden md:block space-y-2">
                      <div className="flex items-center justify-between">
                        <span className={`text-lg font-bold ${stat.color}`}>{stat.current}</span>
                        <div className="flex items-center space-x-2">
                          <span className="text-gray-400 text-sm">/ {stat.limit}</span>
                          {isAtLimit && (
                            <Badge className="bg-gradient-to-r from-red-500/20 to-orange-500/20 border border-red-500/30 text-red-300 text-xs px-2 py-1">
                              ⚠️
                            </Badge>
                          )}
                        </div>
                      </div>

                      {!stat.isTime && (
                        <div className="w-full bg-gray-700 rounded-full h-2">
                          <div
                            className={`h-2 rounded-full bg-gradient-to-r ${
                              isAtLimit ? "from-red-500 to-orange-500" : "from-pink-500 to-purple-500"
                            }`}
                            style={{ width: `${Math.min((Number(stat.current) / Number(stat.limit)) * 100, 100)}%` }}
                          ></div>
                        </div>
                      )}
                    </div>
                  </div>
                )
              })}
            </div>
          </CardContent>
        </Card>

        {/* Characters Section */}
        <Card className="bg-white/5 border-white/10 backdrop-blur-md">
          <CardContent className="p-4 md:p-6">
            <div className="flex flex-col sm:flex-row sm:items-center justify-between mb-4 md:mb-6 space-y-3 sm:space-y-0">
              <h2 className="text-lg md:text-xl font-semibold text-white">My Characters</h2>
              <Link href="/create-lover">
                <Button
                  size="sm"
                  className="bg-gradient-to-r from-pink-500 to-purple-500 hover:from-pink-600 hover:to-purple-600 text-white w-full sm:w-auto"
                >
                  <Users className="h-4 w-4 mr-2" />
                  <span className="hidden sm:inline">Create New</span>
                  <span className="sm:hidden">Create</span>
                </Button>
              </Link>
            </div>

            <Tabs defaultValue="all" className="w-full">
              <TabsList className="grid w-full grid-cols-3 bg-white/10 border border-white/20 h-auto">
                <TabsTrigger
                  value="all"
                  className="data-[state=active]:bg-pink-500 data-[state=active]:text-white text-xs md:text-sm py-2 px-1 md:px-3"
                >
                  <span className="hidden sm:inline">All ({userData.privateCharacters.length + userData.publicCharacters.length})</span>
                  <span className="sm:hidden">All</span>
                </TabsTrigger>
                <TabsTrigger
                  value="private"
                  className="data-[state=active]:bg-pink-500 data-[state=active]:text-white text-xs md:text-sm py-2 px-1 md:px-3"
                >
                  <span className="hidden sm:inline">Private ({userData.privateCharacters.length})</span>
                  <span className="sm:hidden">Private</span>
                </TabsTrigger>
                <TabsTrigger
                  value="public"
                  className="data-[state=active]:bg-pink-500 data-[state=active]:text-white text-xs md:text-sm py-2 px-1 md:px-3"
                >
                  <span className="hidden sm:inline">Public ({userData.publicCharacters.length})</span>
                  <span className="sm:hidden">Public</span>
                </TabsTrigger>
              </TabsList>

              <TabsContent value="all" className="mt-4 md:mt-6">
                {userData.privateCharacters.length + userData.publicCharacters.length > 0 ? (
                  <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 md:gap-4">
                    {[...userData.privateCharacters, ...userData.publicCharacters].map((char) => {
                      const followState = followStates[char.id] || { isFollowing: false, isLoading: false };
                      return (
                        <CharacterCard 
                          key={char.id} 
                          character={{
                            ...char,
                            isFollowing: followState.isFollowing,
                            isFollowLoading: followState.isLoading,
                            onFollowToggle: () => handleFollowToggle(char.id)
                          }} 
                        />
                      );
                    })}
                  </div>
                ) : (
                  <div className="text-center py-8 md:py-12">
                    <div className="h-16 w-16 md:h-20 md:w-20 mx-auto mb-3 md:mb-4 rounded-full bg-white/10 flex items-center justify-center">
                      <Users className="h-8 w-8 md:h-10 md:w-10 text-gray-400" />
                    </div>
                    <h3 className="text-base md:text-lg font-medium text-white mb-2">No characters here</h3>
                    <p className="text-gray-400 mb-4 md:mb-6 text-sm md:text-base px-4">Create your first AI character to get started</p>
                    <Link href="/create-lover">
                      <Button
                        size="sm"
                        className="bg-gradient-to-r from-pink-500 to-purple-500 hover:from-pink-600 hover:to-purple-600 text-white"
                      >
                        <Users className="h-4 w-4 mr-2" />
                        Create Character
                      </Button>
                    </Link>
                  </div>
                )}
              </TabsContent>

              <TabsContent value="private" className="mt-4 md:mt-6">
                {userData.privateCharacters.length > 0 ? (
                  <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 md:gap-4">
                    {userData.privateCharacters.map((char) => {
                      const followState = followStates[char.id] || { isFollowing: false, isLoading: false };
                      return (
                        <CharacterCard 
                          key={char.id} 
                          character={{
                            ...char,
                            isFollowing: followState.isFollowing,
                            isFollowLoading: followState.isLoading,
                            onFollowToggle: () => handleFollowToggle(char.id)
                          }} 
                        />
                      );
                    })}
                  </div>
                ) : (
                  <div className="text-center py-8 md:py-12">
                    <div className="h-16 w-16 md:h-20 md:w-20 mx-auto mb-3 md:mb-4 rounded-full bg-white/10 flex items-center justify-center">
                      <EyeOff className="h-8 w-8 md:h-10 md:w-10 text-gray-400" />
                    </div>
                    <h3 className="text-base md:text-lg font-medium text-white mb-2">No private characters</h3>
                    <p className="text-gray-400 text-sm md:text-base px-4">Your private characters will appear here</p>
                  </div>
                )}
              </TabsContent>

              <TabsContent value="public" className="mt-4 md:mt-6">
                {userData.publicCharacters.length > 0 ? (
                  <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 md:gap-4">
                    {userData.publicCharacters.map((char) => {
                      const followState = followStates[char.id] || { isFollowing: false, isLoading: false };
                      return (
                        <CharacterCard 
                          key={char.id} 
                          character={{
                            ...char,
                            isFollowing: followState.isFollowing,
                            isFollowLoading: followState.isLoading,
                            onFollowToggle: () => handleFollowToggle(char.id)
                          }} 
                        />
                      );
                    })}
                  </div>
                ) : (
                  <div className="text-center py-8 md:py-12">
                    <div className="h-16 w-16 md:h-20 md:w-20 mx-auto mb-3 md:mb-4 rounded-full bg-white/10 flex items-center justify-center">
                      <Eye className="h-8 w-8 md:h-10 md:w-10 text-gray-400" />
                    </div>
                    <h3 className="text-base md:text-lg font-medium text-white mb-2">No public characters</h3>
                    <p className="text-gray-400 text-sm md:text-base px-4">Your public characters will appear here</p>
                  </div>
                )}
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      </div>

      {/* Edit Profile Dialog */}
      <Dialog open={editDialogOpen} onOpenChange={setEditDialogOpen}>
        <DialogContent
          className="bg-gradient-to-br from-[#1a0a24] to-[#2a1a34] border-[#3a1a44] text-white sm:max-w-md p-0"
        >
          {/* Header */}
          <div className="flex items-center justify-between p-6 pb-4">
            <DialogTitle className="text-xl font-bold text-white">My Profile</DialogTitle>
            <Button
              variant="ghost"
              size="icon"
              onClick={() => setEditDialogOpen(false)}
              className="text-gray-400 hover:text-white hover:bg-white/10 rounded-full"
            >
              <X className="h-5 w-5" />
            </Button>
          </div>

          <div className="px-6 pb-6 space-y-6">
            {/* Profile Picture Section */}
            <div className="text-center">
              <h3 className="text-lg font-medium text-white mb-6">Update profile picture</h3>
              <div className="relative inline-block mb-4">
                <div className="h-32 w-32 rounded-full overflow-hidden border-4 border-pink-500 p-1 bg-gradient-to-r from-pink-500 to-purple-500">
                  <div className="h-full w-full rounded-full overflow-hidden bg-gray-800">
                    <SmartImage
                      src={getAvatarUrl(userAvatar)}
                      alt="Profile Avatar"
                      width={128}
                      height={128}
                      className="object-cover"
                    />
                  </div>
                </div>
              </div>
              <div className="mb-6">
                <input
                  type="file"
                  accept="image/*"
                  onChange={handleAvatarUpload}
                  className="hidden"
                  id="avatar-upload"
                />
                <Button
                  asChild
                  size="sm"
                  className="bg-gradient-to-r from-pink-500 to-purple-500 hover:from-pink-600 hover:to-purple-600 text-white rounded-full px-4 py-2 text-sm"
                >
                  <label htmlFor="avatar-upload" className="cursor-pointer">
                    <Upload className="h-4 w-4 mr-2" />
                    Upload new photo
                  </label>
                </Button>
              </div>
            </div>

            {/* Username Section */}
            <div>
              <Label htmlFor="username" className="text-base font-medium text-white mb-3 block">
                Username
              </Label>
              <Input
                id="username"
                value={userName}
                onChange={(e) => setUserName(e.target.value)}
                className="bg-[#0e0314] border border-[#3a1a44] text-white placeholder-gray-400 focus:border-pink-500 focus:ring-pink-500"
                placeholder="Enter your username"
              />
            </div>

            <Button
              onClick={handleEditProfile}
              className="w-full bg-gradient-to-r from-pink-500 to-purple-500 hover:from-pink-600 hover:to-purple-600 text-white py-3 rounded-full text-base font-medium h-12"
            >
              Update Profile
            </Button>
          </div>
        </DialogContent>
      </Dialog>
      
      {/* 移动端底部导航 */}
      <MobileBottomNav />
    </div>
  )
} 