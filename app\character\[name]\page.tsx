'use client';

import { useEffect, useState } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { useAuth } from '@/hooks/useAuth';
import { useUserSubscription } from '@/hooks/useSubscription';
import { getCharacterByName } from '@/lib/api/characters';

export default function CharacterPage() {
  const router = useRouter();
  const params = useParams();
  const characterName = typeof params.name === 'string' ? decodeURIComponent(params.name) : '';
  
  const { isLoggedIn, isLoading: authLoading } = useAuth();
  const { subscriptionLevel } = useUserSubscription();
  const [character, setCharacter] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!characterName) return;

    const loadCharacter = async () => {
      try {
        setLoading(true);
        // 通过角色名称查找角色
        const characterData = await getCharacterByName(characterName);
        
        if (characterData) {
          setCharacter(characterData);
          // 如果用户已登录，直接跳转到聊天页面
          if (isLoggedIn) {
            router.push(`/chat/${characterData.id}`);
          }
        } else {
          setError('Character not found');
        }
      } catch (err) {
        console.error('Error loading character:', err);
        setError('Failed to load character');
      } finally {
        setLoading(false);
      }
    };

    loadCharacter();
  }, [characterName, isLoggedIn, router]);

  if (loading) {
    return (
      <div className="min-h-screen bg-[#0e0314] flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-pink-500 mx-auto mb-4"></div>
          <p className="text-gray-400">Loading character...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-[#0e0314] flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-white mb-4">Character Not Found</h1>
          <p className="text-gray-400 mb-6">{error}</p>
          <button
            onClick={() => router.push('/')}
            className="bg-pink-500 hover:bg-pink-600 text-white px-6 py-2 rounded-lg"
          >
            Go Home
          </button>
        </div>
      </div>
    );
  }

  if (!character) {
    return null;
  }

  // 如果用户未登录，显示角色预览和登录提示
  return (
    <div className="min-h-screen bg-[#0e0314]">
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          {/* 角色信息卡片 */}
          <div className="bg-[#1a0a24] rounded-xl p-6 mb-8 border border-[#3a1a44]">
            <div className="flex flex-col md:flex-row items-center md:items-start gap-6">
              {/* 角色头像 */}
              <div className="w-32 h-32 rounded-full overflow-hidden flex-shrink-0">
                <img
                  src={character.avatarUrl || character.imageUrl}
                  alt={character.name}
                  className="w-full h-full object-cover"
                />
              </div>
              
              {/* 角色信息 */}
              <div className="flex-1 text-center md:text-left">
                <h1 className="text-3xl font-bold text-white mb-2">{character.name}</h1>
                <p className="text-gray-400 mb-4">{character.occupation}, {character.age}</p>
                <p className="text-gray-300 mb-6">{character.description}</p>
                
                {/* 标签 */}
                <div className="flex flex-wrap gap-2 justify-center md:justify-start mb-6">
                  {character.tags?.map((tag: string, index: number) => (
                    <span
                      key={index}
                      className="px-3 py-1 bg-[#2a1a34] text-gray-300 rounded-full text-sm"
                    >
                      {tag}
                    </span>
                  ))}
                </div>
                
                {/* 统计信息 */}
                <div className="flex gap-6 justify-center md:justify-start text-sm text-gray-400">
                  <span>💬 {character.chatCount} chats</span>
                  <span>❤️ {character.likeCount} likes</span>
                </div>
              </div>
            </div>
          </div>

          {/* 登录提示 */}
          <div className="bg-gradient-to-r from-purple-500/20 to-pink-500/20 rounded-xl p-8 text-center border border-purple-500/30">
            <h2 className="text-2xl font-bold text-white mb-4">
              Ready to chat with {character.name}?
            </h2>
            <p className="text-gray-300 mb-6">
              Sign in to start unlimited conversations and save your chat history
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button
                onClick={() => router.push('/login')}
                className="bg-pink-500 hover:bg-pink-600 text-white px-8 py-3 rounded-lg font-medium"
              >
                Sign In
              </button>
              <button
                onClick={() => router.push('/register')}
                className="bg-purple-500 hover:bg-purple-600 text-white px-8 py-3 rounded-lg font-medium"
              >
                Sign Up Free
              </button>
            </div>
            
            <div className="mt-6 pt-6 border-t border-purple-500/20">
              <p className="text-sm text-gray-400">
                Join thousands of users enjoying unlimited AI conversations
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
