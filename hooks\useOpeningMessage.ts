import { useCallback } from 'react';
import type { Message } from './useMessages';

export interface Character {
  id: string | number;
  name: string;
  resource?: {
    opening?: string;
  };
}

export const useOpeningMessage = () => {
  // 生成开场白消息
  const createOpeningMessage = useCallback((character: Character): Message => {
    // 优先使用API数据中的opening字段
    let openingText = '';
    
    if (character?.resource?.opening) {
      openingText = character.resource.opening;
      console.log('🎭 使用API提供的开场白:', openingText.substring(0, 100) + '...');
    } else {
      // 回退到默认开场白
      openingText = `Hello! I'm ${character.name}. Nice to meet you! What would you like to chat about?`;
      console.log('💭 使用默认开场白:', openingText);
    }
    
    return {
      id: Date.now(),
      sender: 'ai',
      text: openingText,
      timestamp: new Date().toISOString(),
    };
  }, []);

  // 创建新对话时的完整逻辑
  const createNewConversationWithOpening = useCallback((
    characterId: number,
    character: Character,
    replaceMessages: (messages: Message[]) => void
  ) => {
    try {
      console.log(
        `📝 为角色 ${character.name} (ID: ${characterId}) 创建新对话`
      );

      // 1. 创建开场白消息
      const welcomeMessage = createOpeningMessage(character);

      // 2. 设置欢迎消息
      replaceMessages([welcomeMessage]);

      // 3. 更新最近聊天记录到localStorage
      const newChatItem = {
        id: characterId,
        name: character.name,
        lastMessage: welcomeMessage.text.length > 50 
          ? welcomeMessage.text.substring(0, 50) + '...' 
          : welcomeMessage.text,
        imageSrc: (character as any).avatarSrc || (character as any).imageSrc,
        timestamp: new Date().toLocaleDateString(),
        unread: false,
        unreadCount: 0,
        isPrivate: (character as any).isPrivate || false,
      };

      // 4. 保存到localStorage的最近聊天
      const chatHistory = JSON.parse(
        localStorage.getItem('recentChats') || '[]'
      );
      const existingIndex = chatHistory.findIndex(
        (chat: any) => chat.id === characterId
      );

      if (existingIndex !== -1) {
        chatHistory.splice(existingIndex, 1);
      }
      chatHistory.unshift(newChatItem);
      const updatedHistory = chatHistory.slice(0, 10);
      localStorage.setItem('recentChats', JSON.stringify(updatedHistory));

      console.log('✅ 新对话创建成功', {
        角色: character.name,
        消息ID: welcomeMessage.id,
        时间戳: welcomeMessage.timestamp,
        开场白长度: welcomeMessage.text.length,
      });
      
      return welcomeMessage;
    } catch (error) {
      console.error('❌ 创建新对话失败:', error);
      throw error;
    }
  }, [createOpeningMessage]);

  return {
    createOpeningMessage,
    createNewConversationWithOpening
  };
};