'use client';

import { useEffect, useRef, useCallback } from "react";
import Image from "next/image";
import { <PERSON>, Mi<PERSON>, <PERSON>c<PERSON><PERSON>, <PERSON>Off, Maximize, X } from "lucide-react";
import { Dialog, DialogContent, DialogTitle } from "@/components/ui/dialog";
import { getAvatarUrl, getAudioUrl } from "@/lib/image-utils";
import { useCallUsage } from "@/hooks/useCallUsage";

interface CallDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onMinimize: () => void;
  character: {
    id: string | number;
    name: string;
    avatarSrc?: string;
    resource?: {
      voice_call?: string;
    };
  };
  callState: {
    status: "connecting" | "active" | "ended";
    time: number;
    text: string;
    displayedText: string;
  };
  isMuted: boolean;
  onMuteToggle: () => void;
  onCallStateUpdate: (updates: Partial<{
    status: "connecting" | "active" | "ended";
    time: number;
    text: string;
    displayedText: string;
  }>) => void;
  onSubscriptionModalOpen: () => void;
}

// Call phrases - now only contains the single specified phrase
const callPhrases = ["You came here by mistake, didn't you? Or maybe your body wanted this more than you think."];

export default function CallDialog({
  isOpen,
  onClose,
  onMinimize,
  character,
  callState,
  isMuted,
  onMuteToggle,
  onCallStateUpdate,
  onSubscriptionModalOpen,
}: CallDialogProps) {
  const callTimerRef = useRef<NodeJS.Timeout | null>(null);
  const textTimerRef = useRef<NodeJS.Timeout | null>(null);
  const audioRef = useRef<HTMLAudioElement | null>(null);

  // 使用通话计费 hook
  const { startCallUsage, stopCallUsage, cleanupCallUsage } = useCallUsage({
    onInsufficientBalance: () => {
      console.log('💰 通话余额不足，结束通话并唤起支付弹窗');
      handleEndCall();
      onSubscriptionModalOpen();
    },
  });

  const formatCallTime = useCallback((seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs < 10 ? "0" : ""}${secs}`;
  }, []);

  const handleEndCall = useCallback(() => {
    console.log('📞 结束通话');
    onCallStateUpdate({ status: "ended" });

    // 停止通话计费
    stopCallUsage();

    // Stop and cleanup audio
    if (audioRef.current) {
      audioRef.current.pause();
      audioRef.current.currentTime = 0;
      audioRef.current.src = ''; // 清空音频源
      audioRef.current.load(); // 重新加载以彻底清理
      audioRef.current = null;
    }

    // Clear all timers
    if (callTimerRef.current) {
      clearInterval(callTimerRef.current);
      callTimerRef.current = null;
    }

    if (textTimerRef.current) {
      clearInterval(textTimerRef.current);
      textTimerRef.current = null;
    }

    // Close the call dialog after a short delay
    setTimeout(() => {
      onClose();
      onCallStateUpdate({ time: 0 });
    }, 500);
  }, [onCallStateUpdate, onClose, stopCallUsage]);

  const startCall = useCallback(() => {
    console.log('🎯 开始语音通话，角色资源:', character.resource);
    
    // Since we only have one phrase now, we can directly use it
    const callPhrase = callPhrases[0];
    
    // 🚀 立即开始预加载音频（不影响付费逻辑）
    if (character.resource?.voice_call) {
      const audioUrl = getAudioUrl(character.resource.voice_call);
      console.log('🎵 开始预加载音频:', audioUrl);
      
      // 创建音频元素但不播放，只预加载
      const preloadAudio = new Audio();
      preloadAudio.src = audioUrl;
      preloadAudio.preload = 'auto'; // 强制预加载
      
      // 监听加载完成事件
      preloadAudio.oncanplay = () => {
        console.log('✅ 音频预加载完成，可以立即播放');
        // 保存预加载的音频引用
        audioRef.current = preloadAudio;
        audioRef.current.loop = true;
      };
      
      // 监听加载错误
      preloadAudio.onerror = (error) => {
        console.error('❌ 音频预加载失败:', error);
        // 回退到文字模式
      };
    }
    
    // 设置连接状态
    onCallStateUpdate({ 
      text: callPhrase, 
      displayedText: "", 
      status: "connecting" 
    });

    // 3秒连接时间（减少等待时间）
    setTimeout(() => {
      console.log('📞 通话连接成功，开始播放音频');
      onCallStateUpdate({ status: "active", text: "", displayedText: "" });

      // 🚀 开始通话实时扣费（需要获取token）
      const token = typeof window !== 'undefined' ? localStorage.getItem('token') : null;
      if (token) {
        startCallUsage(token);
      } else {
        console.warn('⚠️ 未找到token，无法启动通话计费');
      }

      // 开始计时器
      let currentTime = 0;
      callTimerRef.current = setInterval(() => {
        currentTime += 1;
        onCallStateUpdate({ time: currentTime });
        
        // 30秒免费通话时间到了，自动跳转订阅窗口
        if (currentTime >= 30) {
          console.log('⏰ 30秒免费通话时间到，跳转订阅窗口');
          handleEndCall();
          onSubscriptionModalOpen();
        }
      }, 1000);

      // 播放真实音频（如果已预加载完成）
      if (audioRef.current && audioRef.current.readyState >= 2) {
        console.log('🎵 音频已预加载完成，立即播放');
        audioRef.current.play().catch((error) => {
          console.error('❌ 播放失败:', error);
          startTextFallback();
        });
      } else if (character.resource?.voice_call) {
        // 如果预加载还没完成，创建新的音频元素
        const audioUrl = getAudioUrl(character.resource.voice_call);
        console.log('🎵 音频预加载未完成，创建新音频元素:', audioUrl);
        
        try {
          audioRef.current = new Audio(audioUrl);
          audioRef.current.loop = true; // 循环播放
          
          audioRef.current.onloadstart = () => {
            console.log('📁 音频开始加载');
          };
          
          audioRef.current.oncanplay = () => {
            console.log('✅ 音频可以播放');
          };
          
          audioRef.current.onplay = () => {
            console.log('▶️ 音频开始播放');
          };
          
          audioRef.current.onerror = (error) => {
            console.error('❌ 音频播放出错:', error);
            // 回退到文字显示模式
            startTextFallback();
          };
          
          // 开始播放
          audioRef.current.play().catch((error) => {
            console.error('❌ 播放失败:', error);
            // 回退到文字显示模式
            startTextFallback();
          });
          
        } catch (error) {
          console.error('❌ 创建音频元素失败:', error);
          // 回退到文字显示模式
          startTextFallback();
        }
      } else {
        console.warn('⚠️ 没有找到voice_call资源，使用文字显示模式');
        // 回退到文字显示模式
        startTextFallback();
      }
    }, 3000); // 从10秒减少到3秒

    // 文字显示回退模式
    function startTextFallback() {
      onCallStateUpdate({ text: callPhrase, displayedText: "" });
      
      let index = 0;
      textTimerRef.current = setInterval(() => {
        if (index <= callPhrase.length) {
          onCallStateUpdate({ displayedText: callPhrase.substring(0, index) });
          index++;
        } else {
          if (textTimerRef.current) clearInterval(textTimerRef.current);
          // 文字显示完成后，不再循环，保持显示状态
        }
      }, 50);
    }
    
  }, [character.resource, onCallStateUpdate, handleEndCall, onSubscriptionModalOpen, startCallUsage]);

  // Start call when dialog opens
  useEffect(() => {
    if (isOpen && callState.status !== "active" && callState.status !== "connecting") {
      startCall();
    }
  }, [isOpen, startCall, callState.status]);

  // Handle mute/unmute for voice call audio
  useEffect(() => {
    if (audioRef.current) {
      audioRef.current.muted = isMuted;
      console.log(isMuted ? '🔇 音频已静音' : '🔊 音频取消静音');
    }
  }, [isMuted]);

  // CSS animations for floating orbs
  const floatingOrbAnimation = `
@keyframes floatingOrb {
  0% { 
    transform: translateY(0px) scale(1);
    opacity: 0.8;
  }
  25% { 
    transform: translateY(-20px) scale(1.1);
    opacity: 1;
  }
  50% { 
    transform: translateY(-30px) scale(1.2);
    opacity: 0.9;
  }
  75% { 
    transform: translateY(-20px) scale(1.1);
    opacity: 1;
  }
  100% { 
    transform: translateY(0px) scale(1);
    opacity: 0.8;
  }
}
@keyframes orbRotate {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
@keyframes orbPulse {
  0%, 100% { 
    box-shadow: 0 0 20px rgba(236, 72, 153, 0.3), 0 0 40px rgba(236, 72, 153, 0.2), 0 0 60px rgba(236, 72, 153, 0.1);
  }
  50% { 
    box-shadow: 0 0 30px rgba(236, 72, 153, 0.5), 0 0 60px rgba(236, 72, 153, 0.3), 0 0 90px rgba(236, 72, 153, 0.2);
  }
}
@keyframes innerGlow {
  0%, 100% { opacity: 0.6; transform: scale(1); }
  50% { opacity: 1; transform: scale(1.1); }
}
.floating-orb {
  animation: floatingOrb 4s ease-in-out infinite;
}
.orb-rotate {
  animation: orbRotate 8s linear infinite;
}
.orb-pulse {
  animation: orbPulse 3s ease-in-out infinite;
}
.inner-glow {
  animation: innerGlow 2s ease-in-out infinite;
}
.call-dialog [data-radix-popper-content-wrapper] {
  width: 100vw !important;
  height: 100vh !important;
  max-width: 100vw !important;
  max-height: 100vh !important;
  transform: none !important;
}`;

  // Apply CSS animations
  useEffect(() => {
    const styleElement = document.createElement("style");
    styleElement.textContent = floatingOrbAnimation;
    document.head.appendChild(styleElement);
    return () => {
      document.head.removeChild(styleElement);
    };
  }, [floatingOrbAnimation]);

  // Cleanup timers, audio and call usage when component unmounts
  useEffect(() => {
    return () => {
      if (callTimerRef.current) clearInterval(callTimerRef.current);
      if (textTimerRef.current) clearInterval(textTimerRef.current);
      if (audioRef.current) {
        audioRef.current.pause();
        audioRef.current = null;
      }
      // 清理通话计费
      cleanupCallUsage();
    };
  }, [cleanupCallUsage]);

  return (
    <>
              <Dialog
          open={isOpen}
          onOpenChange={(open) => {
            // 只有在通话结束时才允许关闭对话框
            if (!open && callState.status === "ended") {
              handleEndCall();
            }
          }}
        >
        <DialogContent className="bg-[#0e0314]/95 backdrop-blur-md border border-[#3a1a44] p-0 w-screen h-screen max-w-none max-h-none rounded-none sm:rounded-xl sm:max-w-5xl sm:w-[90vw] sm:h-[80vh] overflow-hidden">
          <DialogTitle className="sr-only">Voice Call with {character.name}</DialogTitle>
                     {/* 免费通话倒计时标注 */}
           <div className={`absolute top-3 right-16 sm:top-6 sm:right-24 z-10 backdrop-blur-md text-white px-3 py-1.5 sm:px-4 sm:py-2 rounded-full text-xs sm:text-sm font-medium border shadow-lg transition-all duration-300 ${
             callState.time >= 25 
               ? 'bg-red-500/80 border-red-400/50' 
               : callState.time >= 20 
                 ? 'bg-orange-500/80 border-orange-400/50'
                 : 'bg-black/30 border-white/20'
           }`}>
             <div className="flex items-center space-x-1.5 sm:space-x-2">
               <div className={`w-1.5 h-1.5 sm:w-2 sm:h-2 rounded-full animate-pulse ${
                 callState.time >= 25 
                   ? 'bg-red-200' 
                   : callState.time >= 20 
                     ? 'bg-orange-200'
                     : 'bg-white/60'
               }`}></div>
               <span className="text-white/90">Free Call: {formatCallTime(30 - callState.time)}</span>
             </div>
           </div>

          {/* 最小化按钮 */}
          <button
            className="absolute top-3 left-3 sm:top-6 sm:left-6 z-10 bg-black/40 hover:bg-black/60 text-white p-1.5 sm:p-2 rounded-full backdrop-blur-sm border border-white/10 hover:border-white/20 transition-colors"
            onClick={onMinimize}
            title="Minimize Call"
          >
            <Maximize className="h-4 w-4 sm:h-5 sm:w-5" />
          </button>

          {/* 退出按钮 */}
          <button
            className="absolute top-3 right-3 sm:top-6 sm:right-6 z-10 bg-red-500/80 hover:bg-red-600/80 text-white p-1.5 sm:p-2 rounded-full backdrop-blur-sm border border-red-400/50 hover:border-red-300/50 transition-colors"
            onClick={handleEndCall}
            title="End Call"
          >
            <X className="h-4 w-4 sm:h-5 sm:w-5" />
          </button>

          <div className="flex-1 relative overflow-hidden">
            {/* 悬浮球动画背景 */}
            <div className="absolute inset-0 bg-gradient-to-br from-pink-900/10 via-purple-900/8 to-indigo-900/10">
              {/* 中心悬浮球 */}
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="relative floating-orb">
                  {/* 外层发光环 */}
                  <div className="absolute inset-0 w-20 h-20 sm:w-32 sm:h-32 rounded-full orb-pulse bg-gradient-to-br from-pink-500/20 to-purple-500/20 blur-sm"></div>

                  {/* 旋转外环 */}
                  <div className="absolute inset-2 w-16 h-16 sm:w-28 sm:h-28 rounded-full border-2 border-pink-400/30 orb-rotate">
                    <div className="absolute top-0 left-1/2 w-0.5 h-0.5 sm:w-1 sm:h-1 bg-pink-400 rounded-full transform -translate-x-1/2"></div>
                    <div className="absolute bottom-0 left-1/2 w-0.5 h-0.5 sm:w-1 sm:h-1 bg-purple-400 rounded-full transform -translate-x-1/2"></div>
                    <div className="absolute left-0 top-1/2 w-0.5 h-0.5 sm:w-1 sm:h-1 bg-indigo-400 rounded-full transform -translate-y-1/2"></div>
                    <div className="absolute right-0 top-1/2 w-0.5 h-0.5 sm:w-1 sm:h-1 bg-rose-400 rounded-full transform -translate-y-1/2"></div>
                  </div>

                  {/* 内层核心 */}
                  <div className="absolute inset-4 sm:inset-6 w-12 h-12 sm:w-20 sm:h-20 rounded-full bg-gradient-to-br from-pink-400/40 to-purple-400/40 inner-glow backdrop-blur-sm border border-white/10">
                    <div className="absolute inset-1 sm:inset-2 rounded-full bg-gradient-to-br from-pink-300/30 to-purple-300/30"></div>
                  </div>
                </div>
              </div>

              {/* 周围的小悬浮球 */}
              {[...Array(6)].map((_, i) => (
                <div
                  key={i}
                  className="absolute floating-orb"
                  style={{
                    left: `${20 + i * 12}%`,
                    top: `${30 + Math.sin(i) * 20}%`,
                    animationDelay: `${i * 0.8}s`,
                    animationDuration: `${3 + i * 0.5}s`,
                  }}
                >
                  <div className="w-2 h-2 sm:w-4 sm:h-4 rounded-full bg-gradient-to-br from-pink-400/20 to-purple-400/20 orb-pulse"></div>
                </div>
              ))}
            </div>

            {/* 连接状态 */}
            {callState.status === "connecting" && (
              <div className="absolute inset-0 flex items-center justify-center bg-black/60 backdrop-blur-sm">
                <div className="text-center px-4">
                  <div className="relative w-16 h-16 sm:w-24 sm:h-24 mx-auto mb-4 sm:mb-6">
                    <div className="absolute inset-0 rounded-full border-4 border-pink-500 border-t-transparent animate-spin"></div>
                    <Heart className="absolute inset-0 m-auto h-6 w-6 sm:h-8 sm:w-8 text-pink-500 fill-pink-500 animate-pulse" />
                  </div>
                  <p className="text-lg sm:text-2xl text-white font-medium">Connecting to {character.name}...</p>
                  <p className="text-sm sm:text-lg text-pink-300 mt-2 animate-pulse">💕 Getting ready for you...</p>
                </div>
              </div>
            )}

            {/* Subtitle bubble - show only when there's text to display */}
            {callState.status === "active" && callState.displayedText && (
              <div className="absolute bottom-24 sm:bottom-40 left-1/2 transform -translate-x-1/2 max-w-xs sm:max-w-2xl w-full mx-auto px-4">
                <div className="bg-black/40 backdrop-blur-md p-4 sm:p-6 rounded-2xl border border-pink-500/30 shadow-lg shadow-pink-500/20">
                  <p className="text-white text-sm sm:text-xl text-center leading-relaxed">
                    {callState.displayedText}
                    {callState.displayedText.length < callState.text.length && (
                      <span className="animate-pulse ml-0.5">|</span>
                    )}
                  </p>
                </div>
              </div>
            )}

            {/* Voice call indicator when playing audio */}
            {callState.status === "active" && !callState.displayedText && character.resource?.voice_call && (
              <div className="absolute bottom-24 sm:bottom-40 left-1/2 transform -translate-x-1/2 max-w-xs sm:max-w-2xl w-full mx-auto px-4">
                <div className="bg-black/40 backdrop-blur-md p-4 sm:p-6 rounded-2xl border border-pink-500/30 shadow-lg shadow-pink-500/20">
                  <div className="flex items-center justify-center space-x-2 sm:space-x-4">
                    <div className="flex space-x-0.5 sm:space-x-1">
                      <div className="w-2 h-2 sm:w-3 sm:h-3 bg-pink-400 rounded-full animate-bounce"></div>
                      <div className="w-2 h-2 sm:w-3 sm:h-3 bg-pink-400 rounded-full animate-bounce" style={{animationDelay: '0.1s'}}></div>
                      <div className="w-2 h-2 sm:w-3 sm:h-3 bg-pink-400 rounded-full animate-bounce" style={{animationDelay: '0.2s'}}></div>
                    </div>
                    <p className="text-white text-sm sm:text-xl text-center">
                      {character.name} is speaking...
                    </p>
                  </div>
                </div>
              </div>
            )}

            {/* 底部控制按钮 */}
            <div className="absolute bottom-8 sm:bottom-16 left-0 right-0 flex justify-center items-center space-x-6 sm:space-x-12 px-4">
              <button
                className="bg-red-500 hover:bg-red-600 h-14 w-14 sm:h-20 sm:w-20 rounded-full flex items-center justify-center shadow-lg transition-transform hover:scale-105 border-2 border-red-400"
                onClick={handleEndCall}
              >
                <PhoneOff className="h-7 w-7 sm:h-10 sm:w-10 text-white" />
              </button>
              <div className="relative h-16 w-16 sm:h-24 sm:w-24 flex items-center justify-center">
                <div className="absolute inset-0 bg-gradient-to-br from-pink-400/20 to-rose-400/20 rounded-full animate-pulse"></div>
                <span className="text-2xl sm:text-4xl">💕</span>
              </div>
              <button
                className={`${isMuted ? "bg-gray-600" : "bg-[#2a1a34] hover:bg-[#3a1a44]"} h-14 w-14 sm:h-20 sm:w-20 rounded-full flex items-center justify-center shadow-lg transition-transform hover:scale-105 border-2 ${isMuted ? "border-gray-500" : "border-[#4a2a54]"}`}
                onClick={onMuteToggle}
              >
                {isMuted ? <MicOff className="h-7 w-7 sm:h-10 sm:w-10 text-white" /> : <Mic className="h-7 w-7 sm:h-10 sm:w-10 text-white" />}
              </button>
            </div>

            {/* 底部时间显示 */}
            <div className="absolute bottom-2 sm:bottom-6 left-0 right-0 flex justify-center">
              <div className="bg-black/40 backdrop-blur-sm px-4 py-1.5 sm:px-6 sm:py-2 rounded-full border border-white/10">
                <span className="text-white text-sm sm:text-lg font-medium">{formatCallTime(callState.time)}</span>
              </div>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
}