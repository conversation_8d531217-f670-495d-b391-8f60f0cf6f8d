'use client';

import { useState } from 'react';
import { Tab } from '@headlessui/react';
import Link from 'next/link';
import { useCharactersData } from '@/hooks/useCharactersData';
import { ForYouTab } from './ForYouTab';
import { MaleTab } from './MaleTab';
import { BottomNav } from './BottomNav';

export function MobileHome() {
  const [selectedIndex, setSelectedIndex] = useState(0);
  
  // 复用原有数据加载逻辑
  const {
    characters: femaleCharacters,
    loading: femaleLoading
  } = useCharactersData({
    gender: 'female',
    activeTag: 'All',
    pageSize: 20
  });
  
  const {
    characters: maleCharacters,
    loading: maleLoading
  } = useCharactersData({
    gender: 'male',
    activeTag: 'All',
    pageSize: 20
  });

  return (
    <div className="flex flex-col min-h-screen bg-[#120518]">
      <Tab.Group selectedIndex={selectedIndex} onChange={setSelectedIndex}>
        {/* 顶部区域 */}
        <div className="fixed top-0 left-0 right-0 z-50 bg-[#120518] border-b border-[#3a1a44]">
          <div className="flex items-center justify-between px-4 py-3">
            {/* Tabs */}
            <Tab.List className="flex space-x-4">
              <Tab className={({ selected }) => `
                px-3 py-2 text-sm font-medium rounded-lg transition-colors
                ${selected 
                  ? 'text-white bg-pink-500/20 border-b-2 border-pink-500' 
                  : 'text-gray-400 hover:text-white'
                }
              `}>
                For You
              </Tab>
              <Tab className={({ selected }) => `
                px-3 py-2 text-sm font-medium rounded-lg transition-colors
                ${selected 
                  ? 'text-white bg-pink-500/20 border-b-2 border-pink-500' 
                  : 'text-gray-400 hover:text-white'
                }
              `}>
                Male
              </Tab>
            </Tab.List>

            {/* 升级按钮 */}
            <Link 
              href="/payment"
              className="px-4 py-1.5 text-sm font-medium text-white bg-gradient-to-r from-pink-500 to-purple-600 rounded-full hover:from-pink-600 hover:to-purple-700 transition-all duration-200"
            >
              Upgrade Plan
            </Link>
          </div>
        </div>

        {/* 内容区域 */}
        <div className="flex-1 mt-16 mb-16">
          <Tab.Panels>
            <Tab.Panel>
              <ForYouTab 
                characters={femaleCharacters} 
                loading={femaleLoading} 
              />
            </Tab.Panel>
            <Tab.Panel>
              <MaleTab 
                characters={maleCharacters} 
                loading={maleLoading} 
              />
            </Tab.Panel>
          </Tab.Panels>
        </div>
      </Tab.Group>

      {/* 底部导航 */}
      <BottomNav className="fixed bottom-0 left-0 right-0 z-50" />
    </div>
  );
}