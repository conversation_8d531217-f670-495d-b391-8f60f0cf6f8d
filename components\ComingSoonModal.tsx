'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Dialog, DialogContent } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';

interface ComingSoonModalProps {
  title?: string;
  description?: string;
  isOpen?: boolean;
  onClose?: () => void;
  autoShow?: boolean;
}

export function ComingSoonModal({ 
  title = "Coming Soon",
  description = "This feature is currently under development. We're working hard to bring you amazing new content!",
  isOpen: controlledIsOpen,
  onClose,
  autoShow = true
}: ComingSoonModalProps) {
  const router = useRouter();
  const [internalIsOpen, setInternalIsOpen] = useState(false);

  // Use controlled state if provided, otherwise use internal state
  const isOpen = controlledIsOpen !== undefined ? controlledIsOpen : internalIsOpen;

  useEffect(() => {
    if (autoShow && controlledIsOpen === undefined) {
      // Auto-show modal on component mount if not controlled externally
      setInternalIsOpen(true);
    }
  }, [autoShow, controlledIsOpen]);

  const handleClose = () => {
    if (onClose) {
      onClose();
    } else {
      setInternalIsOpen(false);
    }
    // 点击关闭也返回首页
    router.push('/');
  };

  const handleConfirm = () => {
    handleClose();
  };

  const handleBackdropClick = () => {
    // Prevent closing by backdrop click
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleBackdropClick}>
      <DialogContent 
        className="sm:max-w-[425px] bg-[#1a0825] border border-purple-500/30 text-white [&>button]:hidden"
        onPointerDownOutside={(e) => e.preventDefault()}
        onEscapeKeyDown={(e) => e.preventDefault()}
      >
        <div className="space-y-6 p-6 text-center">
          <div className="space-y-4">
            <div className="text-6xl mb-4">🚧</div>
            <h2 className="text-2xl font-bold text-purple-300">
              {title}
            </h2>
            <div className="space-y-3 text-sm text-gray-300">
              <p>{description}</p>
              <div className="space-y-2 text-xs">
                <p>✨ Stay tuned for exciting updates</p>
                <p>🎯 Follow us for the latest news</p>
                <p>💫 Thank you for your patience</p>
              </div>
            </div>
          </div>
          
          <div className="flex flex-col sm:flex-row gap-3">
            <Button
              onClick={handleConfirm}
              className="flex-1 bg-purple-600 hover:bg-purple-700 text-white font-semibold py-3"
            >
              Back to Home
            </Button>
            <Button
              onClick={handleClose}
              variant="outline"
              className="flex-1 border-gray-600 text-gray-300 hover:bg-gray-800 py-3"
            >
              Close
            </Button>
          </div>
          
          <div className="text-xs text-gray-500">
            We appreciate your interest in this feature! 🙏
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}

export default ComingSoonModal;