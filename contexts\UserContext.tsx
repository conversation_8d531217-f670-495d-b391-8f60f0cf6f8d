'use client';

import React, { createContext, useContext, useCallback, useEffect, useState, ReactNode } from 'react';
import { UserAPI } from '@/lib/api/user';
import { checkAndClearFakeData } from '@/lib/clear-fake-chat-data';

// 套餐信息类型
export interface UserPlan {
  id: number;
  name: string;
  subscriptionLevel: string; // 'Free', 'Lite', 'Basic', 'Premium'
  privilege: {
    text: number;
    image: number;
    voice_plays: number;
    voice_call: number;
    video: number;
    image_gen: boolean;
    nsfw: boolean;
    max_gen_character: number;
  };
  planPeriod: number;
  price: number;
  currency: string;
}

// 用户信息类型
export interface UserInfo {
  id: number;
  username: string;
  email: string;
  avatar?: string;
  balance?: number;
  plan?: UserPlan;
  planExpireAt?: string;
  autoSubscribe?: boolean;
  usageText?: number;
  usageImage?: number;
  usageVoice?: number;
  usageCall?: number;
  usageVideo?: number;
  imageGen?: boolean;
  nsfw?: boolean;
  numGenCharacter?: number;
}

// Context 状态类型
interface UserContextState {
  // 用户状态
  user: UserInfo | null;
  isLoggedIn: boolean;
  isLoading: boolean;
  error: string | null;
  
  // 套餐相关方法
  refreshUserData: () => Promise<void>;
  logout: () => void;
  
  // 套餐显示辅助方法
  getPlanDisplayName: () => string;
  getPlanBadgeStyle: () => string;
  formatExpireTime: () => string;
  isPremiumUser: () => boolean;
}

const UserContext = createContext<UserContextState | undefined>(undefined);

// 套餐名称映射函数
const mapPlanDisplayName = (plan?: UserPlan): string => {
  if (!plan) return "Free Plan";
  
  // 根据plan ID映射到具体名称
  switch (plan.id) {
    case 1:
      return "Prime";
    case 2:
      return "Plus"; 
    case 3:
      return "Pro";
    case 4:
      return "Lifetime";
    default:
      // 回退到subscriptionLevel
      switch (plan.subscriptionLevel) {
        case 'Lite':
          return "Prime";
        case 'Basic':
          return "Plus";
        case 'Premium':
          return "Pro";
        default:
          return "Free Plan";
      }
  }
};

// 获取套餐颜色样式
const getPlanBadgeStyleByName = (planName: string): string => {
  switch (planName) {
    case 'Prime':
      return "bg-gradient-to-r from-blue-500/20 to-cyan-500/20 border-blue-500/30 text-blue-300";
    case 'Plus':
      return "bg-gradient-to-r from-green-500/20 to-emerald-500/20 border-green-500/30 text-green-300";
    case 'Pro':
      return "bg-gradient-to-r from-purple-500/20 to-pink-500/20 border-purple-500/30 text-purple-300";
    case 'Lifetime':
      return "bg-gradient-to-r from-yellow-500/20 to-orange-500/20 border-yellow-500/30 text-yellow-300";
    default:
      return "bg-gradient-to-r from-gray-500/20 to-gray-600/20 border-gray-500/30 text-gray-300";
  }
};

// 格式化到期时间
const formatExpireTimeString = (expireAt?: string): string => {
  if (!expireAt) return "No expiration";
  
  try {
    const expireDate = new Date(expireAt);
    const now = new Date();
    
    if (expireDate.getFullYear() === 2099) {
      return "Lifetime";
    }
    
    if (expireDate < now) {
      return "Expired";
    }
    
    const diffTime = expireDate.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    if (diffDays === 1) {
      return "Expires tomorrow";
    } else if (diffDays <= 7) {
      return `Expires in ${diffDays} days`;
    } else {
      return expireDate.toLocaleDateString();
    }
  } catch (error) {
    return "Invalid date";
  }
};

interface UserProviderProps {
  children: ReactNode;
}

export function UserProvider({ children }: UserProviderProps) {
  const [user, setUser] = useState<UserInfo | null>(null);
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [mounted, setMounted] = useState(false);

  // 刷新用户数据
  const refreshUserData = useCallback(async () => {
    try {
      if (typeof window === 'undefined') return;
      
      const token = localStorage.getItem('token');
      const storedIsLoggedIn = localStorage.getItem('isLoggedIn') === 'true';
      
      if (!token || !storedIsLoggedIn) {
        setUser(null);
        setIsLoggedIn(false);
        setIsLoading(false);
        return;
      }

      setIsLoading(true);
      setError(null);
      
      // 调用真实API获取用户数据
      const userData = await UserAPI.getProfile();
      
      setUser(userData);
      setIsLoggedIn(true);
      
    } catch (err: any) {
      console.error('Failed to fetch user data:', err);
      setError(err.message || 'Failed to load user data');
      
      // 如果是认证错误，清除登录状态
      if (err.status === 401) {
        logout();
      }
    } finally {
      setIsLoading(false);
    }
  }, []);

  // 登出
  const logout = useCallback(() => {
    if (typeof window !== 'undefined') {
      localStorage.removeItem('token');
      localStorage.removeItem('user');
      localStorage.removeItem('isLoggedIn');
    }
    setUser(null);
    setIsLoggedIn(false);
    setError(null);
  }, []);

  // 获取套餐显示名称
  const getPlanDisplayName = useCallback((): string => {
    return mapPlanDisplayName(user?.plan);
  }, [user?.plan]);

  // 获取套餐徽章样式
  const getPlanBadgeStyle = useCallback((): string => {
    const planName = mapPlanDisplayName(user?.plan);
    return getPlanBadgeStyleByName(planName);
  }, [user?.plan]);

  // 格式化到期时间
  const formatExpireTime = useCallback((): string => {
    return formatExpireTimeString(user?.planExpireAt);
  }, [user?.planExpireAt]);

  // 是否是付费用户
  const isPremiumUser = useCallback((): boolean => {
    if (!user?.plan) return false;
    return user.plan.subscriptionLevel !== 'Free' && user.plan.id > 0;
  }, [user?.plan]);

  // 处理客户端挂载
  useEffect(() => {
    setMounted(true);
  }, []);

  // 初始化用户状态 - 只在客户端挂载后执行
  useEffect(() => {
    if (mounted) {
      // 清理任何虚假的聊天数据
      checkAndClearFakeData();
      refreshUserData();
    }
  }, [mounted, refreshUserData]);

  // 监听支付成功事件，自动刷新用户数据 - 只在客户端运行
  useEffect(() => {
    if (!mounted) return;

    const handlePaymentSuccess = () => {
      console.log('Payment success detected, refreshing user data...');
      refreshUserData();
    };

    const handleUserDataUpdate = () => {
      console.log('User data update detected, refreshing...');
      refreshUserData();
    };

    // 监听localStorage变化
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'isLoggedIn' || e.key === 'token' || e.key === 'user') {
        console.log('localStorage changed, refreshing user data...');
        setTimeout(() => refreshUserData(), 100); // 延迟一下确保所有数据都已更新
      }
    };

    window.addEventListener('paymentSuccess', handlePaymentSuccess);
    window.addEventListener('userDataUpdated', handleUserDataUpdate);
    window.addEventListener('loginSuccess', handleUserDataUpdate);
    window.addEventListener('storage', handleStorageChange);

    return () => {
      window.removeEventListener('paymentSuccess', handlePaymentSuccess);
      window.removeEventListener('userDataUpdated', handleUserDataUpdate);
      window.removeEventListener('loginSuccess', handleUserDataUpdate);
      window.removeEventListener('storage', handleStorageChange);
    };
  }, [mounted, refreshUserData]);

  const value: UserContextState = {
    user,
    isLoggedIn,
    isLoading,
    error,
    refreshUserData,
    logout,
    getPlanDisplayName,
    getPlanBadgeStyle,
    formatExpireTime,
    isPremiumUser,
  };

  return (
    <UserContext.Provider value={value}>
      {children}
    </UserContext.Provider>
  );
}

// 自定义 Hook 来使用 UserContext
export function useUser(): UserContextState {
  const context = useContext(UserContext);
  if (context === undefined) {
    throw new Error('useUser must be used within a UserProvider');
  }
  return context;
}

// 便捷的套餐相关 Hook
export function useUserPlan() {
  const { user, getPlanDisplayName, getPlanBadgeStyle, formatExpireTime, isPremiumUser } = useUser();
  
  return {
    plan: user?.plan,
    planDisplayName: getPlanDisplayName(),
    planBadgeStyle: getPlanBadgeStyle(),
    expireTimeFormatted: formatExpireTime(),
    isPremium: isPremiumUser(),
    // 使用统计数据
    usageStats: {
      textReply: { 
        current: user?.usageText || 0, 
        limit: user?.plan?.privilege?.text || 30 
      },
      pictureReply: { 
        current: user?.usageImage || 0, 
        limit: user?.plan?.privilege?.image || 1 
      },
      voiceReply: { 
        current: user?.usageVoice || 0, 
        limit: user?.plan?.privilege?.voice_plays || 1 
      },
      createCharacter: { 
        current: 0, // 需要从其他地方获取
        limit: user?.plan?.privilege?.max_gen_character || 1 
      },
      voiceCall: { 
        current: `${Math.floor((user?.usageCall || 0) / 60)}min`, 
        limit: `${Math.floor((user?.plan?.privilege?.voice_call || 0) / 60)}min` 
      }
    }
  };
}