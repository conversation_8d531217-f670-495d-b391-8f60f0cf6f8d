'use client';

import CharacterCard from '@/components/character-card';
import { Badge } from '@/components/ui/badge';
import { useState, useMemo } from 'react';
import { useFilterTags } from '@/hooks/useLabels';
import { getFilterTags } from '@/lib/characters';

interface ForYouTabProps {
  characters: any[];
  loading: boolean;
}

export function ForYouTab({ characters, loading }: ForYouTabProps) {
  const [activeTag, setActiveTag] = useState('All');

  // 获取女性标签数据
  const {
    filterTags,
    loading: tagsLoading,
    error: tagsError,
  } = useFilterTags('female');

  // 根据标签过滤角色
  const filteredCharacters = useMemo(() => {
    if (!characters || characters.length === 0) return [];
    if (activeTag === 'All') return characters;
    
    // 根据选中的标签过滤角色
    return characters.filter(character => 
      character.tags && character.tags.some((tag: string) => tag === activeTag)
    );
  }, [characters, activeTag]);
  if (loading) {
    return (
      <div className="flex items-center justify-center h-[calc(100vh-8rem)]">
        <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-pink-500" />
      </div>
    );
  }

  return (
    <div className="flex flex-col">
      {/* 标签过滤区域 */}
      <div className="px-3 py-2 border-b border-[#3a1a44]">
        <div className="flex overflow-x-auto pb-2">
          <div className="flex space-x-2 min-w-max">
            {tagsLoading ? (
              // 标签加载中状态
              <div className="flex space-x-2">
                {[1, 2, 3, 4, 5].map((i) => (
                  <div
                    key={i}
                    className="h-8 w-16 bg-[#1a0a24] rounded-full animate-pulse"
                  />
                ))}
              </div>
            ) : tagsError ? (
              // 标签加载失败，使用fallback
              getFilterTags('female').map(
                (tag: string, index: number) => (
                  <Badge
                    key={index}
                    variant={tag === activeTag ? 'default' : 'outline'}
                    className={`whitespace-nowrap px-3 py-1.5 text-sm rounded-full cursor-pointer ${
                      tag === activeTag
                        ? 'bg-pink-500 hover:bg-pink-600'
                        : 'bg-[#1a0a24] hover:bg-[#2a1a34]'
                    }`}
                    onClick={() => setActiveTag(tag)}
                  >
                    {tag === 'All' && <span className="mr-1">⭐</span>}
                    {tag !== 'All' && <span className="mr-1">🏷️</span>}
                    {tag}
                  </Badge>
                )
              )
            ) : (
              // 使用API数据
              filterTags.map((tag: string, index: number) => (
                <Badge
                  key={index}
                  variant={tag === activeTag ? 'default' : 'outline'}
                  className={`whitespace-nowrap px-3 py-1.5 text-sm rounded-full cursor-pointer ${
                    tag === activeTag
                      ? 'bg-pink-500 hover:bg-pink-600'
                      : 'bg-[#1a0a24] hover:bg-[#2a1a34]'
                  }`}
                  onClick={() => setActiveTag(tag)}
                >
                  {tag === 'All' && <span className="mr-1">⭐</span>}
                  {tag !== 'All' && <span className="mr-1">🏷️</span>}
                  {tag}
                </Badge>
              ))
            )}
          </div>
        </div>
      </div>

      {/* 角色网格区域 */}
      <div className="flex-1">
        {!characters || characters.length === 0 ? (
          <div className="flex items-center justify-center h-[calc(100vh-12rem)]">
            <div className="text-center text-gray-400">
              <div className="text-lg mb-2">📭 No characters available</div>
              <div className="text-sm">Please check back later</div>
            </div>
          </div>
        ) : filteredCharacters.length === 0 ? (
          <div className="flex items-center justify-center h-[calc(100vh-12rem)]">
            <div className="text-center text-gray-400">
              <div className="text-lg mb-2">🔍 No characters found</div>
              <div className="text-sm">No characters match "{activeTag}". Try a different tag.</div>
            </div>
          </div>
        ) : (
          <div className="grid grid-cols-2 gap-3 p-3">
            {filteredCharacters.map((character) => (
              <CharacterCard
                key={character.id}
                character={character}
              />
            ))}
          </div>
        )}
      </div>
    </div>
  );
}