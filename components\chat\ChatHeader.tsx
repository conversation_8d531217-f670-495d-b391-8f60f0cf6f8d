'use client';

import { <PERSON>u, MessageSquare, Phone, PanelRightOpen, PanelRightClose, Settings, ArrowLeft } from 'lucide-react';
import { getAvatarUrl } from '@/lib/image-utils';
import SmartImage from '@/components/SmartImage';
import { useRouter } from 'next/navigation';
import UserPlanHeader from '@/components/UserPlanHeader';

interface ChatHeaderProps {
  character: {
    name: string;
    avatarSrc: string;
  };
  characterLoading: boolean;
  showProfile: boolean;
  onProfileClick: () => void;
  onCallClick: () => void;
  onClearHistoryClick: () => void;
  onMobileSidebarClick: () => void;
  onMobileChatListClick: () => void;
  onProfileToggle: () => void;
}

export default function ChatHeader({
  character,
  characterLoading,
  showProfile,
  onProfileClick,
  onCallClick,
  onClearHistoryClick,
  onMobileSidebarClick,
  onMobileChatListClick,
  onProfileToggle,
}: ChatHeaderProps) {
  const router = useRouter();

  return (
    <div className="flex items-center justify-between p-3 sm:p-4 border-b border-[#3a1a44]">
      <div className="flex items-center">
        {/* Mobile back button */}
        <div className="flex sm:hidden mr-2">
          <button
            className="min-h-[44px] min-w-[44px] p-2 rounded-full hover:bg-[#2a1a34] flex items-center justify-center"
            onClick={() => router.push('/chat')}
            title="Back to chat list"
          >
            <ArrowLeft className="h-5 w-5 text-gray-400" />
          </button>
        </div>

        <button
          onClick={onProfileClick}
          className="h-10 w-10 sm:h-12 sm:w-12 rounded-full overflow-hidden mr-2 sm:mr-3"
        >
          {characterLoading ? (
            <div className="w-10 h-10 sm:w-12 sm:h-12 bg-gray-600 rounded-full animate-pulse"></div>
          ) : (
            <SmartImage
              src={
                getAvatarUrl(character.avatarSrc) ||
                `/avatar/alexander_avatar.png`
              }
              alt={character.name}
              width={48}
              height={48}
              className="object-cover"
            />
          )}
        </button>
        <h2 className="text-base sm:text-lg md:text-xl font-semibold">
          {characterLoading ? (
            <div className="w-16 sm:w-20 h-5 sm:h-6 bg-gray-600 rounded animate-pulse"></div>
          ) : (
            character.name
          )}
        </h2>
      </div>
      <div className="flex items-center space-x-1 sm:space-x-3">
        {/* 用户套餐信息 */}
        <div className="hidden sm:block">
          <UserPlanHeader className="scale-90" showUpgradeLink={false} />
        </div>
        
        {/* API接口切换按钮 - 已隐藏，V2为默认 */}
        
        {/* 添加清除聊天记录按钮 */}
        <button
          className="min-h-[44px] min-w-[44px] p-3 rounded-full hover:bg-[#2a1a34] text-gray-400 hover:text-white flex items-center justify-center"
          onClick={onClearHistoryClick}
          title="Clear chat history"
        >
          <svg
            className="h-5 w-5"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
            />
          </svg>
        </button>
        <button
          className="min-h-[44px] min-w-[44px] p-3 rounded-full hover:bg-[#2a1a34] flex items-center justify-center"
          onClick={onCallClick}
        >
          <Phone className="h-6 w-6 text-gray-400" />
        </button>
        {/* Profile toggle button - only show on lg+ screens */}
        <button
          className="hidden lg:flex min-h-[44px] min-w-[44px] p-3 rounded-full hover:bg-[#2a1a34] text-gray-400 hover:text-white items-center justify-center"
          onClick={onProfileToggle}
          title={showProfile ? 'Hide profile' : 'Show profile'}
        >
          {showProfile ? (
            <PanelRightClose className="h-5 w-5" />
          ) : (
            <PanelRightOpen className="h-5 w-5" />
          )}
        </button>
      </div>
    </div>
  );
}
