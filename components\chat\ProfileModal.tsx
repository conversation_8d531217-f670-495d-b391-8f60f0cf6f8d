'use client';

import Link from 'next/link';
import { Heart } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';
import { getCharacterImageUrl, getAvatarUrl } from '@/lib/image-utils';
import SmartImage from '@/components/SmartImage';
import { getFakeStatsForCharacter } from '@/lib/fake-stats';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from '@/components/ui/dialog';

interface Character {
  id: string | number;
  name: string;
  age: number;
  occupation: string;
  followers: number;
  tags: string[];
  description: string;
  images?: string[];
  avatarSrc: string;
  imageSrc?: string;
  resource?: {
    opening?: string;
    profile_image?: string[];
    image?: Array<{
      description: string;
      url: string;
    }>;
    image_urls?: string[];
  };
}

interface ProfileModalProps {
  isOpen: boolean;
  onClose: () => void;
  character: Character;
  isFavorite: boolean;
  onSubscriptionButtonClick: () => void;
}

export default function ProfileModal({
  isOpen,
  onClose,
  character,
  isFavorite,
  onSubscriptionButtonClick,
}: ProfileModalProps) {
  // 🎯 统一图片数据处理逻辑 - 与CharacterProfile保持一致
  const getCharacterImages = () => {
    const allImages: string[] = [];

    // 1. 优先使用接口返回的resource.profile_image（专门用于个人信息卡片的照片）
    if (character.resource?.profile_image?.length) {
      allImages.push(...character.resource.profile_image);
      console.log(
        '🖼️ ProfileModal使用profile_image字段:',
        character.resource.profile_image
      );
    } else if (character.resource) {
      // 2. 如果没有profile_image，使用其他resource数据
      console.log('⚠️ ProfileModal没有profile_image，使用其他resource数据');

      // image 资源数组的URL（自拍图片）
      if (character.resource.image?.length) {
        allImages.push(...character.resource.image.map((img) => img.url));
      }

      // image_urls 额外图片URL
      if (character.resource.image_urls?.length) {
        allImages.push(...character.resource.image_urls);
      }
    }

    // 3. 如果接口resource数据为空，fallback到原有字段
    if (allImages.length === 0) {
      // console.log('⚠️ ProfileModal resource数据为空，使用fallback字段');

      // 优先使用主图片
      if (character.imageSrc) {
        allImages.push(character.imageSrc);
      }

      // 然后使用images数组
      if (character.images?.length) {
        allImages.push(...character.images);
      }

      // 最后使用头像作为备选
      if (character.avatarSrc && allImages.length === 0) {
        allImages.push(character.avatarSrc);
      }
    }

    // 4. 去重处理并过滤无效URL
    const uniqueImages = Array.from(new Set(allImages.filter(Boolean)));
    // console.log('📸 ProfileModal最终gallery图片列表:', uniqueImages);
    return uniqueImages;
  };

  const galleryImages = getCharacterImages();

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="bg-[#1a0a24] border-[#3a1a44] text-white
                                 max-w-[95vw] max-h-[95vh]
                                 sm:max-w-2xl md:max-w-3xl
                                 mx-2 sm:mx-4
                                 p-0 overflow-hidden
                                 flex flex-col">
        <DialogHeader className="px-3 sm:px-6 pt-3 sm:pt-6 pb-2 sm:pb-4 flex-shrink-0">
          <DialogTitle className="text-center text-lg sm:text-xl md:text-2xl font-bold">
            {character.name}
          </DialogTitle>
          <DialogDescription className="text-center text-sm text-gray-400">
            {character.occupation}, {character.age} years old
          </DialogDescription>
        </DialogHeader>

        {/* 可滚动内容区域 */}
        <div className="flex-1 overflow-y-auto">
          {/* Image Gallery - 移动端优化 */}
          <div className="px-3 sm:px-6 mb-4">
            <div className="flex space-x-2 sm:space-x-3 overflow-x-auto pb-3 snap-x
                           [&::-webkit-scrollbar]:hidden [-ms-overflow-style:none] [scrollbar-width:none]">
              {galleryImages.map((image: string, index: number) => (
                <div key={index} className="snap-center shrink-0">
                  <SmartImage
                    src={
                      getCharacterImageUrl(image) ||
                      getAvatarUrl(image) ||
                      '/placeholder.svg'
                    }
                    alt={`${character.name} ${index + 1}`}
                    width={160}
                    height={200}
                    className="rounded-lg object-cover
                               h-40 w-32
                               sm:h-48 sm:w-36
                               md:h-56 md:w-44"
                  />
                </div>
              ))}
            </div>
          </div>

          {/* Character Info - 移动端优化 */}
          <div className="px-3 sm:px-6 mb-4">
            {/* 基本信息 */}
            <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between mb-3 gap-2">
              <div className="flex-1">
                <h3 className="text-base sm:text-lg md:text-xl font-semibold mb-1">
                  {character.name}, {character.age}
                </h3>
                <p className="text-gray-400 text-sm sm:text-base">{character.occupation}</p>
              </div>
              <div className="flex items-center self-start sm:self-auto">
                <span className="text-gray-400 mr-2 text-xs sm:text-sm">
                  {getFakeStatsForCharacter(character).likeCount} likes
                </span>
                <Heart
                  className={cn(
                    'h-3 w-3 sm:h-4 sm:w-4',
                    isFavorite ? 'text-pink-500 fill-pink-500' : 'text-gray-400'
                  )}
                />
              </div>
            </div>

            {/* 标签 */}
            <div className="flex flex-wrap gap-1.5 sm:gap-2 mb-4">
              {character.tags.map((tag: string, index: number) => (
                <Badge
                  key={index}
                  variant="outline"
                  className="bg-[#2a1a34] text-xs px-2 py-0.5 sm:py-1 border-[#3a1a44]"
                >
                  {tag}
                </Badge>
              ))}
            </div>

            {/* Background */}
            <div>
              <h4 className="text-sm sm:text-base font-medium mb-2 text-white">Background</h4>
              <p className="text-xs sm:text-sm text-gray-400 leading-relaxed">
                {character.resource?.opening ||
                  `${character.name} has been ${character.occupation.toLowerCase()} since he was young. He's won multiple awards and is known for his dedication and skill. Despite his fame, he keeps his personal life very private.`}
              </p>
            </div>
          </div>
        </div>

        {/* CTA Buttons - 固定在底部 */}
        <div className="flex-shrink-0 border-t border-[#3a1a44] bg-[#1a0a24] p-3 sm:p-6">
          <div className="grid grid-cols-1 sm:grid-cols-3 gap-2 sm:gap-3">
            <Link href="/payment">
              <Button className="w-full bg-pink-500 hover:bg-pink-600 text-white
                               text-xs sm:text-sm py-2.5 sm:py-3
                               rounded-lg font-medium transition-colors">
                ❤️ Want to know more
              </Button>
            </Link>
            <Link href="/payment">
              <Button className="w-full bg-purple-500 hover:bg-purple-600 text-white
                               text-xs sm:text-sm py-2.5 sm:py-3
                               rounded-lg font-medium transition-colors">
                💎 See more of me
              </Button>
            </Link>
            <Button
              className="w-full bg-red-500 hover:bg-red-600 text-white
                       text-xs sm:text-sm py-2.5 sm:py-3
                       rounded-lg font-medium transition-colors"
              onClick={onSubscriptionButtonClick}
            >
              🔥 Continue our night
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
