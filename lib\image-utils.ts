import { buildImageUrl } from './config'

// 图片类型枚举
export enum ImageType {
  AVATAR = 'avatar',
  CHARACTER = 'character',
  CHAT = 'chat',
  AUDIO = 'audio',
  GALLERY = 'gallery',
  PACKAGE = 'package'
}

// 检查是否为完整URL
export const isFullUrl = (url: string): boolean => {
  return url.startsWith('http://') || url.startsWith('https://')
}

// ===========================================
// 图片URL处理说明
// ===========================================
// 
// 本文件提供统一的图片URL处理逻辑，支持以下特性：
// 
// 1. 智能前缀拼接控制
//    - 通过环境变量 NEXT_PUBLIC_ENABLE_IMAGE_PREFIX 控制
//    - 启用时：相对路径自动添加CDN前缀
//    - 禁用时：保持原始路径不变
//    - 完整URL始终保持不变
// 
// 2. 多种图片类型支持
//    - AVATAR: 用户头像
//    - CHARACTER: 角色图片  
//    - CHAT: 聊天中的图片
//    - GALLERY: 画廊图片
//    - 等等...
// 
// 3. 使用示例：
//    getCharacterImageUrl('/male/male_01.png')
//    // 启用前缀时: https://lumiloveaibucket.s3.ap-southeast-2.amazonaws.com/male/male_01.png
//    // 禁用前缀时: /male/male_01.png
// 
// ===========================================

// 统一的图片URL处理函数
export const getImageUrl = (imagePath: string | undefined, type?: ImageType): string => {
  if (!imagePath) {
    // 返回默认占位图
    return '/placeholder-avatar.png'
  }
  
  return buildImageUrl(imagePath)
}

// 专门的头像URL处理
export const getAvatarUrl = (avatarPath: string | undefined): string => {
  // 如果头像路径为空或无效，返回一个特殊标识让组件处理
  if (!avatarPath || avatarPath.trim() === '') {
    return '/placeholder-avatar.png' // 这会触发SmartImage的错误处理
  }
  
  // 如果是base64格式的图片，直接返回
  if (avatarPath.startsWith('data:image/')) {
    return avatarPath
  }
  
  return getImageUrl(avatarPath, ImageType.AVATAR)
}

// 专门的角色图片URL处理
export const getCharacterImageUrl = (imagePath: string | undefined): string => {
  return getImageUrl(imagePath, ImageType.CHARACTER)
}

// 专门的聊天图片URL处理
export const getChatImageUrl = (imagePath: string | undefined): string => {
  return getImageUrl(imagePath, ImageType.CHAT)
}

// 专门的音频URL处理
export const getAudioUrl = (audioPath: string | undefined): string => {
  return getImageUrl(audioPath, ImageType.AUDIO)
}

// 专门的画廊图片URL处理
export const getGalleryImageUrl = (imagePath: string | undefined): string => {
  return getImageUrl(imagePath, ImageType.GALLERY)
}

// 批量处理图片URL数组
export const processImageUrls = (images: string[] | undefined): string[] => {
  if (!images) return []
  return images.map(img => getImageUrl(img))
}