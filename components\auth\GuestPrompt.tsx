"use client";

import Link from 'next/link';
import { Button } from '@/components/ui/button';

interface GuestPromptProps {
  feature: string;
  description?: string;
  showRegister?: boolean;
}

export function GuestPrompt({ feature, description, showRegister = true }: GuestPromptProps) {
  return (
    <div className="bg-gradient-to-r from-purple-500/20 to-pink-500/20 border border-purple-500/30 rounded-lg p-6 text-center">
      <div className="mb-4">
        <h3 className="text-white font-medium mb-2">Start {feature}</h3>
        <p className="text-purple-200 text-sm">
          {description || `Sign in to unlock ${feature} features`}
        </p>
      </div>
      
      <div className="flex gap-3 justify-center">
        <Button variant="outline" size="sm" asChild>
          <Link href="/login">Sign In</Link>
        </Button>
        {showRegister && (
          <Button variant="default" size="sm" asChild>
            <Link href="/register">Sign Up</Link>
          </Button>
        )}
      </div>
    </div>
  );
}

export function GuestHeader() {
  return (
    <div className="bg-gradient-to-r from-purple-900/30 to-pink-900/30 border-b border-purple-500/20 p-3 text-center">
      <div className="flex items-center justify-center gap-2 text-sm">
        <span className="text-purple-300">Guest Mode</span>
        <span className="text-gray-400">|</span>
        <Link 
          href="/login" 
          className="text-pink-400 hover:text-pink-300 transition-colors"
        >
          Sign in to unlock full features
        </Link>
      </div>
    </div>
  );
}