import { useState, useCallback } from "react";

interface Message {
  id: number;
  sender: "user" | "ai";
  text: string;
  timestamp: string;
  audioDuration?: number;
  hasImage?: boolean;
  imageSrc?: string;
  isThinking?: boolean;
}

interface UiState {
  // 模态框状态
  showShareDialog: boolean;
  showProfileModal: boolean;
  showCallDialog: boolean;
  showClearDialog: boolean;
  showSubscriptionModal: boolean;
  showReplyOptions: boolean;
  showRecommendation: boolean;
  showFullImage: boolean;
  showHeartBubbles: boolean;
  
  // 移动端状态
  showMobileSidebar: boolean;
  showMobileChatList: boolean;
  showChatList: boolean;
  showProfile: boolean;
  
  // 交互状态
  isFavorite: boolean;
  isFollowing: boolean;
  isFollowLoading: boolean;
  isMuted: boolean;
  isPlaying: number | null;
  
  // 加载状态
  isClearingHistory: boolean;
}

interface CallState {
  status: "connecting" | "active" | "ended";
  time: number;
  text: string;
  displayedText: string;
}

interface ChatState {
  // 消息相关
  messages: Message[];
  inputValue: string;
  replyType: string;
  
  // 角色相关
  characterData: any;
  characterLoading: boolean;
  
  // 聊天相关
  recentChats: any[];
  recentChatsLoading: boolean;
  selectedImage: string;
  
  // 配额
  pictureQuota: number;
  voiceQuota: number;
  
  // 加载状态
  isLoading: boolean;
  token: string | null;
}

const initialUiState: UiState = {
  showShareDialog: false,
  showProfileModal: false,
  showCallDialog: false,
  showClearDialog: false,
  showSubscriptionModal: false,
  showReplyOptions: false,
  showRecommendation: false,
  showFullImage: false,
  showHeartBubbles: false,
  showMobileSidebar: false,
  showMobileChatList: false,
  showChatList: true,
  showProfile: true,
  isFavorite: false,
  isFollowing: false,
  isFollowLoading: false,
  isMuted: false,
  isPlaying: null,
  isClearingHistory: false,
};

const initialCallState: CallState = {
  status: "connecting",
  time: 0,
  text: "",
  displayedText: "",
};

const initialChatState: ChatState = {
  messages: [],
  inputValue: "",
  replyType: "text",
  characterData: null,
  characterLoading: false,
  recentChats: [],
  recentChatsLoading: false,
  selectedImage: "",
  pictureQuota: 3,
  voiceQuota: 3,
  isLoading: false,
  token: null,
};

export function useChatState() {
  const [uiState, setUiState] = useState<UiState>(initialUiState);
  const [callState, setCallState] = useState<CallState>(initialCallState);
  const [chatState, setChatState] = useState<ChatState>(initialChatState);

  // UI状态更新器
  const updateUiState = useCallback((updates: Partial<UiState>) => {
    setUiState(prev => ({ ...prev, ...updates }));
  }, []);

  // 通话状态更新器
  const updateCallState = useCallback((updates: Partial<CallState> | ((prev: CallState) => Partial<CallState>)) => {
    if (typeof updates === 'function') {
      setCallState(prev => ({ ...prev, ...updates(prev) }));
    } else {
      setCallState(prev => ({ ...prev, ...updates }));
    }
  }, []);

  // 聊天状态更新器
  const updateChatState = useCallback((updates: Partial<ChatState>) => {
    setChatState(prev => ({ ...prev, ...updates }));
  }, []);

  // 专门的消息更新器
  const setMessages = useCallback((messages: Message[] | ((prev: Message[]) => Message[])) => {
    if (typeof messages === 'function') {
      setChatState(prev => ({ ...prev, messages: messages(prev.messages) }));
    } else {
      setChatState(prev => ({ ...prev, messages }));
    }
  }, []);

  // 输入值更新器
  const setInputValue = useCallback((value: string | ((prev: string) => string)) => {
    if (typeof value === 'function') {
      setChatState(prev => ({ ...prev, inputValue: value(prev.inputValue) }));
    } else {
      setChatState(prev => ({ ...prev, inputValue: value }));
    }
  }, []);

  // 回复类型更新器
  const setReplyType = useCallback((type: string) => {
    setChatState(prev => ({ ...prev, replyType: type }));
  }, []);

  // 选中图片更新器
  const setSelectedImage = useCallback((image: string) => {
    setChatState(prev => ({ ...prev, selectedImage: image }));
  }, []);

  return {
    // 状态
    uiState,
    callState,
    chatState,
    
    // 更新器
    updateUiState,
    updateCallState,
    updateChatState,
    setMessages,
    setInputValue,
    setReplyType,
    setSelectedImage,
    
    // 便捷访问器
    messages: chatState.messages,
    inputValue: chatState.inputValue,
    replyType: chatState.replyType,
    isLoading: chatState.isLoading,
    token: chatState.token,
    characterData: chatState.characterData,
    characterLoading: chatState.characterLoading,
    recentChats: chatState.recentChats,
    recentChatsLoading: chatState.recentChatsLoading,
    pictureQuota: chatState.pictureQuota,
    voiceQuota: chatState.voiceQuota,
    selectedImage: chatState.selectedImage,
    
    // 通话状态访问器
    callStatus: callState.status,
    callTime: callState.time,
    callText: callState.text,
    displayedText: callState.displayedText,
  };
}