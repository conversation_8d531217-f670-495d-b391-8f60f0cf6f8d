/**
 * 角色标签相关API接口
 * Character Labels API integration
 */

import { API_CONFIG } from '../config';
import { mainApiClient } from './client';

// ====================
// 类型定义
// ====================

// API返回的标签数据
export interface ApiLabel {
  id: number;
  labelName: string;
  gender: string; // 新增性别字段
  description: {
    description: string;
  };
  createdAt: string;
  updateAt: string;
}

// 转换后的标签数据（用于前端）
export interface Label {
  id: number;
  name: string;
  gender: string; // 新增性别字段
  description: string;
  createdAt: string;
  updatedAt: string;
}

// API返回的标签列表响应
export interface ApiLabelListResponse {
  success: boolean;
  data: ApiLabel[];
  message: string;
}

// 前端使用的响应格式
export interface LabelListResponse {
  success: boolean;
  data: {
    labels: Label[];
    total: number;
  };
  message: string;
}

// ====================
// API接口函数
// ====================

/**
 * 获取标签列表
 * GET /api/characters/label/list
 */
export async function getCharacterLabels(): Promise<LabelListResponse> {
  try {
    const result: ApiLabelListResponse = await mainApiClient.get(
      API_CONFIG.ENDPOINTS.CHARACTERS.LABEL_LIST
    );

    if (!result.success) {
      throw new Error(result.message || 'Failed to fetch character labels');
    }

    // 转换API数据为前端数据格式
    const labels: Label[] = result.data.map(transformApiLabel);

    return {
      success: true,
      message: result.message,
      data: {
        labels,
        total: labels.length,
      },
    };
  } catch (error) {
    console.error('获取标签列表失败:', error);
    throw error;
  }
}

// ====================
// 辅助函数
// ====================

/**
 * 转换API标签数据为前端格式
 * @param apiLabel API返回的标签数据
 */
function transformApiLabel(apiLabel: ApiLabel): Label {
  return {
    id: apiLabel.id,
    name: apiLabel.labelName,
    gender: apiLabel.gender,
    description: apiLabel.description.description,
    createdAt: apiLabel.createdAt,
    updatedAt: apiLabel.updateAt,
  };
}

/**
 * 将API标签数据转换为过滤标签数组
 * @param labels API返回的标签数据
 * @param gender 性别（用于过滤标签）
 */
export function convertLabelsToFilterTags(
  labels: Label[],
  gender?: 'male' | 'female'
): string[] {
  // 添加"All"选项作为第一个标签
  const allTag = ['All'];
  
  // 根据性别过滤标签
  let filteredLabels = labels;
  if (gender) {
    filteredLabels = labels.filter(label => label.gender === gender);
  }
  
  // 提取过滤后的标签名称
  const labelNames = filteredLabels.map(label => label.name);
  
  // 合并All标签和过滤后的标签
  const allTags = [...allTag, ...labelNames];
  
  return allTags;
}

// 导出用于其他文件的类型
// ApiLabel 已在上面以 export interface 形式导出，无需重复导出