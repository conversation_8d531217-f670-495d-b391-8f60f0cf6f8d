'use client';

import { useState, useEffect } from 'react';

const AGE_VERIFICATION_KEY = 'age_verification_confirmed';
const VERIFICATION_DURATION = 30 * 24 * 60 * 60 * 1000; // 30 days in milliseconds

interface AgeVerificationData {
  confirmed: boolean;
  timestamp: number;
}

export function useAgeVerification() {
  const [isVerified, setIsVerified] = useState<boolean | null>(null);
  const [showModal, setShowModal] = useState(false);

  useEffect(() => {
    checkAgeVerification();
  }, []);

  const checkAgeVerification = () => {
    try {
      const stored = localStorage.getItem(AGE_VERIFICATION_KEY);
      if (stored) {
        const data: AgeVerificationData = JSON.parse(stored);
        const now = Date.now();
        
        // Check if verification is still valid (within 30 days)
        if (data.confirmed && (now - data.timestamp) < VERIFICATION_DURATION) {
          setIsVerified(true);
          setShowModal(false);
          return;
        }
        
        // Verification expired, remove old data
        localStorage.removeItem(AGE_VERIFICATION_KEY);
      }
      
      // No valid verification found
      setIsVerified(false);
      setShowModal(true);
    } catch (error) {
      console.error('Error checking age verification:', error);
      setIsVerified(false);
      setShowModal(true);
    }
  };

  const confirmAge = () => {
    try {
      const verificationData: AgeVerificationData = {
        confirmed: true,
        timestamp: Date.now(),
      };
      
      localStorage.setItem(AGE_VERIFICATION_KEY, JSON.stringify(verificationData));
      setIsVerified(true);
      setShowModal(false);
    } catch (error) {
      console.error('Error saving age verification:', error);
    }
  };

  const denyAge = () => {
    // Clear any existing verification
    localStorage.removeItem(AGE_VERIFICATION_KEY);
    setIsVerified(false);
    setShowModal(false);
    
    // Redirect to a safe page or show blocked message
    // For now, we'll redirect to a blank page
    setTimeout(() => {
      window.location.href = 'about:blank';
    }, 2000);
  };

  const resetVerification = () => {
    localStorage.removeItem(AGE_VERIFICATION_KEY);
    setIsVerified(false);
    setShowModal(true);
  };

  return {
    isVerified,
    showModal,
    confirmAge,
    denyAge,
    resetVerification,
  };
}