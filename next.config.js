/** @type {import('next').NextConfig} */
const nextConfig = {
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'lumiloveaibucket.s3.ap-southeast-2.amazonaws.com',
        port: '',
        pathname: '/**',
      },
    ],
  },
  async rewrites() {
    return [
      {
        source: '/api/users/:path*',
        destination: 'https://api.loomelove.ai/api/users/:path*',
      },
      {
        source: '/api/auth/:path*',
        destination: 'https://api.loomelove.ai/api/auth/:path*',
      },
      {
        source: '/api/:path*',
        destination: 'https://api.loomelove.ai/api/:path*',
      },
    ];
  },
};

module.exports = nextConfig;
