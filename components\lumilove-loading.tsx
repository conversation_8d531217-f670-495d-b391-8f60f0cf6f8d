"use client"

import { useEffect, useRef, useCallback } from "react"
import { cn } from "@/lib/utils"

type Phase = "scatter" | "forming" | "formed" | "sparkle" | "dissolve" | "free"

interface Particle {
  id: number
  x: number
  y: number
  targetX: number
  targetY: number
  size: number
  baseSize: number
  color: string
  baseColor: string
  alpha: number
  speed: number
  osc: number
  isForming: boolean
  isLetter: boolean
  delay: number
  vx: number
  vy: number
  z: number
  trailX: number[]
  trailY: number[]
  trailPtr: number
  heartbeat: number
  sparkle: number
  magnetism: number
  temperature: number
  intimacy: number
  sparkleIntensity?: number
  sparklePhase?: number
  dissolveAge?: number
  dissolveSpeed?: number
  dissolveDirection?: { x: number; y: number }
  nseedX?: number
  nseedY?: number
}

interface LumidateLoadingProps {
  className?: string
  onComplete?: () => void
  text?: string
  loop?: boolean
  density?: number
  font?: string
  mobileAdaptive?: boolean
}

const romanticPalette = [
  "#FF69B4",
  "#FF1493",
  "#DA70D6",
  "#BA55D3",
  "#9370DB",
  "#8A2BE2",
  "#FF6347",
  "#FFB6C1",
  "#FFC0CB",
  "#DDA0DD",
]

const rgbaFromColor = (color: string, a: number) => {
  if (color.startsWith("#")) {
    const h = color.replace("#", "")
    const r = Number.parseInt(h.substring(0, 2), 16)
    const g = Number.parseInt(h.substring(2, 4), 16)
    const b = Number.parseInt(h.substring(4, 6), 16)
    return `rgba(${r},${g},${b},${a})`
  }
  const nums = color.match(/\d+(\.\d+)?/g)
  if (!nums) return color
  const [r, g, b] = nums.map(Number)
  return `rgba(${r},${g},${b},${a})`
}

// 增强饱和度和亮度但保持色调
const enhanceColor = (cssColor: string, factor: number) => {
  let r: number, g: number, b: number
  if (cssColor.startsWith("#")) {
    const h = cssColor.replace("#", "")
    r = Number.parseInt(h.substring(0, 2), 16)
    g = Number.parseInt(h.substring(2, 4), 16)
    b = Number.parseInt(h.substring(4, 6), 16)
  } else {
    const nums = cssColor.match(/\d+(\.\d+)?/g) || ["255", "255", "255"]
    r = Number(nums[0])
    g = Number(nums[1])
    b = Number(nums[2])
  }

  const max = Math.max(r, g, b) / 255
  const min = Math.min(r, g, b) / 255
  const diff = max - min

  let h = 0
  if (diff !== 0) {
    if (max === r / 255) h = ((g - b) / 255 / diff) % 6
    else if (max === g / 255) h = (b - r) / 255 / diff + 2
    else h = (r - g) / 255 / diff + 4
  }
  h = Math.round(h * 60)
  if (h < 0) h += 360

  const l = (max + min) / 2
  const s = diff === 0 ? 0 : diff / (1 - Math.abs(2 * l - 1))

  const newS = Math.min(1, s + factor * 0.4)
  const newL = Math.min(0.85, l + factor * 0.25)

  const c = (1 - Math.abs(2 * newL - 1)) * newS
  const x = c * (1 - Math.abs(((h / 60) % 2) - 1))
  const m = newL - c / 2

  let rNew, gNew, bNew
  if (h >= 0 && h < 60) [rNew, gNew, bNew] = [c, x, 0]
  else if (h >= 60 && h < 120) [rNew, gNew, bNew] = [x, c, 0]
  else if (h >= 120 && h < 180) [rNew, gNew, bNew] = [0, c, x]
  else if (h >= 180 && h < 240) [rNew, gNew, bNew] = [0, x, c]
  else if (h >= 240 && h < 300) [rNew, gNew, bNew] = [x, 0, c]
  else [rNew, gNew, bNew] = [c, 0, x]

  const finalR = Math.round((rNew + m) * 255)
  const finalG = Math.round((gNew + m) * 255)
  const finalB = Math.round((bNew + m) * 255)

  return `rgb(${finalR},${finalG},${finalB})`
}

const loveFlow = (x: number, y: number, t: number, cx: number, cy: number) => {
  const dx = (x - cx) * 0.003
  const dy = (y - cy) * 0.003
  const r = Math.sqrt(dx * dx + dy * dy)
  const ang = Math.atan2(dy, dx)
  const heartX = 16 * Math.pow(Math.sin(ang), 3)
  const heartY = 13 * Math.cos(ang) - 5 * Math.cos(2 * ang) - 2 * Math.cos(3 * ang) - Math.cos(4 * ang)
  const swirl = Math.sin(t * 0.5 + r * 3) * 0.3
  const nx = heartX * 0.1 + Math.cos(ang + swirl)
  const ny = heartY * 0.1 + Math.sin(ang + swirl)
  const len = Math.hypot(nx, ny) || 1
  return { nx: nx / len, ny: ny / len }
}

const detectProfile = () => {
  const isCoarse = window.matchMedia?.("(pointer: coarse)").matches ?? false
  const isMobile = isCoarse || Math.min(window.innerWidth, window.innerHeight) <= 540
  const prefersReduce = window.matchMedia?.("(prefers-reduced-motion: reduce)").matches ?? false
  const dpr = Math.min(window.devicePixelRatio || 1, isMobile ? 1.5 : 2)
  return { isMobile, prefersReduce, dpr }
}

const easeInOut = (t: number) => (t < 0.5 ? 4 * t * t * t : 1 - Math.pow(-2 * t + 2, 3) / 2)
const easeOut = (t: number) => 1 - Math.pow(1 - t, 3)
const clamp01 = (x: number) => Math.max(0, Math.min(1, x))
const lerp = (a: number, b: number, t: number) => a + (b - a) * t

export function LumidateLoading({
  className,
  onComplete,
  text = "LUMILOVE",
  loop = false,
  density,
  font = `"Playfair Display","Georgia","Times New Roman",serif`,
  mobileAdaptive = true,
}: LumidateLoadingProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const rafRef = useRef<number | undefined>(undefined)
  const particlesRef = useRef<Particle[]>([])
  const lastTimeRef = useRef<number>(0)
  const initializedRef = useRef(false)

  const phaseRef = useRef<Phase>("scatter")
  const phaseElapsedRef = useRef(0)
  const phaseJustChangedRef = useRef(true)
  const completedRef = useRef(false)
  const timeoutsRef = useRef<number[]>([])
  const particleIdCounter = useRef(0)

        const formedMsRef = useRef(300) // 清晰展示时间
      const sparkleMsRef = useRef(500) // 闪烁时间
      const dissolveMsRef = useRef(800) // 消散时间
  const trailLenRef = useRef(8)
  const maxPointsRef = useRef(2000)

  const generateTextTargets = useCallback(
    (
      canvasW: number,
      canvasH: number,
      str: string,
      opts?: { density?: number; maxWidthPct?: number; threshold?: number; lineHeightPct?: number; maxPoints?: number },
    ) => {
      const { density = 7, maxWidthPct = 0.85, threshold = 80, lineHeightPct = 0.5, maxPoints = 1200 } = opts || {}
      const off = document.createElement("canvas")
      const ctx = off.getContext("2d")!
      off.width = Math.max(1, Math.floor(canvasW * maxWidthPct))
      off.height = Math.max(1, Math.floor(canvasH * lineHeightPct))
      ctx.textBaseline = "middle"
      ctx.textAlign = "center"

      let fs = Math.floor(off.height * 0.9)
      const measure = (size: number) => {
        ctx.font = `900 ${size}px ${font}`
        return ctx.measureText(str).width
      }
      while (measure(fs) > off.width && fs > 20) fs -= 2
      ctx.font = `900 ${fs}px ${font}`

      ctx.clearRect(0, 0, off.width, off.height)
      ctx.fillStyle = "#fff"
      ctx.fillText(str, off.width / 2, off.height / 2)

      const { data, width, height } = ctx.getImageData(0, 0, off.width, off.height)
      const pts: { x: number; y: number }[] = []
      for (let y = 0; y < height; y += density) {
        for (let x = 0; x < width; x += density) {
          const a = data[(y * width + x) * 4 + 3]
          if (a > threshold) {
            const gx = (canvasW - width) / 2 + x
            const gy = (canvasH - height) / 2 + y
            const organicX = gx + (Math.random() - 0.5) * density * 0.6
            const organicY = gy + (Math.random() - 0.5) * density * 0.6
            pts.push({ x: organicX, y: organicY })
          }
        }
      }
      if (pts.length > maxPoints) {
        const out: typeof pts = []
        for (let i = 0; i < maxPoints; i++) out.push(pts[(Math.random() * pts.length) | 0])
        return out
      }
      return pts
    },
    [font],
  )

  const changePhase = (next: Phase) => {
    phaseRef.current = next
    phaseElapsedRef.current = 0
    phaseJustChangedRef.current = true
  }

  const initializeParticles = useCallback(
    (w: number, h: number) => {
      const { isMobile, prefersReduce } = detectProfile()
      const dynDensity = density ?? (isMobile ? 8 : 7)
      maxPointsRef.current = isMobile ? 800 : 1200
      trailLenRef.current = isMobile ? 6 : 10
      formedMsRef.current = prefersReduce ? 250 : 300
      sparkleMsRef.current = prefersReduce ? 400 : 500
      dissolveMsRef.current = prefersReduce ? 600 : 800

      const targets = generateTextTargets(w, h, text, {
        density: dynDensity,
        maxPoints: maxPointsRef.current,
      })
      const parts: Particle[] = []
      particleIdCounter.current = 0

      for (let i = 0; i < targets.length; i++) {
        const t = targets[i]
        const baseSize = Math.random() * 2.5 + 2.0
        const angle = Math.random() * Math.PI * 2
        const distance = Math.random() * 400 + 200
        const startX = w / 2 + Math.cos(angle) * distance
        const startY = h / 2 + Math.sin(angle) * distance
        const z = Math.pow(Math.random(), 1.5)
        const trailLen = trailLenRef.current + Math.floor((Math.random() - 0.5) * 4)
        const baseColor = romanticPalette[(Math.random() * romanticPalette.length) | 0]

        parts.push({
          id: particleIdCounter.current++,
          x: startX,
          y: startY,
          targetX: t.x,
          targetY: t.y,
          size: baseSize * (0.8 + 0.4 * (1 - z)),
          baseSize: baseSize * (0.8 + 0.4 * (1 - z)),
          color: baseColor,
          baseColor: baseColor,
          alpha: 0.9 * (0.6 + 0.4 * (1 - z)),
          speed: 0.25 + Math.random() * 0.12, // 更快的聚集速度
          osc: Math.random() * Math.PI * 2,
          isForming: false,
          isLetter: true,
          delay: Math.floor(i % 3) * 0.3, // 减少延迟
          vx: (Math.random() - 0.5) * 1.5,
          vy: (Math.random() - 0.5) * 1.5,
          z,
          trailX: new Array(Math.max(4, trailLen)).fill(startX),
          trailY: new Array(Math.max(4, trailLen)).fill(startY),
          trailPtr: 0,
          heartbeat: Math.random() * Math.PI * 2,
          sparkle: Math.random() * Math.PI * 2,
          magnetism: 0.7 + Math.random() * 0.3, // 更强的磁性
          temperature: Math.random(),
          intimacy: Math.random(),
          nseedX: Math.random() * 1000 + 1,
          nseedY: Math.random() * 1000 + 1,
          sparkleIntensity: Math.random(),
          sparklePhase: Math.random() * Math.PI * 2,
          dissolveAge: 0,
          dissolveSpeed: 0.6 + Math.random() * 0.4,
        })
      }

      // 减少装饰粒子
      const decor = isMobile ? 3 : 6
      for (let i = 0; i < decor; i++) {
        const baseSize = Math.random() * 1.5 + 1
        const z = Math.random()
        const trailLen = trailLenRef.current + Math.floor((Math.random() - 0.5) * 3)
        const sx = Math.random() * w
        const sy = Math.random() * h
        const baseColor = romanticPalette[(Math.random() * romanticPalette.length) | 0]
        parts.push({
          id: particleIdCounter.current++,
          x: sx,
          y: sy,
          targetX: Math.random() * w,
          targetY: Math.random() * h,
          size: baseSize * (0.7 + 0.3 * (1 - z)),
          baseSize: baseSize * (0.7 + 0.3 * (1 - z)),
          color: baseColor,
          baseColor: baseColor,
          alpha: 0.25 * (0.5 + 0.5 * (1 - z)),
          speed: 0.01 + Math.random() * 0.02,
          osc: Math.random() * Math.PI * 2,
          isForming: false,
          isLetter: false,
          delay: 0,
          vx: (Math.random() - 0.5) * 0.4,
          vy: (Math.random() - 0.5) * 0.4,
          z,
          trailX: new Array(Math.max(3, trailLen)).fill(sx),
          trailY: new Array(Math.max(3, trailLen)).fill(sy),
          trailPtr: 0,
          heartbeat: Math.random() * Math.PI * 2,
          sparkle: Math.random() * Math.PI * 2,
          magnetism: 0.1 + Math.random() * 0.2,
          temperature: Math.random(),
          intimacy: Math.random() * 0.3,
          nseedX: Math.random() * 1000 + 1,
          nseedY: Math.random() * 1000 + 1,
          sparkleIntensity: Math.random(),
          sparklePhase: Math.random() * Math.PI * 2,
          dissolveAge: 0,
          dissolveSpeed: 0.2 + Math.random() * 0.3,
        })
      }

      particlesRef.current = parts
      completedRef.current = false
      changePhase("scatter")
    },
    [generateTextTargets, text, density],
  )

  const update = useCallback(
    (dt: number, w: number, h: number) => {
      const arr = particlesRef.current
      const phase = phaseRef.current
      phaseElapsedRef.current += dt
      const nowt = performance.now() * 0.001

      // 更快的阶段转换
      if (phase === "scatter" && phaseElapsedRef.current > 2.5) {
        arr.forEach((p) => {
          if (p.isLetter) p.isForming = true
        })
        changePhase("forming")
      } else if (phase === "forming") {
        const letters = arr.filter((p) => p.isLetter)
        let formed = 0
        for (const p of letters) {
          const dx = p.targetX - p.x,
            dy = p.targetY - p.y
          if (Math.hypot(dx, dy) <= 6) formed++
        }
        const ratio = letters.length ? formed / letters.length : 0

        // 更激进的强制聚合
        if (phaseElapsedRef.current > 12 && ratio < 0.7) {
          for (const p of letters) {
            p.delay = 0
            const dx = p.targetX - p.x,
              dy = p.targetY - p.y
            p.vx += dx * 0.2 * p.magnetism
            p.vy += dy * 0.2 * p.magnetism
          }
        }

        if (ratio > 0.9 && phaseElapsedRef.current > 4) changePhase("formed")
      } else if (phase === "formed") {
        if (phaseElapsedRef.current * 16.666 > formedMsRef.current) changePhase("sparkle")
      } else if (phase === "sparkle") {
        if (phaseElapsedRef.current * 16.666 > sparkleMsRef.current) changePhase("dissolve")
      } else if (phase === "dissolve") {
        if (phaseElapsedRef.current * 16.666 > dissolveMsRef.current) {
          changePhase(loop ? "scatter" : "free")
          if (!loop && !completedRef.current) {
            completedRef.current = true
            const t = window.setTimeout(() => onComplete?.(), 300)
            timeoutsRef.current.push(t)
          }
        }
      }

      // 初始化消散方向
      if (phaseJustChangedRef.current && phaseRef.current === "dissolve") {
        phaseJustChangedRef.current = false
        for (const p of arr) {
          if (p.isLetter) {
            const angle = Math.random() * Math.PI * 2
            const distance = 0.4 + Math.random() * 0.8
            p.dissolveDirection = {
              x: Math.cos(angle) * distance,
              y: Math.sin(angle) * distance - 0.15,
            }
            p.dissolveAge = 0
            p.color = p.baseColor
          }
        }
      }

      for (const p of arr) {
        p.osc += 0.04 * dt
        p.heartbeat += 0.05 * dt * (0.8 + p.temperature * 0.4)
        p.sparkle += 0.08 * dt * (1 + p.intimacy)
        if (p.sparklePhase !== undefined) p.sparklePhase += 0.1 * dt

        if (phaseRef.current === "scatter") {
          const { nx, ny } = loveFlow(p.x, p.y, nowt, w / 2, h / 2)
          p.vx += nx * 0.02 + Math.sin(p.heartbeat) * 0.025
          p.vy += ny * 0.02 + Math.cos(p.heartbeat * 0.7) * 0.025
          p.vx *= 0.985
          p.vy *= 0.985
          p.x += p.vx * dt * 4.0
          p.y += p.vy * dt * 4.0
          p.color = p.baseColor
        } else if (phaseRef.current === "forming" && p.isForming) {
          if (phaseElapsedRef.current <= p.delay) {
            const { nx, ny } = loveFlow(p.x, p.y, nowt, w / 2, h / 2)
            p.vx += nx * 0.015
            p.vy += ny * 0.015
            p.vx *= 0.98
            p.vy *= 0.98
            p.x += p.vx * dt * 3.5
            p.y += p.vy * dt * 3.5
          } else {
            const dx = p.targetX - p.x,
              dy = p.targetY - p.y
            const dist = Math.hypot(dx, dy) || 1
            const attract = 0.25 * p.magnetism * Math.min(2.0, Math.max(0.8, dist / 80)) * p.speed
            p.vx += dx * attract
            p.vy += dy * attract

            const pulse = Math.sin(p.heartbeat * 2.5) * 0.4
            p.vx += dx * pulse * 0.015
            p.vy += dy * pulse * 0.015

            const spd = Math.hypot(p.vx, p.vy),
              maxV = 8.0
            if (spd > maxV) {
              p.vx = (p.vx / spd) * maxV
              p.vy = (p.vy / spd) * maxV
            }
            p.vx *= 0.88
            p.vy *= 0.88
            p.x += p.vx * dt * 1.8
            p.y += p.vy * dt * 1.8
          }
          p.color = p.baseColor
        } else if (phaseRef.current === "formed") {
          // 完全稳定的文字展示
          const dx = p.targetX - p.x,
            dy = p.targetY - p.y
                     p.x += dx * 0.5 // 快速锁定到目标位置
           p.y += dy * 0.5
          p.vx *= 0.7
          p.vy *= 0.7
          p.size = p.baseSize
          p.alpha = 1.0
          p.color = p.baseColor
        } else if (phaseRef.current === "sparkle") {
          // 保持位置稳定，只改变视觉效果
          const tNorm = clamp01((phaseElapsedRef.current * 16.666) / sparkleMsRef.current)
          const t = easeInOut(tNorm)
          const dx = p.targetX - p.x,
            dy = p.targetY - p.y
                     p.x += dx * 0.4 // 继续锁定位置
           p.y += dy * 0.4
          p.vx *= 0.8
          p.vy *= 0.8
          p.size = p.baseSize * lerp(1.0, 1.2, t)
          p.alpha = 1.0
          if (p.isLetter) {
            p.color = enhanceColor(p.baseColor, t * 0.9)
          } else {
            p.color = p.baseColor
          }
        } else if (phaseRef.current === "dissolve") {
          if (p.isLetter && p.dissolveDirection) {
            p.dissolveAge! += dt * p.dissolveSpeed!
            const dissolveT = Math.min(1, p.dissolveAge! / 80)

            const easeT = easeOut(dissolveT)
            p.vx = p.dissolveDirection.x * easeT * 3
            p.vy = p.dissolveDirection.y * easeT * 3

            const swirl = Math.sin(p.dissolveAge! * 0.08 + p.id) * 0.6
            p.vx += swirl * 0.4
            p.vy += Math.cos(p.dissolveAge! * 0.06 + p.id) * 0.3

            p.x += p.vx * dt
            p.y += p.vy * dt

            const fadeStart = 0.4
            if (dissolveT > fadeStart) {
              const fadeT = (dissolveT - fadeStart) / (1 - fadeStart)
              p.size = p.baseSize * (1 - fadeT * 0.8)
              p.alpha = (1 - fadeT) * 0.95
            } else {
              p.size = p.baseSize * (1 + Math.sin(p.dissolveAge! * 0.15) * 0.08)
              p.alpha = 0.95
            }
            p.color = p.baseColor
          }
        } else if (phaseRef.current === "free") {
          const { nx, ny } = loveFlow(p.x, p.y, nowt, w / 2, h / 2)
          p.vx += nx * 0.06 * p.magnetism
          p.vy += ny * 0.05 * p.magnetism
          p.vx *= 0.97
          p.vy *= 0.97
          p.x += p.vx * dt
          p.y += p.vy * dt
          p.color = p.baseColor
        }

        // 轨迹更新
        const ptr = p.trailPtr
        p.trailX[ptr] = p.x
        p.trailY[ptr] = p.y
        p.trailPtr = (ptr + 1) % p.trailX.length

        // 边界处理
        const margin = 40
        let wrapped = false
        if (p.x < -margin) {
          p.x = w + margin
          wrapped = true
        }
        if (p.x > w + margin) {
          p.x = -margin
          wrapped = true
        }
        if (p.y < -margin) {
          p.y = h + margin
          wrapped = true
        }
        if (p.y > h + margin) {
          p.y = -margin
          wrapped = true
        }
        if (wrapped) {
          p.trailX.fill(p.x)
          p.trailY.fill(p.y)
          p.trailPtr = 0
        }
      }
    },
    [loop, onComplete],
  )

  const draw = useCallback((ctx: CanvasRenderingContext2D) => {
    const phase = phaseRef.current
    const withTrails = phase === "free"
    const MAX_SEG = 50

    const drawTrail = (p: Particle) => {
      const n = p.trailX.length
      let idx = (p.trailPtr + 1) % n
      let lastX = p.trailX[idx],
        lastY = p.trailY[idx]
      for (let k = 1; k < n; k++) {
        idx = (idx + 1) % n
        const x = p.trailX[idx],
          y = p.trailY[idx]
        const dx = x - lastX,
          dy = y - lastY
        const d2 = dx * dx + dy * dy
        if (d2 < MAX_SEG * MAX_SEG) {
          const a = (k / n) * (p.alpha * 0.6) * (1 - p.z * 0.2)
          if (a > 0.02) {
            ctx.globalAlpha = a
            ctx.beginPath()
            ctx.moveTo(lastX, lastY)
            ctx.lineTo(x, y)
            ctx.lineWidth = Math.max(1.0, p.size * (1 - k / n) * (1 - p.z * 0.3))
            ctx.lineCap = "round"
            ctx.strokeStyle = p.color
            ctx.stroke()
          }
        }
        lastX = x
        lastY = y
      }
    }

    const parts = particlesRef.current
    if (withTrails) for (const p of parts) if (p.alpha > 0.03) drawTrail(p)

    for (const p of parts) {
      if (p.alpha <= 0.03) continue
      ctx.save()
      ctx.globalAlpha = Math.min(1, p.alpha)
      ctx.translate(p.x, p.y)

      // 根据阶段调整光晕
      let glowSize = p.size * 1.1
      if (phase === "formed") {
        glowSize = p.size * 1.0 // 最清晰状态
      } else if (phase === "sparkle" && p.isLetter) {
        glowSize = p.size * 1.3 // 适度发光
      }

      const g = ctx.createRadialGradient(0, 0, 0, 0, 0, glowSize)
      const warmth = Math.min(1, 0.4 + p.temperature * 0.6)

      if (phase === "formed") {
        // 最清晰的渐变
        g.addColorStop(0, rgbaFromColor(p.color, warmth * 1.0))
        g.addColorStop(0.2, rgbaFromColor(p.color, warmth * 0.8))
        g.addColorStop(0.6, rgbaFromColor(p.color, warmth * 0.4))
        g.addColorStop(1, rgbaFromColor(p.color, 0))
      } else if (phase === "sparkle" && p.isLetter) {
        // 闪烁但保持清晰
        g.addColorStop(0, rgbaFromColor(p.color, warmth * 0.95))
        g.addColorStop(0.25, rgbaFromColor(p.color, warmth * 0.8))
        g.addColorStop(0.65, rgbaFromColor(p.color, warmth * 0.35))
        g.addColorStop(1, rgbaFromColor(p.color, 0))
      } else {
        g.addColorStop(0, rgbaFromColor(p.color, warmth))
        g.addColorStop(0.4, rgbaFromColor(p.color, warmth * 0.6))
        g.addColorStop(0.8, rgbaFromColor(p.color, warmth * 0.2))
        g.addColorStop(1, rgbaFromColor(p.color, 0))
      }

      ctx.fillStyle = g
      ctx.beginPath()
      ctx.arc(0, 0, glowSize, 0, Math.PI * 2)
      ctx.fill()

      // 核心亮点
      if (phase === "formed" || phase === "forming" || phase === "sparkle") {
        let coreSize = p.size * 0.45
        if (phase === "sparkle" && p.isLetter) coreSize *= 1.4
        const coreGrad = ctx.createRadialGradient(0, 0, 0, 0, 0, coreSize)
        coreGrad.addColorStop(0, rgbaFromColor(p.color, 0.98))
        coreGrad.addColorStop(0.4, rgbaFromColor(p.color, 0.85))
        coreGrad.addColorStop(1, rgbaFromColor(p.color, 0))
        ctx.fillStyle = coreGrad
        ctx.beginPath()
        ctx.arc(0, 0, coreSize, 0, Math.PI * 2)
        ctx.fill()

        // 超清晰中心点
        if (phase === "formed" || (phase === "sparkle" && p.isLetter)) {
          const centerSize = p.size * 0.18
          const centerGrad = ctx.createRadialGradient(0, 0, 0, 0, 0, centerSize)
          centerGrad.addColorStop(0, rgbaFromColor(p.color, 1.0))
          centerGrad.addColorStop(1, rgbaFromColor(p.color, 0.8))
          ctx.fillStyle = centerGrad
          ctx.beginPath()
          ctx.arc(0, 0, centerSize, 0, Math.PI * 2)
          ctx.fill()
        }
      }

      ctx.restore()
    }
  }, [])

  useEffect(() => {
    const canvas = canvasRef.current
    if (!canvas || initializedRef.current) return

    const profile = detectProfile()
    const setCanvasSize = () => {
      const rect = canvas.getBoundingClientRect()
      let width = rect.width,
        height = rect.height
      if (width < 2 || height < 2) {
        width = window.innerWidth
        height = window.innerHeight
      }
      const dpr = mobileAdaptive ? profile.dpr : window.devicePixelRatio || 1
      canvas.width = Math.max(1, Math.floor(width * dpr))
      canvas.height = Math.max(1, Math.floor(height * dpr))
      canvas.style.width = `${width}px`
      canvas.style.height = `${height}px`
      const ctx = canvas.getContext("2d")!
      ctx.setTransform(dpr, 0, 0, dpr, 0, 0)
      ctx.imageSmoothingEnabled = true
      ctx.imageSmoothingQuality = "high"
      return { width, height }
    }

    const { width, height } = setCanvasSize()
    initializeParticles(width, height)
    initializedRef.current = true

    const tick = () => {
      rafRef.current = requestAnimationFrame(tick)
      const ctx = canvas.getContext("2d")
      if (!ctx) return

      const now = performance.now()
      const last = lastTimeRef.current || now
      let dt = (now - last) / 16.666
      dt = Math.max(0, Math.min(dt, 3))
      lastTimeRef.current = now

      const rect = canvas.getBoundingClientRect()

      ctx.clearRect(0, 0, rect.width, rect.height)
      const bg = ctx.createLinearGradient(0, 0, 0, rect.height)
      bg.addColorStop(0, "rgba(0,0,0,1)")
      bg.addColorStop(1, "rgba(45,15,60,1)")
      ctx.fillStyle = bg
      ctx.fillRect(0, 0, rect.width, rect.height)

      // 中心光晕
      const formingProg =
        phaseRef.current === "forming" || phaseRef.current === "formed" ? clamp01(phaseElapsedRef.current / 60) : 0
      const sparkleProg = phaseRef.current === "sparkle" ? 1 : 0
      const heartGlow = Math.min(1, formingProg * 0.7 + sparkleProg * 0.9)
      if (heartGlow > 0) {
        const radius = 80 + heartGlow * 350
        const hg = ctx.createRadialGradient(rect.width / 2, rect.height / 2, 0, rect.width / 2, rect.height / 2, radius)
        hg.addColorStop(0, `rgba(255,105,180,${heartGlow * 0.2})`)
        hg.addColorStop(0.5, `rgba(218,112,214,${heartGlow * 0.12})`)
        hg.addColorStop(1, "rgba(255,105,180,0)")
        ctx.fillStyle = hg
        ctx.fillRect(0, 0, rect.width, rect.height)
      }

      update(dt, rect.width, rect.height)
      draw(ctx)
    }

    tick()

    return () => {
      if (rafRef.current) cancelAnimationFrame(rafRef.current)
      timeoutsRef.current.forEach((t) => clearTimeout(t))
      timeoutsRef.current = []
      initializedRef.current = false
    }
  }, [initializeParticles, update, draw])

  return (
    <div className={cn("fixed inset-0 z-50 flex items-center justify-center", className)}>
      <canvas ref={canvasRef} className="absolute inset-0 w-full h-full" />
    </div>
  )
}
