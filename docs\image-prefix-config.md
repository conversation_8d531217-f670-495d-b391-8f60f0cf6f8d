# 图片前缀拼接配置说明

## 概述

为了更好地控制图片URL的处理方式，我们添加了一个配置开关来决定是否为相对路径的图片添加CDN前缀。

## 配置方式

### 环境变量

在 `.env.local` 文件中设置：

```bash
# 启用图片前缀拼接 (默认)
NEXT_PUBLIC_ENABLE_IMAGE_PREFIX=true

# 禁用图片前缀拼接
NEXT_PUBLIC_ENABLE_IMAGE_PREFIX=false
```

### 代码配置

在 `lib/config.ts` 中：

```typescript
export const IMAGE_CONFIG = {
  CDN_BASE_URL: 'https://lumiloveaibucket.s3.ap-southeast-2.amazonaws.com',
  ENABLE_PREFIX_CONCAT: process.env.NEXT_PUBLIC_ENABLE_IMAGE_PREFIX === 'true' || true,
}
```

## 行为说明

### 启用前缀拼接 (ENABLE_PREFIX_CONCAT = true)

- **相对路径**: `/male/male_01.png` → `https://lumiloveaibucket.s3.ap-southeast-2.amazonaws.com/male/male_01.png`
- **完整URL**: `https://example.com/image.jpg` → `https://example.com/image.jpg` (保持不变)

### 禁用前缀拼接 (ENABLE_PREFIX_CONCAT = false)

- **相对路径**: `/male/male_01.png` → `/male/male_01.png` (保持不变)
- **完整URL**: `https://example.com/image.jpg` → `https://example.com/image.jpg` (保持不变)

## 使用场景

### 1. 生产环境
```bash
NEXT_PUBLIC_ENABLE_IMAGE_PREFIX=true
```
- 使用CDN加速图片加载
- 适合部署到生产环境

### 2. 开发环境
```bash
NEXT_PUBLIC_ENABLE_IMAGE_PREFIX=false
```
- 使用本地图片资源
- 方便开发和调试
- 不依赖外部CDN

### 3. 混合场景
```bash
NEXT_PUBLIC_ENABLE_IMAGE_PREFIX=true
```
- 前端固定数据使用本地路径 (通过相对路径)
- 后端API数据使用完整URL
- 系统自动区分处理

## 影响的组件

此配置影响所有使用以下函数的组件：

- `buildImageUrl()` - 核心URL构建函数
- `getImageUrl()` - 通用图片URL获取
- `getCharacterImageUrl()` - 角色图片URL
- `getAvatarUrl()` - 头像URL
- `getGalleryImageUrl()` - 画廊图片URL

## 调试信息

启用配置后，控制台会显示相应的日志：

```
🔗 图片前缀拼接已启用: /male/male_01.png -> https://lumiloveaibucket.s3.ap-southeast-2.amazonaws.com/male/male_01.png
🚫 图片前缀拼接已关闭，使用原始路径: /male/male_01.png
```

## 最佳实践

1. **生产环境**: 启用前缀拼接，使用CDN加速
2. **开发环境**: 根据需要选择，建议禁用以使用本地资源
3. **测试环境**: 启用前缀拼接，模拟生产环境行为

## 注意事项

- 修改此配置需要重启开发服务器
- 完整URL (以 `http://` 或 `https://` 开头) 不受此配置影响
- 确保CDN URL配置正确，避免图片无法加载