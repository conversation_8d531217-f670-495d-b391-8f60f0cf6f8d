'use client';

import { Home, MessageCircle, CreditCard, User } from 'lucide-react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';

interface BottomNavProps {
  className?: string;
}

export function BottomNav({ className = '' }: BottomNavProps) {
  const pathname = usePathname();

  const navItems = [
    { href: '/', icon: Home, label: 'Explore' },
    { href: '/chat', icon: MessageCircle, label: 'Chat' },
    { href: '/payment', icon: CreditCard, label: 'Premium' },
    { href: '/profile', icon: User, label: 'Me' }
  ];

  return (
    <nav className={`bg-[#120518] border-t border-[#3a1a44] ${className}`}>
      <div className="flex justify-around">
        {navItems.map(({ href, icon: Icon, label }) => {
          const isActive = pathname === href;
          return (
            <Link
              key={href}
              href={href}
              className={`flex flex-col items-center py-2 px-3 ${
                isActive ? 'text-pink-500' : 'text-gray-400'
              }`}
            >
              <Icon className="w-6 h-6" />
              <span className="text-xs mt-1">{label}</span>
            </Link>
          );
        })}
      </div>
    </nav>
  );
}