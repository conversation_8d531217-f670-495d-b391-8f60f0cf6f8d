import Link from 'next/link';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { getCharacterImageUrl } from '@/lib/image-utils';
import SmartImage from '@/components/SmartImage';

interface TrendingCharacter {
  id: number | string;
  name: string;
  occupation: string;
  rank?: number;
  imageSrc: string;
}

// 定义指定的hot角色数据
const hotCharacters = [
  {
    id: 118,
    name: '<PERSON>',
    occupation:
      'Seductive fitness trainer with a passion for pushing limits and exploring boundaries',
    rank: 1,
    imageSrc:
      'https://lumiloveaibucket.s3.ap-southeast-2.amazonaws.com/female/<PERSON>+<PERSON>/Tina_a.png',
  },
  {
    id: 119,
    name: '<PERSON><PERSON>',
    occupation:
      'Mysterious artist who finds beauty in the darkest corners of desire',
    rank: 2,
    imageSrc:
      'https://lumiloveaibucket.s3.ap-southeast-2.amazonaws.com/female/<PERSON><PERSON>+<PERSON>/<PERSON><PERSON>_a.png',
  },
  {
    id: 139,
    name: '<PERSON><PERSON><PERSON>',
    occupation:
      'Dominant CEO who takes control both in the boardroom and the bedroom',
    rank: 3,
    imageSrc:
      'https://lumiloveaibucket.s3.ap-southeast-2.amazonaws.com/female/Roxx+Noir/Roxx_a.png',
  },
  {
    id: 151,
    name: '<PERSON> <PERSON>',
    occupation:
      'Adventurous photographer capturing intimate moments and wild fantasies',
    rank: 4,
    imageSrc:
      'https://lumiloveaibucket.s3.ap-southeast-2.amazonaws.com/male/<PERSON>+<PERSON>/kai_a.png.png',
  },
  {
    id: 160,
    name: '<PERSON> Mangione',
    occupation:
      'Passionate Italian chef who knows how to satisfy all your appetites',
    rank: 5,
    imageSrc:
      'https://lumiloveaibucket.s3.ap-southeast-2.amazonaws.com/male/Luigi+Mangione/luigi_a.png.png',
  },
];

export default function TrendingList({
  characters,
}: {
  characters: TrendingCharacter[];
}) {
  return (
    <div className="bg-[#1a0a24] rounded-xl p-5 border border-[#3a1a44]">
      <Tabs defaultValue="hot">
        <TabsList className="grid grid-cols-2 mb-4 bg-[#2a1a34]">
          <TabsTrigger value="hot" className="data-[state=active]:bg-pink-500">
            🔥 Hot
          </TabsTrigger>
          <TabsTrigger
            value="trending"
            className="data-[state=active]:bg-pink-500"
          >
            🟣 Trending
          </TabsTrigger>
        </TabsList>
        <TabsContent value="hot" className="space-y-3 mt-0">
          {hotCharacters.map((character) => (
            <Link href={`/chat/${character.id}`} key={character.id}>
              <div className="flex items-center p-2 rounded-lg hover:bg-[#2a1a34] transition-colors">
                <div className="relative mr-3">
                  <div className="h-10 w-10 rounded-full overflow-hidden">
                    <SmartImage
                      src={character.imageSrc}
                      alt={character.name}
                      width={40}
                      height={40}
                      className="object-cover"
                    />
                  </div>
                  {character.rank && character.rank <= 3 && (
                    <div className="absolute -top-1 -right-1 bg-yellow-500 rounded-full h-4 w-4 flex items-center justify-center text-[10px] font-bold">
                      {character.rank}
                    </div>
                  )}
                </div>
                <div className="flex-1">
                  <h4 className="text-sm font-medium">{character.name}</h4>
                  <p className="text-xs text-gray-400">
                    {character.occupation}
                  </p>
                </div>
                <div className="text-sm text-gray-400">#{character.rank}</div>
              </div>
            </Link>
          ))}
        </TabsContent>
        <TabsContent value="trending" className="space-y-3 mt-0">
          {hotCharacters
            .slice()
            .reverse()
            .map((character, index) => (
              <Link href={`/chat/${character.id}`} key={character.id}>
                <div className="flex items-center p-2 rounded-lg hover:bg-[#2a1a34] transition-colors">
                  <div className="relative mr-3">
                    <div className="h-10 w-10 rounded-full overflow-hidden">
                      <SmartImage
                        src={character.imageSrc}
                        alt={character.name}
                        width={40}
                        height={40}
                        className="object-cover"
                      />
                    </div>
                    {index < 3 && (
                      <div className="absolute -top-1 -right-1 bg-purple-500 rounded-full h-4 w-4 flex items-center justify-center text-[10px] font-bold">
                        {index + 1}
                      </div>
                    )}
                  </div>
                  <div className="flex-1">
                    <h4 className="text-sm font-medium">{character.name}</h4>
                    <p className="text-xs text-gray-400">
                      {character.occupation}
                    </p>
                  </div>
                  <div className="text-sm text-gray-400">#{5 - index}</div>
                </div>
              </Link>
            ))}
        </TabsContent>
      </Tabs>
    </div>
  );
}
