import { NextRequest, NextResponse } from 'next/server';

// 使用Node.js runtime来支持https模块
export const runtime = 'nodejs';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { planId } = body;

    // 获取认证头
    const authorization = request.headers.get('authorization');
    if (!authorization) {
      return NextResponse.json({ error: '缺少认证信息' }, { status: 401 });
    }

    console.log('🔄 代理订阅请求:', { planId });

    // 临时禁用SSL验证（仅用于测试环境）
    process.env['NODE_TLS_REJECT_UNAUTHORIZED'] = '0';

    const response = await fetch(
      'https://api.loomelove.ai/api/plan/subscribe',
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-Title': 'Lumilove',
          Authorization: authorization,
        },
        body: JSON.stringify({ planId }),
        redirect: 'manual', // 不自动跟随重定向
      }
    );

    console.log('📊 后端响应状态:', response.status);
    console.log('📍 Location头:', response.headers.get('Location'));

    // 如果是重定向，返回重定向信息
    if (response.status >= 300 && response.status < 400) {
      const redirectUrl = response.headers.get('Location');

      if (redirectUrl) {
        return NextResponse.json({
          success: true,
          redirect: true,
          paymentUrl: redirectUrl,
          message: '需要跳转到支付页面',
        });
      } else {
        return NextResponse.json(
          {
            success: false,
            error: '服务器返回重定向但未提供跳转地址',
          },
          { status: 500 }
        );
      }
    }

    // 如果不是重定向，返回原始响应
    if (response.ok) {
      const contentType = response.headers.get('content-type');
      if (contentType?.includes('application/json')) {
        const data = await response.json();
        return NextResponse.json(data);
      } else {
        return NextResponse.json({
          success: true,
          message: '订阅成功',
        });
      }
    } else {
      return NextResponse.json(
        {
          success: false,
          error: `HTTP ${response.status}: ${response.statusText}`,
        },
        { status: response.status }
      );
    }
  } catch (error) {
    console.error('订阅代理错误:', error);
    return NextResponse.json(
      {
        success: false,
        error: '订阅请求失败',
      },
      { status: 500 }
    );
  }
}
