import { NextRequest, NextResponse } from 'next/server';

// 使用Node.js runtime来支持https模块
export const runtime = 'nodejs';

// Clinkbill API配置
const CLINKBILL_API_URL = 'https://api.clinkbill.dev/api/checkout/session';
const CLINKBILL_API_KEY = process.env.CLINKBILL_API_KEY || 'your_clinkbill_api_key_here'; // 👈 在这里配置您的Clinkbill API Key

// 套餐到Clinkbill产品的映射
const PLAN_TO_CLINKBILL_MAPPING = {
  1: { productId: 'prd_prime_monthly', priceId: 'price_prime_monthly' },     // Prime
  2: { productId: 'prd_plus_quarterly', priceId: 'price_plus_quarterly' },   // Plus  
  3: { productId: 'prd_pro_yearly', priceId: 'price_pro_yearly' },           // Pro
  4: { productId: 'prd_lifetime', priceId: 'price_lifetime' },               // Lifetime
};

interface ClinkbillRequest {
  planId: number;
  customerEmail?: string;
  customerId?: string;
  successUrl?: string;
  cancelUrl?: string;
}

interface ClinkbillResponse {
  code: number;
  msg: string;
  data: {
    sessionId: string;
    tokenValue: string;
    customerId: string;
    originalAmount: number;
    originalCurrency: string;
    url: string;
    merchantReferenceId: string;
    expireTime: string;
  };
}

export async function POST(request: NextRequest) {
  try {
    const body: ClinkbillRequest = await request.json();
    const { planId, customerEmail, customerId, successUrl, cancelUrl } = body;

    // 获取认证头（用于获取用户信息）
    const authorization = request.headers.get('authorization');
    if (!authorization) {
      return NextResponse.json({ error: '缺少认证信息' }, { status: 401 });
    }

    console.log('🔄 Clinkbill支付请求:', { planId });

    // 检查planId映射
    const clinkbillMapping = PLAN_TO_CLINKBILL_MAPPING[planId as keyof typeof PLAN_TO_CLINKBILL_MAPPING];
    if (!clinkbillMapping) {
      return NextResponse.json({ 
        error: `不支持的套餐ID: ${planId}` 
      }, { status: 400 });
    }

    // 从原有API获取套餐信息（价格等）
    let planInfo;
    try {
      const planResponse = await fetch('https://api.loomelove.ai/plan/list', {
        headers: {
          'Authorization': authorization,
          'X-Title': 'Lumilove',
        },
      });
      
      if (planResponse.ok) {
        const planData = await planResponse.json();
        planInfo = planData.data?.planList?.find((p: any) => p.id === planId);
      }
    } catch (error) {
      console.warn('获取套餐信息失败，使用默认价格:', error);
    }

    // 构建Clinkbill请求参数
    const timestamp = Date.now().toString();
    const clinkbillRequestBody = {
      originalAmount: planInfo?.price || getDefaultPrice(planId),
      originalCurrency: planInfo?.currency || 'USD',
      customerId: customerId,
      customerEmail: customerEmail || '<EMAIL>', // 如果没有提供邮箱，使用默认
      productId: clinkbillMapping.productId,
      priceId: clinkbillMapping.priceId,
      merchantReferenceId: `loomelove_plan_${planId}_${timestamp}`,
      successUrl: successUrl || `${getBaseUrl(request)}/payment-clinkbill?success=true`,
      cancelUrl: cancelUrl || `${getBaseUrl(request)}/payment-clinkbill?canceled=true`,
    };

    console.log('📤 发送到Clinkbill的请求:', clinkbillRequestBody);

    // 调用Clinkbill API
    const clinkbillResponse = await fetch(CLINKBILL_API_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-API-KEY': CLINKBILL_API_KEY,
        'X-Timestamp': timestamp,
      },
      body: JSON.stringify(clinkbillRequestBody),
    });

    console.log('📊 Clinkbill响应状态:', clinkbillResponse.status);

    if (!clinkbillResponse.ok) {
      const errorText = await clinkbillResponse.text();
      console.error('❌ Clinkbill API错误:', errorText);
      return NextResponse.json({
        success: false,
        error: `Clinkbill API错误: ${clinkbillResponse.status}`,
        details: errorText,
      }, { status: clinkbillResponse.status });
    }

    const clinkbillData: ClinkbillResponse = await clinkbillResponse.json();
    console.log('📄 Clinkbill响应数据:', clinkbillData);

    // 检查Clinkbill响应状态
    if (clinkbillData.code !== 200) {
      return NextResponse.json({
        success: false,
        error: `Clinkbill错误: ${clinkbillData.msg}`,
        code: clinkbillData.code,
      }, { status: 400 });
    }

    // 返回标准化的响应格式，兼容现有前端
    return NextResponse.json({
      success: true,
      redirect: true,
      paymentUrl: clinkbillData.data.url,
      message: '支付会话创建成功',
      data: {
        sessionId: clinkbillData.data.sessionId,
        tokenValue: clinkbillData.data.tokenValue,
        customerId: clinkbillData.data.customerId,
        originalAmount: clinkbillData.data.originalAmount,
        originalCurrency: clinkbillData.data.originalCurrency,
        merchantReferenceId: clinkbillData.data.merchantReferenceId,
        expireTime: clinkbillData.data.expireTime,
      },
    });

  } catch (error) {
    console.error('💥 Clinkbill代理错误:', error);
    return NextResponse.json({
      success: false,
      error: 'Clinkbill支付请求失败',
      details: error instanceof Error ? error.message : String(error),
    }, { status: 500 });
  }
}

// 辅助函数：获取默认价格
function getDefaultPrice(planId: number): number {
  const defaultPrices = {
    1: 12.99,  // Prime
    2: 8.99,   // Plus
    3: 5.99,   // Pro
    4: 199.99, // Lifetime
  };
  return defaultPrices[planId as keyof typeof defaultPrices] || 9.99;
}

// 辅助函数：获取基础URL
function getBaseUrl(request: NextRequest): string {
  const protocol = request.headers.get('x-forwarded-proto') || 'https';
  const host = request.headers.get('host') || 'localhost:3000';
  return `${protocol}://${host}`;
}