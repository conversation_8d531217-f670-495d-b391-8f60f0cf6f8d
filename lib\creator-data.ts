// 创作者数据管理
// 实现假UGC创作者系统，随机分配真实角色给创作者

// 创作者配置接口
interface CreatorConfig {
  id: string;
  name: string;
  avatarSrc: string;
  bannerSrc: string;
  followers: string;
  following: string;
  interactions: string;
  description: string;
}

// 创作者数据接口
interface CreatorData {
  id: string;
  name: string;
  avatarSrc: string;
  bannerSrc: string;
  followers: string;
  following: string;
  interactions: string;
  description: string;
  characters: any[]; // 动态获取的角色列表
}

// 静态创作者配置
const creatorConfigs: Record<string, CreatorConfig> = {
  'mei-chan': {
    id: 'mei-chan',
    name: 'MeiLetters',
    avatarSrc: '/avatar/mei.png',
    bannerSrc: '/placeholder.svg?height=300&width=1200',
    followers: '18503',
    following: '195',
    interactions: '89.7M',
    description: 'Pretty hands. Useless apologies. Long games.',
  },
  starlight: {
    id: 'starlight',
    name: 'shy.starlight',
    avatarSrc: '/avatar/starlight.png',
    bannerSrc: '/placeholder.svg?height=300&width=1200',
    followers: '22456',
    following: '167',
    interactions: '156.2M',
    description:
      '(｡•́‿•̀｡)♡ sometimes i write you soft, sometimes i break your heart.',
  },
  shadowweaver: {
    id: 'shadowweaver',
    name: '0xShadowWvr',
    avatarSrc: '/avatar/shadow.png',
    bannerSrc: '/placeholder.svg?height=300&width=1200',
    followers: '15678',
    following: '89',
    interactions: '78.9M',
    description: "Don't DM me unless you've kissed your own villain.",
  },
  goldenheart: {
    id: 'goldenheart',
    name: 'G0LDH34RT',
    avatarSrc: '/avatar/golden.png',
    bannerSrc: '/placeholder.svg?height=300&width=1200',
    followers: '19876',
    following: '234',
    interactions: '112.4M',
    description: 'too pretty to be pure. too nice to be safe.',
  },
  nightstalker: {
    id: 'nightstalker',
    name: 'n1ght.slkr',
    avatarSrc: '/avatar/night.png',
    bannerSrc: '/placeholder.svg?height=300&width=1200',
    followers: '13456',
    following: '123',
    interactions: '67.3M',
    description: "quiet boys who bite back. that's it.",
  },
  roadrebel: {
    id: 'roadrebel',
    name: '✨ rebel.run.69',
    avatarSrc: '/avatar/rebel.png',
    bannerSrc: '/placeholder.svg?height=300&width=1200',
    followers: '18765',
    following: '156',
    interactions: '94.8M',
    description: 'Left the plot 3 scenes ago. Still hot tho 😈',
  },
  alexmaster: {
    id: 'alexmaster',
    name: 'a.ctrl.deluxe',
    avatarSrc: '/avatar/alex.png',
    bannerSrc: '/placeholder.svg?height=300&width=1200',
    followers: '21234',
    following: '189',
    interactions: '128.5M',
    description: 'System override: dirty thoughts.exe has launched.',
  },
  thunderbolt: {
    id: 'thunderbolt',
    name: 'thun⚡twist',
    avatarSrc: '/avatar/thunder.png',
    bannerSrc: '/placeholder.svg?height=300&width=1200',
    followers: '16789',
    following: '145',
    interactions: '89.2M',
    description: "Sugar highs. Filthy lows. You'll come back anyway.",
  },
};

// 角色到创作者的随机分配映射
// 改进的分配逻辑，确保所有角色都有创作者
const characterCreatorMapping: Record<string, string> = {};

// 初始化角色分配映射
function initializeCharacterMapping() {
  const creatorIds = Object.keys(creatorConfigs);

  // 为可能的角色ID范围创建随机分配
  // 扩大范围到1000，确保覆盖所有可能的角色ID
  for (let i = 1; i <= 1000; i++) {
    const randomIndex = Math.floor(
      Math.abs(Math.sin(i * 1000)) * creatorIds.length
    );
    characterCreatorMapping[i.toString()] = creatorIds[randomIndex];
  }

  // 为字符串ID也创建分配（以防角色ID是字符串）
  for (let i = 1; i <= 1000; i++) {
    const randomIndex = Math.floor(
      Math.abs(Math.sin(i * 1000)) * creatorIds.length
    );
    characterCreatorMapping[`char_${i}`] = creatorIds[randomIndex];
  }

  console.log(
    '创作者分配映射已初始化:',
    Object.keys(characterCreatorMapping).length,
    '个角色ID'
  );
}

// 初始化映射
initializeCharacterMapping();

// 获取动态创作者数据
export function getDynamicCreatorData(creatorId: string): CreatorData | null {
  const config = creatorConfigs[creatorId];
  if (!config) {
    console.warn(`未找到创作者配置: ${creatorId}`);
    return null;
  }

  return {
    ...config,
    characters: [], // 角色列表将通过API动态获取
  };
}

// 获取所有创作者ID
export function getAllCreatorIds(): string[] {
  return Object.keys(creatorConfigs);
}

// 检查创作者是否存在
export function creatorExists(creatorId: string): boolean {
  return creatorId in creatorConfigs;
}

// 根据角色ID获取对应的创作者ID
export function getCreatorForCharacter(
  characterId: string | number
): string | null {
  const id = characterId.toString();
  const creatorId = characterCreatorMapping[id];

  if (creatorId) {
    // console.log(`角色 ${id} 分配给创作者: ${creatorId}`);
    return creatorId;
  }

  // 如果没有找到映射，使用哈希算法生成一个
  const creatorIds = Object.keys(creatorConfigs);
  const hash = id.split('').reduce((a, b) => {
    a = (a << 5) - a + b.charCodeAt(0);
    return a & a;
  }, 0);
  const fallbackCreatorId = creatorIds[Math.abs(hash) % creatorIds.length];

  // console.log(`角色 ${id} 使用哈希分配创作者: ${fallbackCreatorId}`);
  return fallbackCreatorId;
}

// 获取创作者的统计信息
export function getCreatorStats(creatorId: string) {
  const config = creatorConfigs[creatorId];
  if (!config) return null;

  return {
    followers: config.followers,
    following: config.following,
    interactions: config.interactions,
  };
}

// 获取创作者的描述
export function getCreatorDescription(creatorId: string) {
  const config = creatorConfigs[creatorId];
  return config?.description || '';
}

// 获取创作者的名称
export function getCreatorName(creatorId: string) {
  const config = creatorConfigs[creatorId];
  return config?.name || creatorId;
}

// 获取创作者的头像
export function getCreatorAvatar(creatorId: string) {
  const config = creatorConfigs[creatorId];
  return config?.avatarSrc || '/avatar/default_avatar.png';
}

// 获取创作者的横幅
export function getCreatorBanner(creatorId: string) {
  const config = creatorConfigs[creatorId];
  return config?.bannerSrc || '/placeholder.svg?height=300&width=1200';
}
