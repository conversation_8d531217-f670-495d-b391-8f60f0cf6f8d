import { useRef, useCallback } from 'react';
import { getAudioUrl } from '@/lib/image-utils';
import type { Message } from './useMessages';

export interface UseAudioPlayerProps {
  messages: Message[];
  updateMessage: (id: number, updates: Partial<Message>) => void;
  updateUiState: (updates: any) => void;
  isPlaying: number | null;
}

export const useAudioPlayer = ({
  messages, 
  updateMessage, 
  updateUiState, 
  isPlaying
}: UseAudioPlayerProps) => {
  const audioRef = useRef<HTMLAudioElement | null>(null);

  // 🕐 处理音频时长更新
  const handleAudioDurationUpdate = useCallback((messageId: number, duration: number) => {
    console.log('⏰ 更新音频时长:', { messageId, duration });
    updateMessage(messageId, { audioDuration: duration });
  }, [updateMessage]);

  // 🎵 音频播放处理
  const handleAudioPlay = useCallback((messageId: number, audioElement?: HTMLAudioElement) => {
    console.log('🎵 handleAudioPlay 被调用:', { messageId, hasAudioElement: !!audioElement });
    
    // 如果当前正在播放同一个音频消息，则暂停
    if (isPlaying === messageId && audioRef.current) {
      console.log('😄 暂停当前音频');
      audioRef.current.pause();
      updateUiState({ isPlaying: null });
      return;
    }
    
    // 如果有其他音频正在播放，先停止
    if (audioRef.current && !audioRef.current.paused) {
      console.log('🛑 停止其他音频');
      audioRef.current.pause();
    }
    
    // 如果没有提供音频元素，尝试从消息中获取
    if (!audioElement) {
      const message = messages.find((m) => m.id === messageId);
      if (message?.audioSrc) {
        audioElement = new Audio();
        audioElement.src = getAudioUrl(message.audioSrc);
        console.log('🎵 从消息中创建音频元素:', audioElement.src);
      } else {
        console.warn('⚠️ 找不到音频源，使用模拟播放');
        // fallback 到模拟播放
        updateUiState({ isPlaying: messageId });
        const message = messages.find((m) => m.id === messageId);
        if (message && message.audioDuration) {
          setTimeout(() => {
            updateUiState({ isPlaying: null });
          }, message.audioDuration * 1000);
        }
        return;
      }
    }
    
    if (!audioElement) {
      console.error('❌ 无法获取音频元素');
      return;
    }
    
    // 保存引用
    audioRef.current = audioElement;
    
    // 设置事件监听器
    audioElement.onloadstart = () => {
      console.log('📁 音频开始加载');
    };
    
    audioElement.oncanplay = () => {
      console.log('✅ 音频可以播放，时长:', audioElement.duration, '秒');
      
      // 🎯 获取真实音频时长并更新消息
      if (audioElement.duration && !isNaN(audioElement.duration) && isFinite(audioElement.duration)) {
        const realDuration = Math.ceil(audioElement.duration); // 向上取整到秒
        console.log('🕐 更新消息的真实音频时长:', realDuration, '秒');
        
        // 更新对应消息的audioDuration
        updateMessage(messageId, { 
          audioDuration: realDuration 
        });
      }
    };
    
    audioElement.onplay = () => {
      console.log('▶️ 音频开始播放');
      updateUiState({ isPlaying: messageId });
    };
    
    audioElement.onpause = () => {
      console.log('⏸️ 音频暂停');
      updateUiState({ isPlaying: null });
    };
    
    audioElement.onended = () => {
      console.log('🏁 音频播放结束');
      updateUiState({ isPlaying: null });
      audioRef.current = null;
    };
    
    audioElement.onerror = (error) => {
      console.error('❌ 音频播放出错:', error, '音频URL:', audioElement.src);
      updateUiState({ isPlaying: null });
      audioRef.current = null;
    };
    
    // 开始播放
    console.log('🚀 尝试播放音频:', audioElement.src);
    audioElement.play().catch((error) => {
      console.error('❌ 播放失败:', error);
      updateUiState({ isPlaying: null });
      audioRef.current = null;
    });
  }, [messages, updateMessage, updateUiState, isPlaying]);

  return {
    handleAudioPlay,
    handleAudioDurationUpdate,
    audioRef
  };
};