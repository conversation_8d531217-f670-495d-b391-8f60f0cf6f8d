"use client";

import { Check, Crown } from "lucide-react";
import { cn } from "@/lib/utils";
import { IMAGE_COUNT_OPTIONS } from "@/lib/imageGeneration/config";

interface ImageCountSelectorProps {
  selectedCount: number;
  onSelectCount: (count: number) => void;
  className?: string;
}

export default function ImageCountSelector({
  selectedCount,
  onSelectCount,
  className = "",
}: ImageCountSelectorProps) {
  return (
    <div className={cn("space-y-4", className)}>
      <h3 className="text-lg font-semibold">Number of Images</h3>
      <div className="grid grid-cols-3 gap-3">
        {IMAGE_COUNT_OPTIONS.map((option) => (
          <div
            key={option.value}
            className={cn(
              "relative cursor-pointer rounded-lg border-2 p-4 text-center transition-all hover:scale-105",
              selectedCount === option.value
                ? "border-pink-500 bg-pink-500/10"
                : "border-[#3a1a44] bg-[#1a0a24] hover:border-pink-400",
              option.isPremium && "border-amber-500/50 bg-amber-500/5"
            )}
            onClick={() => onSelectCount(option.value)}
          >
            {/* 选中状态指示器 */}
            {selectedCount === option.value && (
              <div className="absolute -top-2 -right-2 z-10 h-6 w-6 rounded-full bg-pink-500 flex items-center justify-center">
                <Check className="h-4 w-4 text-white" />
              </div>
            )}
            
            {/* Premium标识 */}
            {option.isPremium && (
              <div className="absolute -top-2 -left-2 z-10 h-6 w-6 rounded-full bg-amber-500 flex items-center justify-center">
                <Crown className="h-4 w-4 text-white" />
              </div>
            )}
            
            {/* 主要内容 */}
            <div className="space-y-2">
              <div className="text-2xl font-bold text-pink-400">
                {option.value}
              </div>
              <div className="text-sm font-medium">
                {option.label}
              </div>
              {option.isPremium && (
                <div className="text-xs text-amber-400">
                  Premium
                </div>
              )}
            </div>
          </div>
        ))}
      </div>
      
      {/* Premium提示 */}
      <div className="text-xs text-gray-400 bg-[#1a0a24] p-3 rounded-lg border border-[#3a1a44]">
        <p className="flex items-center">
          <Crown className="h-4 w-4 mr-2 text-amber-400" />
          Premium features require subscription
        </p>
      </div>
    </div>
  );
}