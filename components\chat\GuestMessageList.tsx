"use client";

import { useState } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { getAvatarUrl } from '@/lib/image-utils';
import type { Message } from '@/hooks/useMessages';

interface Character {
  id: string;
  name: string;
  avatarSrc: string;
}

interface GuestMessageListProps {
  sampleMessages: Message[];
  character: Character;
}

export function GuestMessageList({ sampleMessages, character }: GuestMessageListProps) {
  return (
    <div className="flex-1 overflow-y-auto relative">
      {/* 只显示角色开场白 */}
      <div className="p-3 sm:p-4 space-y-3 sm:space-y-4 relative">
        {/* 显示角色开场白消息 */}
        {sampleMessages.map((message, index) => (
          <div
            key={index}
            className="flex items-start gap-2 sm:gap-3"
          >
            {/* 角色头像 */}
            <div className="flex-shrink-0">
              <div className="w-8 h-8 sm:w-10 sm:h-10 rounded-full overflow-hidden">
                <Image
                  src={getAvatarUrl(character.avatarSrc)}
                  alt={character.name}
                  width={40}
                  height={40}
                  className="w-full h-full object-cover"
                />
              </div>
            </div>

            {/* 消息气泡 */}
            <div className="max-w-[85%] sm:max-w-[80%] px-3 py-2 sm:px-4 sm:py-3 rounded-2xl bg-[#1a0a24] border border-[#3a1a44] text-gray-100">
              <p className="text-sm sm:text-base break-words">
                {message.text}
              </p>
              
              {/* 时间戳 */}
              <div className="mt-1 text-xs text-gray-400">
                Just now
              </div>
            </div>
          </div>
        ))}

        {/* 登录提示卡片 */}
        <div className="mt-8 mx-2 sm:mx-0">
          <div className="bg-gradient-to-r from-purple-500/20 to-pink-500/20 backdrop-blur-sm rounded-xl p-4 sm:p-6 text-center border border-purple-500/30">
            <div className="mb-4">
              <div className="text-2xl mb-2">💬</div>
              <h3 className="text-white font-medium mb-2">
                Ready to chat with {character.name}?
              </h3>
              <p className="text-purple-200 text-sm mb-4">
                Sign in to start unlimited conversations and save your chat history
              </p>
            </div>

            <div className="flex gap-3 justify-center">
              <Button variant="outline" size="sm" asChild>
                <Link href="/login">Sign In</Link>
              </Button>
              <Button variant="default" size="sm" asChild>
                <Link href="/register">Sign Up Free</Link>
              </Button>
            </div>

            <div className="mt-3 pt-3 border-t border-purple-500/20">
              <p className="text-xs text-gray-400">
                Join thousands of users enjoying unlimited AI conversations
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default GuestMessageList;