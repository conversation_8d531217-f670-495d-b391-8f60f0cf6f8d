"use client";

import { useRef } from "react";
import { Dice5, Upload } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import Sidebar from "@/components/sidebar";
import { Dialog, DialogContent } from "@/components/ui/dialog";
import UserPlanHeader from "@/components/UserPlanHeader";

// 引入新的组件和hooks
import { useImageGeneration } from "@/hooks/useImageGeneration";
import CharacterSelector from "@/components/imageGeneration/CharacterSelector";
import OptionSelector from "@/components/imageGeneration/OptionSelector";
import ImageCountSelector from "@/components/imageGeneration/ImageCountSelector";
import GenerationResults from "@/components/imageGeneration/GenerationResults";
import {
  SCENE_OPTIONS,
  POSE_OPTIONS,
  ANGLE_OPTIONS,
  STYLE_OPTIONS,
} from "@/lib/imageGeneration/config";

export default function CreatePage() {
  const fileInputRef = useRef<HTMLInputElement>(null);

  // 使用统一的状态管理hook
  const {
    selected<PERSON>haracter,
    selectedGender,
    selectedStyle,
    selectedScene,
    selectedClothing,
    selectedPose,
    selectedAngle,
    imageCount,
    isGenerating,
    generatedImages,
    uploadedImage,
    openUploadDialog,
    canGenerate,
    currentClothingOptions,
    selectCharacter,
    selectGender,
    selectStyle,
    uploadImage,
    selectScene,
    selectClothing,
    selectPose,
    selectAngle,
    setImageCount,
    randomizeAll,
    generateImages,
    clearGeneratedImages,
    toggleUploadDialog,
  } = useImageGeneration();

  // 处理图片生成
  const handleGenerate = async () => {
    try {
      await generateImages();
    } catch (error) {
      alert(error instanceof Error ? error.message : "生成失败，请重试");
    }
  };

  // 处理图片上传
  const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        if (e.target?.result) {
          uploadImage(e.target.result as string);
          toggleUploadDialog(false);
        }
      };
      reader.readAsDataURL(file);
    }
  };

  const handleUploadClick = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  return (
    <div className="flex min-h-screen bg-[#0e0314]">
      <Sidebar />

      <main className="flex-1 p-6">
        <div className="max-w-6xl mx-auto space-y-8">
          {/* 页面Header */}
          <div className="flex justify-between items-center">
            <div>
              <h1 className="text-3xl font-bold text-white mb-2">
                AI Image Generator
              </h1>
              <p className="text-gray-400">
                Create amazing AI-generated images with our advanced tool
              </p>
            </div>
            <UserPlanHeader />
          </div>

          {/* 角色选择区域 */}
          <div className="bg-[#1a0a24] rounded-xl p-6 border border-[#3a1a44]">
            <CharacterSelector
              selectedCharacter={selectedCharacter}
              selectedGender={selectedGender}
              uploadedImage={uploadedImage}
              onSelectCharacter={selectCharacter}
              onSelectGender={selectGender}
              onUploadImage={uploadImage}
              onOpenUpload={() => toggleUploadDialog(true)}
            />
          </div>

          {/* 风格选择 */}
          <div className="bg-[#1a0a24] rounded-xl p-6 border border-[#3a1a44]">
            <h3 className="text-lg font-semibold mb-4">Style</h3>
            <div className="flex space-x-4">
              {STYLE_OPTIONS.map((style) => (
                <button
                  key={style.value}
                  className={`px-6 py-3 rounded-lg border-2 transition-all ${
                    selectedStyle === style.value
                      ? "border-pink-500 bg-pink-500/10 text-pink-400"
                      : "border-[#3a1a44] bg-[#2a1a34] text-gray-300 hover:border-pink-400"
                  }`}
                  onClick={() => selectStyle(style.value as "realistic" | "anime")}
                >
                  {style.label}
                </button>
              ))}
            </div>
          </div>

          {/* 生成参数区域 */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* 左列 */}
            <div className="space-y-6">
              {/* 场景选择 */}
              <div className="bg-[#1a0a24] rounded-xl p-6 border border-[#3a1a44]">
                <OptionSelector
                  title="Scene"
                  options={SCENE_OPTIONS}
                  selectedOption={selectedScene}
                  onSelect={selectScene}
                  columns={3}
                />
              </div>

              {/* 服装选择 */}
              <div className="bg-[#1a0a24] rounded-xl p-6 border border-[#3a1a44]">
                <OptionSelector
                  title="Clothing"
                  options={currentClothingOptions}
                  selectedOption={selectedClothing}
                  onSelect={selectClothing}
                  columns={3}
                />
              </div>
            </div>

            {/* 右列 */}
            <div className="space-y-6">
              {/* 姿势选择 */}
              <div className="bg-[#1a0a24] rounded-xl p-6 border border-[#3a1a44]">
                <OptionSelector
                  title="Pose"
                  options={POSE_OPTIONS}
                  selectedOption={selectedPose}
                  onSelect={selectPose}
                  columns={3}
                />
              </div>

              {/* 角度选择 */}
              <div className="bg-[#1a0a24] rounded-xl p-6 border border-[#3a1a44]">
                <OptionSelector
                  title="Angle"
                  options={ANGLE_OPTIONS}
                  selectedOption={selectedAngle}
                  onSelect={selectAngle}
                  columns={3}
                />
              </div>
            </div>
          </div>

          {/* 图片数量选择 */}
          <div className="bg-[#1a0a24] rounded-xl p-6 border border-[#3a1a44]">
            <ImageCountSelector
              selectedCount={imageCount}
              onSelectCount={setImageCount}
            />
          </div>

          {/* 操作按钮 */}
          <div className="flex justify-center space-x-4">
            <Button
              size="lg"
              variant="outline"
              onClick={randomizeAll}
              className="bg-[#1a0a24] border-[#3a1a44] hover:border-pink-400"
            >
              <Dice5 className="h-5 w-5 mr-2" />
              Random
            </Button>
            <Button
              size="lg"
              onClick={handleGenerate}
              disabled={!canGenerate || isGenerating}
              className="bg-pink-500 hover:bg-pink-600 text-white px-8"
            >
              {isGenerating ? "Generating..." : "Generate Images"}
            </Button>
          </div>

          {/* 生成结果 */}
          <GenerationResults
            images={generatedImages}
            isGenerating={isGenerating}
            onClear={clearGeneratedImages}
          />
        </div>
      </main>

      {/* 隐藏的文件输入 */}
      <input
        type="file"
        ref={fileInputRef}
        onChange={handleFileUpload}
        accept="image/*"
        className="hidden"
      />

      {/* 上传对话框 */}
      <Dialog open={openUploadDialog} onOpenChange={toggleUploadDialog}>
        <DialogContent className="bg-[#1a0a24] border-[#3a1a44] max-w-md">
          <div className="flex flex-col items-center p-6">
            <div
              className="w-40 h-40 rounded-lg border-2 border-dashed border-pink-500 flex items-center justify-center mb-6 cursor-pointer hover:bg-[#3a1a44] transition-colors"
              onClick={handleUploadClick}
            >
              {uploadedImage ? (
                <div className="relative w-full h-full">
                  <img
                    src={uploadedImage}
                    alt="Uploaded"
                    className="w-full h-full object-cover rounded-lg"
                  />
                </div>
              ) : (
                <div className="flex flex-col items-center">
                  <div className="bg-pink-500 rounded-full p-4 mb-3">
                    <Upload className="h-8 w-8 text-white" />
                  </div>
                  <span className="text-base font-medium text-white">
                    Upload Image
                  </span>
                  <span className="text-xs text-gray-400 mt-1">
                    Click to browse files
                  </span>
                </div>
              )}
            </div>

            <div className="w-full">
              <h3 className="text-white text-lg mb-3">
                Style Preference
              </h3>
              <div className="grid grid-cols-2 gap-3">
                {STYLE_OPTIONS.map((style) => (
                  <button
                    key={style.value}
                    className={`px-4 py-3 rounded-lg border transition-all ${
                      selectedStyle === style.value
                        ? "border-pink-500 bg-pink-500/10 text-pink-400"
                        : "border-[#3a1a44] bg-[#2a1a34] text-gray-300"
                    }`}
                    onClick={() => selectStyle(style.value as "realistic" | "anime")}
                  >
                    {style.label}
                  </button>
                ))}
              </div>
            </div>

            <div className="flex justify-between w-full mt-6 space-x-3">
              <Button
                variant="outline"
                className="flex-1 border-[#3a1a44] text-white hover:bg-[#3a1a44]"
                onClick={() => toggleUploadDialog(false)}
              >
                Cancel
              </Button>
              <Button
                className="flex-1 bg-pink-500 hover:bg-pink-600"
                onClick={() => toggleUploadDialog(false)}
                disabled={!uploadedImage}
              >
                Confirm
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}