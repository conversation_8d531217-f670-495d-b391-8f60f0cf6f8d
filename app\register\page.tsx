"use client"

import type React from "react"
import { useState } from "react"
import Link from "next/link"
import { useRouter } from "next/navigation"
import { Button } from "@/components/ui/button"
import { Heart, X } from "lucide-react"
import Image from "next/image"

export default function RegisterPage() {
  const router = useRouter()
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState("")


  const handleGoogleSignup = () => {
    setLoading(true)
    setError("")
    
    try {
      // 直接跳转到后端Google OAuth端点（注册和登录使用相同流程）
      const apiUrl = process.env.NEXT_PUBLIC_API_URL || 'https://api.loomelove.ai'
      window.location.href = `${apiUrl}/api/auth/google`
    } catch (error) {
      console.error("Google signup error:", error)
      setError("Failed to initiate Google signup")
      setLoading(false)
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-[#0a0a0f] via-[#1a0a24] to-[#2a1a34] flex items-center justify-center p-3 sm:p-4">
      {/* Background decoration */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-pink-500/10 rounded-full blur-3xl"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-purple-500/10 rounded-full blur-3xl"></div>
      </div>

      {/* 统一的卡片容器 */}
      <div className="relative w-full max-w-md sm:max-w-2xl md:max-w-4xl lg:max-w-5xl mx-auto">
        {/* 退出按钮 */}
        <button
          onClick={() => router.push('/')}
          className="absolute -top-2 -right-2 sm:top-2 sm:right-2 z-10 w-7 h-7 sm:w-10 sm:h-10 bg-black/20 hover:bg-black/40 backdrop-blur-sm border border-white/20 rounded-full flex items-center justify-center transition-all duration-200 group"
        >
          <X className="w-3 h-3 sm:w-5 sm:h-5 text-white group-hover:text-pink-300" />
        </button>

        <div className="bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-xl border border-white/20 rounded-lg sm:rounded-xl md:rounded-2xl lg:rounded-3xl shadow-2xl overflow-hidden">
          <div className="flex flex-row">
            {/* Left side - Image */}
            <div className="w-2/5 sm:w-2/5 md:w-1/2">
              <div className="relative h-full min-h-[450px] sm:min-h-[500px] md:min-h-[600px]">
                <Image
                  src="/login-left-image.jpg"
                  alt="Register visual"
                  width={500}
                  height={600}
                  className="w-full h-full object-cover"
                  priority
                />
                {/* Image overlay for better visual appeal */}
                <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"></div>
              </div>
            </div>

            {/* Right side - Register form */}
            <div className="w-3/5 sm:w-3/5 md:w-1/2 p-4 sm:p-6 md:p-8 lg:p-10">
              {/* Form content container */}
              <div className="h-full flex flex-col justify-center">
                {/* Header */}
                <div className="text-center mb-4 sm:mb-6 lg:mb-8">
                  <div className="flex justify-center mb-3 sm:mb-4 lg:mb-6">
                    <div className="relative">
                      <div className="h-12 w-12 sm:h-16 sm:w-16 lg:h-20 lg:w-20 rounded-full p-1 bg-gradient-to-r from-pink-500 via-purple-500 to-pink-500 animate-pulse">
                        <div className="h-full w-full rounded-full bg-gradient-to-br from-[#1a0a24] to-[#2a1a34] flex items-center justify-center">
                          <Heart className="h-6 w-6 sm:h-8 sm:w-8 lg:h-10 lg:w-10 text-white drop-shadow-lg" fill="currentColor" />
                        </div>
                      </div>
                      <div className="absolute -inset-2 bg-gradient-to-r from-pink-500/20 to-purple-500/20 rounded-full blur-lg -z-10"></div>
                    </div>
                  </div>
                  <h1 className="text-2xl sm:text-3xl lg:text-4xl font-bold bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent mb-2 sm:mb-3 lg:mb-4">
                    Create Account
                  </h1>
                  <p className="text-gray-400 text-sm sm:text-base lg:text-lg">Sign up with your Google account</p>
                </div>

                {/* Error message */}
                {error && (
                  <div className="mb-4 sm:mb-6 p-3 sm:p-4 bg-gradient-to-r from-red-500/10 to-red-600/10 border border-red-500/30 rounded-xl backdrop-blur-sm">
                    <div className="flex items-center space-x-2">
                      <div className="w-2 h-2 bg-red-500 rounded-full animate-pulse"></div>
                      <span className="text-red-300 text-sm font-medium">{error}</span>
                    </div>
                  </div>
                )}

                {/* Google 注册按钮 */}
                <div className="space-y-3 sm:space-y-4">
                  <Button
                    type="button"
                    onClick={handleGoogleSignup}
                    disabled={loading}
                    className="w-full h-11 sm:h-12 lg:h-14 bg-white hover:bg-gray-50 text-gray-900 font-semibold rounded-lg sm:rounded-xl border border-gray-300 shadow-sm hover:shadow-md transition-all duration-200 flex items-center justify-center space-x-2 sm:space-x-3 disabled:opacity-50 disabled:cursor-not-allowed text-sm sm:text-base lg:text-lg"
                  >
                    {loading ? (
                      <div className="flex items-center space-x-2">
                        <div className="w-4 h-4 sm:w-5 sm:h-5 border-2 border-gray-400/30 border-t-gray-600 rounded-full animate-spin"></div>
                        <span className="text-sm sm:text-base lg:text-lg">Connecting to Google...</span>
                      </div>
                    ) : (
                      <>
                        <svg className="h-5 w-5 sm:h-6 sm:w-6" viewBox="0 0 24 24">
                          <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                          <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                          <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                          <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                        </svg>
                        <span>Sign up with Google</span>
                      </>
                    )}
                  </Button>
                </div>

                {/* Login link */}
                <div className="text-center">
                  <p className="text-gray-400 text-sm sm:text-base">
                    Already have an account?{" "}
                    <Link
                      href="/login"
                      className="text-pink-400 hover:text-pink-300 transition-colors font-medium"
                    >
                      Sign in now
                    </Link>
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Additional info */}
        <div className="mt-4 sm:mt-6 text-center">
          <div className="flex items-center justify-center space-x-3 sm:space-x-4 text-sm sm:text-base text-gray-500">
            <Link href="/terms" className="hover:text-gray-400 transition-colors">
              Terms of Service
            </Link>
            <span>•</span>
            <Link href="/privacy" className="hover:text-gray-400 transition-colors">
              Privacy Policy
            </Link>
            <span>•</span>
            <Link href="/help" className="hover:text-gray-400 transition-colors">
              Help
            </Link>
          </div>
        </div>
      </div>
    </div>
  )
}