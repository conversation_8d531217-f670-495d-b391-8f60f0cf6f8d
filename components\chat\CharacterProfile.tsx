'use client';

import Image from 'next/image';
import { Heart, Share2, Lock } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Ta<PERSON>, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';
import { getCharacterImageUrl, getAvatarUrl } from '@/lib/image-utils';
import { getFakeStatsForCharacter } from '@/lib/fake-stats';

interface CharacterProfileProps {
  character: {
    name: string;
    occupation: string;
    age: string;
    description: string;
    followers: string;
    tags?: string[];
    images?: string[];
    avatarSrc: string;
    imageSrc?: string;
    resource?: {
      opening?: string;
      profile_image?: string[];
      image?: Array<{
        description: string;
        url: string;
      }>;
      image_urls?: string[];
    };
  };
  characterLoading: boolean;
  isFollowing: boolean;
  isFollowLoading?: boolean;
  isFavorite: boolean;
  userSubscriptionLevel?: 'Free' | 'Lite' | 'Basic' | 'Premium';
  showProfile: boolean;
  onFollowToggle: () => void;
  onFavoriteToggle: () => void;
  onShareCharacter: () => void;
}

export default function CharacterProfile({
  character,
  characterLoading,
  isFollowing,
  isFollowLoading = false,
  isFavorite,
  userSubscriptionLevel = 'Free',
  showProfile,
  onFollowToggle,
  onFavoriteToggle,
  onShareCharacter,
}: CharacterProfileProps) {
  // 🎯 统一图片数据处理逻辑 - 以接口数据为主
  const getCharacterImages = () => {
    const allImages: string[] = [];

    // 1. 优先使用接口返回的resource.profile_image（专门用于个人信息卡片的照片）
    if (character.resource?.profile_image?.length) {
      allImages.push(...character.resource.profile_image);
      // console.log('🖼️ 使用profile_image字段:', character.resource.profile_image);
    } else if (character.resource) {
      // 2. 如果没有profile_image，使用其他resource数据
      // console.log('⚠️ 没有profile_image，使用其他resource数据');

      // image 资源数组的URL（自拍图片）
      if (character.resource.image?.length) {
        allImages.push(...character.resource.image.map((img) => img.url));
      }

      // image_urls 额外图片URL
      if (character.resource.image_urls?.length) {
        allImages.push(...character.resource.image_urls);
      }
    }

    // 3. 如果接口resource数据为空，fallback到原有字段
    if (allImages.length === 0) {
      console.log('⚠️ resource数据为空，使用fallback字段');

      // 优先使用主图片
      if (character.imageSrc) {
        allImages.push(character.imageSrc);
      }

      // 然后使用images数组
      if (character.images?.length) {
        allImages.push(...character.images);
      }

      // 最后使用头像作为备选
      if (character.avatarSrc && allImages.length === 0) {
        allImages.push(character.avatarSrc);
      }
    }

    // 4. 去重处理并过滤无效URL
    const uniqueImages = Array.from(new Set(allImages.filter(Boolean)));
    // console.log('📸 最终gallery图片列表:', uniqueImages);
    return uniqueImages;
  };

  // 获取主头像URL - 用于顶部圆形头像显示
  const getMainAvatarUrl = () => {
    // 优先使用专门的头像
    if (character.avatarSrc) {
      return getAvatarUrl(character.avatarSrc);
    }

    // fallback到主图片
    if (character.imageSrc) {
      return getCharacterImageUrl(character.imageSrc);
    }

    // fallback到第一张gallery图片
    const images = getCharacterImages();
    if (images.length > 0) {
      return getCharacterImageUrl(images[0]);
    }

    return '/placeholder.svg';
  };

  // 检查用户是否有Premium权限
  const isPremiumUser =
    userSubscriptionLevel === 'Premium' || userSubscriptionLevel === 'Basic';

  // 获取用于Pictures标签页展示的图片
  const galleryImages = getCharacterImages();
  return (
    <div
      className={`${
        showProfile ? 'hidden lg:flex' : 'hidden'
      } w-80 bg-[#120518] border-l border-[#3a1a44] flex-col h-full transition-all duration-300 ease-in-out`}
    >
      <div className="p-6">
        {characterLoading ? (
          <div className="text-center mb-6">
            <div className="h-32 w-32 rounded-full overflow-hidden mx-auto mb-4 bg-gray-600 animate-pulse"></div>
            <div className="h-6 w-24 bg-gray-600 rounded mx-auto mb-1 animate-pulse"></div>
            <div className="h-4 w-32 bg-gray-600 rounded mx-auto mb-3 animate-pulse"></div>
            <div className="h-8 w-20 bg-gray-600 rounded mx-auto animate-pulse"></div>
          </div>
        ) : (
          <div className="text-center mb-6">
            <div className="h-32 w-32 rounded-full overflow-hidden mx-auto mb-4">
              <Image
                src={getMainAvatarUrl()}
                alt={character.name}
                width={128}
                height={128}
                className="object-cover w-full h-full"
              />
            </div>
            <h3 className="text-xl font-bold mb-1">{character.name}</h3>
            <p className="text-sm text-gray-400 mb-3">
              {character.occupation}, {character.age}
            </p>

            <Button
              variant="outline"
              size="sm"
              className={`text-sm px-4 py-2 h-auto ${
                isFollowing
                  ? 'bg-[#2a1a34] hover:bg-[#3a1a44]'
                  : 'bg-pink-500 hover:bg-pink-600'
              }`}
              onClick={onFollowToggle}
              disabled={isFollowLoading}
            >
              {isFollowLoading ? '...' : isFollowing ? 'Following' : '+ Follow'}
            </Button>
          </div>
        )}

        {characterLoading ? (
          <div className="mb-5">
            <div className="flex gap-2 mb-3">
              <div className="h-6 w-16 bg-gray-600 rounded animate-pulse"></div>
              <div className="h-6 w-20 bg-gray-600 rounded animate-pulse"></div>
            </div>
            <div className="h-4 w-12 bg-gray-600 rounded animate-pulse"></div>
          </div>
        ) : (
          <div className="flex items-center justify-between mb-5">
            <div className="flex flex-wrap gap-2">
              {character.tags?.map((tag: string, index: number) => (
                <Badge
                  key={index}
                  variant="outline"
                  className={`text-xs px-2 py-1 ${
                    tag === 'Private'
                      ? 'bg-red-500/20 border-red-500 text-red-400'
                      : 'bg-[#1a0a24] text-gray-300'
                  }`}
                >
                  {tag === 'Private' && (
                    <Lock className="h-3 w-3 mr-1 inline" />
                  )}
                  {tag}
                </Badge>
              ))}
            </div>
            <div className="flex items-center">
              <span className="text-sm mr-2">
                {getFakeStatsForCharacter(character).likeCount}
              </span>
              <button onClick={onFavoriteToggle}>
                <Heart
                  className={cn(
                    'h-4 w-4',
                    isFavorite ? 'text-pink-500 fill-pink-500' : 'text-gray-400'
                  )}
                />
              </button>
            </div>
          </div>
        )}

        {characterLoading ? (
          <div className="mb-5">
            <div className="h-4 w-full bg-gray-600 rounded mb-2 animate-pulse"></div>
            <div className="h-4 w-3/4 bg-gray-600 rounded mb-2 animate-pulse"></div>
            <div className="h-4 w-1/2 bg-gray-600 rounded animate-pulse"></div>
          </div>
        ) : (
          <p className="text-sm text-gray-300 mb-5 leading-relaxed">
            {character.description}
          </p>
        )}

        <Button
          variant="outline"
          size="sm"
          className="w-full mb-4 text-sm py-2 h-auto"
          onClick={onShareCharacter}
        >
          <Share2 className="h-4 w-4 mr-2" /> Share Character
        </Button>
      </div>

      <div className="flex-1 overflow-y-auto">
        <Tabs defaultValue="pictures" className="w-full">
          <TabsList className="grid grid-cols-3 w-full">
            <TabsTrigger value="pictures" className="text-xs py-2">
              📸 Pictures
            </TabsTrigger>
            <TabsTrigger value="videos" className="text-xs py-2">
              🎥 Videos
            </TabsTrigger>
            <TabsTrigger value="profile" className="text-xs py-2">
              📄 Profile
            </TabsTrigger>
          </TabsList>
          <TabsContent value="pictures" className="p-4">
            {galleryImages.length > 0 && (
              <div className="mb-3">
                <p className="text-xs text-gray-400 mb-2">
                  {character.resource?.profile_image?.length
                    ? `📸 Profile Gallery (${galleryImages.length} photos)`
                    : `📸 Photo Collection (${galleryImages.length} photos)`}
                </p>
              </div>
            )}

            <div className="grid grid-cols-2 gap-2">
              {galleryImages.map((image: string, index: number) => (
                <div
                  key={`gallery-${index}`}
                  className="aspect-[3/4] rounded-lg overflow-hidden group cursor-pointer"
                >
                  <Image
                    src={
                      getCharacterImageUrl(image) ||
                      getAvatarUrl(image) ||
                      '/placeholder.svg'
                    }
                    alt={`${character.name} photo ${index + 1}`}
                    width={120}
                    height={160}
                    className="object-cover w-full h-full group-hover:scale-105 transition-transform duration-200"
                  />
                </div>
              ))}

              {/* 只有非Premium用户才显示Premium锁定提示 */}
              {!isPremiumUser && galleryImages.length > 0 && (
                <div className="aspect-[3/4] rounded-lg overflow-hidden bg-[#1a0a24] flex items-center justify-center border border-dashed border-[#3a1a44] hover:border-pink-500/50 transition-colors cursor-pointer">
                  <div className="text-center p-3">
                    <Lock className="h-6 w-6 text-gray-500 mx-auto mb-2" />
                    <p className="text-xs text-gray-400">Unlock Premium</p>
                    <p className="text-xs text-gray-500 mt-1">
                      Exclusive Photos
                    </p>
                  </div>
                </div>
              )}

              {/* 如果没有图片，显示占位符 */}
              {galleryImages.length === 0 && (
                <div className="col-span-2 aspect-[3/2] rounded-lg overflow-hidden bg-[#1a0a24] flex items-center justify-center border border-dashed border-[#3a1a44]">
                  <div className="text-center p-4">
                    <div className="h-8 w-8 bg-gray-600 rounded-full mx-auto mb-2 flex items-center justify-center">
                      <span className="text-gray-400 text-sm">📸</span>
                    </div>
                    <p className="text-xs text-gray-400 mb-1">
                      No photos available
                    </p>
                    <p className="text-xs text-gray-500">
                      {!isPremiumUser
                        ? 'Upgrade for exclusive content'
                        : 'Photos coming soon'}
                    </p>
                  </div>
                </div>
              )}
            </div>
          </TabsContent>
          <TabsContent value="videos" className="p-4">
            {isPremiumUser ? (
              // Premium用户：显示视频内容或暂无视频的状态
              <div className="flex items-center justify-center h-32 bg-[#1a0a24] rounded-lg border border-dashed border-[#3a1a44]">
                <div className="text-center p-3">
                  <div className="h-8 w-8 bg-gray-600 rounded-full mx-auto mb-2 flex items-center justify-center">
                    <span className="text-gray-400 text-sm">🎥</span>
                  </div>
                  <p className="text-xs text-gray-400">
                    No videos available yet
                  </p>
                  <p className="text-xs text-gray-500 mt-1">Coming soon</p>
                </div>
              </div>
            ) : (
              // 非Premium用户：显示锁定状态
              <div className="flex items-center justify-center h-32 bg-[#1a0a24] rounded-lg border border-dashed border-[#3a1a44] hover:border-pink-500/50 transition-colors">
                <div className="text-center p-3">
                  <Lock className="h-6 w-6 text-gray-500 mx-auto mb-2" />
                  <p className="text-xs text-gray-400">Unlock Premium</p>
                  <p className="text-xs text-gray-500 mt-1">Access Videos</p>
                </div>
              </div>
            )}
          </TabsContent>
          <TabsContent value="profile" className="p-4">
            <div className="space-y-4">
              <div>
                <h4 className="text-sm font-medium mb-2">Background</h4>
                <p className="text-xs text-gray-400 leading-relaxed">
                  {character.resource?.opening || `${character.name} has been ${character.occupation.toLowerCase()} since he was young.`}
                </p>
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}
