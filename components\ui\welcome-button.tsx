'use client';

import { Heart } from 'lucide-react';
import { useState } from 'react';

interface WelcomeButtonProps {
  className?: string;
  onClick?: () => void;
}

export function WelcomeButton({ className = '', onClick }: WelcomeButtonProps) {
  const [isHovered, setIsHovered] = useState(false);

  return (
    <button
      className={`
        relative overflow-hidden rounded-full px-6 py-3 
        bg-gradient-to-r from-pink-300 to-purple-300 
        hover:from-pink-400 hover:to-purple-400 
        transition-all duration-300 transform hover:scale-105
        shadow-lg hover:shadow-xl
        ${className}
      `}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      onClick={onClick}
    >
      {/* 背景渐变 */}
      <div className="absolute inset-0 bg-gradient-to-r from-pink-200/20 to-purple-200/20 rounded-full" />
      
      {/* 内容区域 */}
      <div className="relative flex items-center justify-between">
        {/* 左侧文字 */}
        <div className="flex items-center">
          <span className="text-lg font-semibold bg-gradient-to-r from-pink-600 to-purple-600 bg-clip-text text-transparent">
            Welcome
          </span>
        </div>
        
        {/* 右侧爱心礼物盒 */}
        <div className="flex items-center ml-4">
          {/* 爱心图标 - 跳动动画 */}
          <div className="relative">
            <Heart 
              className={`
                h-6 w-6 text-red-500 
                transition-all duration-300 ease-in-out
                ${isHovered ? 'animate-pulse scale-110' : 'animate-bounce'}
              `}
              style={{
                animation: isHovered 
                  ? 'pulse 0.6s ease-in-out infinite' 
                  : 'bounce 1s ease-in-out infinite'
              }}
            />
            {/* 爱心光晕效果 */}
            <div className={`
              absolute inset-0 rounded-full 
              bg-red-400/30 blur-sm
              transition-all duration-300
              ${isHovered ? 'scale-150 opacity-60' : 'scale-100 opacity-0'}
            `} />
          </div>
          
          {/* 礼物盒装饰 */}
          <div className="ml-2 flex items-center">
            {/* 紫色丝带 */}
            <div className="w-1 h-4 bg-purple-400 rounded-full mx-1" />
            <div className="w-1 h-3 bg-purple-300 rounded-full mx-1" />
          </div>
        </div>
      </div>
      
      {/* 模糊光晕效果 */}
      <div className={`
        absolute inset-0 rounded-full 
        bg-gradient-to-r from-pink-400/20 to-purple-400/20 
        blur-xl transition-all duration-300
        ${isHovered ? 'scale-110 opacity-60' : 'scale-100 opacity-0'}
      `} />
    </button>
  );
} 