import type React from "react"
import type { Metada<PERSON> } from "next/dist/lib/metadata/types/metadata-interface"
import { Inter } from "next/font/google"
import "./globals.css"
import { ThemeProvider } from "@/components/theme-provider"
import ClientStatsManager from "@/components/client-stats-manager"
import { UserProvider } from "@/contexts/UserContext"
import AgeVerificationWrapper from "@/components/AgeVerificationWrapper"

const inter = Inter({ subsets: ["latin"] })

export const metadata: Metadata = {
  title: "LumiLove - Your AI Companion",
  description: "Experience emotional companionship with AI virtual companions",
  generator: 'v0.dev'
}

export const viewport = {
  width: 'device-width',
  initialScale: 1.0,
  maximumScale: 1.0,
  userScalable: false
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={`${inter.className} bg-[#0e0314] text-white antialiased`}>
        <ThemeProvider attribute="class" defaultTheme="dark" enableSystem={false} disableTransitionOnChange>
          <UserProvider>
            <ClientStatsManager />
            <AgeVerificationWrapper>
              {children}
            </AgeVerificationWrapper>
          </UserProvider>
        </ThemeProvider>
      </body>
    </html>
  )
}
