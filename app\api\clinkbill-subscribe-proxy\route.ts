import { NextRequest, NextResponse } from 'next/server';

// 使用Node.js runtime来支持https模块
export const runtime = 'nodejs';

/**
 * Clinkbill 订阅代理路由
 * 调用新的 V2 API 端点
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { planId } = body;

    // 获取认证头
    const authorization = request.headers.get('authorization');
    if (!authorization) {
      return NextResponse.json({ error: '缺少认证信息' }, { status: 401 });
    }

    console.log('🔄 Clinkbill 代理订阅请求:', { planId });

    // 临时禁用SSL验证（仅用于测试环境）
    process.env['NODE_TLS_REJECT_UNAUTHORIZED'] = '0';

    // 调用新的 V2 API 端点
    const response = await fetch(
      'https://api.loomelove.ai/api/v2/plan/subscribe',
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-Title': 'Lumilove',
          Authorization: authorization,
        },
        body: JSON.stringify({ planId }),
        redirect: 'manual', // 不自动跟随重定向
      }
    );

    console.log('📊 Clinkbill 后端响应状态:', response.status);
    console.log('📍 Location头:', response.headers.get('Location'));

    // 如果是重定向，返回重定向信息
    if (response.status >= 300 && response.status < 400) {
      const redirectUrl = response.headers.get('Location');

      if (redirectUrl) {
        console.log('🔄 检测到支付页面重定向:', redirectUrl);
        return NextResponse.json({
          success: true,
          redirect: true,
          paymentUrl: redirectUrl,
          message: '需要跳转到 Clinkbill 支付页面',
        });
      } else {
        return NextResponse.json(
          {
            success: false,
            error: '服务器返回重定向但未提供跳转地址',
          },
          { status: 500 }
        );
      }
    }

    // 如果不是重定向，处理其他响应
    if (response.ok) {
      const contentType = response.headers.get('content-type');
      if (contentType?.includes('application/json')) {
        const data = await response.json();
        console.log('📄 Clinkbill API 响应数据:', data);
        return NextResponse.json(data);
      } else {
        return NextResponse.json({
          success: true,
          message: 'Clinkbill 订阅成功',
        });
      }
    } else {
      // 处理错误响应
      let errorMessage = `HTTP ${response.status}: ${response.statusText}`;
      
      try {
        const errorData = await response.json();
        if (errorData.message) {
          errorMessage = errorData.message;
        }
      } catch (e) {
        // 如果无法解析错误响应，使用默认错误消息
      }

      console.error('❌ Clinkbill API 错误:', errorMessage);
      return NextResponse.json(
        {
          success: false,
          error: errorMessage,
        },
        { status: response.status }
      );
    }
  } catch (error) {
    console.error('❌ Clinkbill 订阅代理错误:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Clinkbill 订阅请求失败: ' + (error instanceof Error ? error.message : '未知错误'),
      },
      { status: 500 }
    );
  }
}