'use client';

import { useState, useEffect, useCallback, useMemo } from 'react';
import {
  getCharactersAsync,
  getCharacters,
  type Character,
  type CharacterListResponse,
} from '@/lib/characters';
import {
  getCreatorForCharacter,
  getDynamicCreatorData,
} from '@/lib/creator-data';

interface UseCharactersDataProps {
  gender: 'male' | 'female';
  activeTag: string;
  pageSize?: number;
}

interface CharacterDetailsCache {
  [key: string]: Character;
}

// 全局缓存，避免组件重渲染时丢失
const globalCharacterCache = new Map<string, Character>();

export function useCharactersData({
  gender,
  activeTag,
  pageSize = 20,
}: UseCharactersDataProps) {
  const [charactersData, setCharactersData] =
    useState<CharacterListResponse | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // 为角色分配创作者信息的函数
  const assignCreatorToCharacter = useCallback(
    (character: Character): Character => {
      // console.log('开始为角色分配创作者:', character.id, character.name);

      const creatorId = getCreatorForCharacter(character.id);
      // console.log('获取到的创作者ID:', creatorId);

      if (creatorId) {
        const creatorData = getDynamicCreatorData(creatorId);
        // console.log('获取到的创作者数据:', creatorData);

        if (creatorData) {
          const characterWithCreator = {
            ...character,
            creator: {
              id: creatorId,
              name: creatorData.name,
              likeCount: creatorData.followers,
            },
          };
          // console.log('角色分配创作者成功:', characterWithCreator);
          return characterWithCreator;
        }
      }

      // 如果没有找到创作者，使用默认值
      // console.log('使用默认创作者信息');
      return {
        ...character,
        creator: {
          id: 'unknown',
          name: 'Unknown Creator',
          likeCount: '0',
        },
      };
    },
    []
  );

  // 批量获取角色详情的优化函数
  const fetchCharacterDetails = useCallback(
    async (characters: Character[]) => {
      const { getCharacterDetail, transformCharacterDetail } = await import(
        '@/lib/api/characters'
      );

      // 分批处理，避免并发请求过多
      const batchSize = 5;
      const batches = [];
      for (let i = 0; i < characters.length; i += batchSize) {
        batches.push(characters.slice(i, i + batchSize));
      }

      const allDetailedCharacters: Character[] = [];

      for (const batch of batches) {
        const batchPromises = batch.map(async (character) => {
          const characterId = character.id.toString();

          // 检查全局缓存
          if (globalCharacterCache.has(characterId)) {
            console.log(`使用缓存的角色${characterId}详情`);
            const cachedCharacter = globalCharacterCache.get(characterId)!;
            return assignCreatorToCharacter(cachedCharacter);
          }

          try {
            const detailResponse = await getCharacterDetail(character.id);
            if (detailResponse.success) {
              const detailedCharacter = transformCharacterDetail(
                detailResponse.data.character
              );

              // 为角色分配创作者信息
              const characterWithCreator =
                assignCreatorToCharacter(detailedCharacter);

              // 存入全局缓存
              globalCharacterCache.set(characterId, characterWithCreator);
              return characterWithCreator;
            }

            // 如果详情获取失败，使用基础数据并分配创作者
            const characterWithCreator = assignCreatorToCharacter(character);
            return characterWithCreator;
          } catch (error) {
            console.warn(`获取角色${character.id}详情失败:`, error);
            // 回退到原始数据并分配创作者
            const characterWithCreator = assignCreatorToCharacter(character);
            return characterWithCreator;
          }
        });

        const batchResults = await Promise.allSettled(batchPromises);
        const successfulResults = batchResults
          .filter((result) => result.status === 'fulfilled')
          .map((result) => (result as PromiseFulfilledResult<Character>).value);

        allDetailedCharacters.push(...successfulResults);

        // 批次间延迟，避免服务器压力
        if (batches.indexOf(batch) < batches.length - 1) {
          await new Promise((resolve) => setTimeout(resolve, 100));
        }
      }

      return allDetailedCharacters;
    },
    [assignCreatorToCharacter]
  );

  // 主要的数据获取函数
  const fetchCharacters = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      console.log('🔍 开始获取角色数据', { gender, activeTag, pageSize });

      // 首先获取基础角色列表
      const basicData = await getCharactersAsync({
        gender,
        activeTag,
        pageSize,
      });
      console.log('basicData', basicData);

      if (!basicData?.characters || basicData.characters.length === 0) {
        // 如果API失败，使用本地数据
        console.warn('🔄 API数据为空，回退到本地数据');
        const fallbackData = getCharacters({ gender, activeTag, pageSize });

        // 为本地数据分配创作者信息
        const charactersWithCreators = fallbackData.characters.map(
          assignCreatorToCharacter
        );
        setCharactersData({
          ...fallbackData,
          characters: charactersWithCreators,
        });
        setLoading(false);
        return;
      }

      // 为基础数据分配创作者信息
      const basicCharactersWithCreators = basicData.characters.map(
        assignCreatorToCharacter
      );
      console.log(
        '基础角色数据分配创作者完成:',
        basicCharactersWithCreators.length,
        '个角色'
      );

      // 先显示基础数据，提升用户体验
      setCharactersData({
        ...basicData,
        characters: basicCharactersWithCreators,
      });

      // 后台异步获取详情数据
      try {
        const detailedCharacters = await fetchCharacterDetails(
          basicData.characters
        );
        console.log('detailedCharacters', detailedCharacters);
        setCharactersData({
          ...basicData,
          characters: detailedCharacters,
        });
      } catch (detailError) {
        console.warn('获取角色详情失败，使用基础数据:', detailError);
        // 保持基础数据显示
      }
    } catch (error) {
      console.error('获取角色数据失败:', error);
      setError('获取角色数据失败');

      // 回退到本地数据
      try {
        const fallbackData = getCharacters({ gender, activeTag, pageSize });
        const charactersWithCreators = fallbackData.characters.map(
          assignCreatorToCharacter
        );
        setCharactersData({
          ...fallbackData,
          characters: charactersWithCreators,
        });
      } catch (fallbackError) {
        console.error('本地数据回退也失败:', fallbackError);
      }
    } finally {
      setLoading(false);
    }
  }, [
    gender,
    activeTag,
    pageSize,
    fetchCharacterDetails,
    assignCreatorToCharacter,
  ]);

  // 当参数变化时重新获取数据
  useEffect(() => {
    fetchCharacters();
  }, [fetchCharacters]);

  // 返回的数据和状态
  const characters = useMemo(() => {
    return charactersData?.characters || [];
  }, [charactersData]);

  // 重试函数
  const retry = useCallback(() => {
    fetchCharacters();
  }, [fetchCharacters]);

  // 清除缓存函数
  const clearCache = useCallback(() => {
    globalCharacterCache.clear();
    console.log('✅ 角色详情缓存已清除');
  }, []);

  return {
    characters,
    charactersData,
    loading,
    error,
    retry,
    clearCache,
    cacheSize: globalCharacterCache.size,
  };
}
