'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from '@/components/ui/dialog';
import Sidebar from '@/components/sidebar';
import { useSubscriptionPlans } from '@/hooks/useSubscription';
import { convertPrivilegeToFeatures } from '@/lib/api/subscription';
import type { SubscriptionPlan } from '@/lib/api/subscription';

type PlanType = 'Free' | 'Prime' | 'Plus' | 'Pro' | 'Lifetime';

interface DialogState {
  isOpen: boolean;
  type:
    | 'subscribe'
    | 'upgrade'
    | 'downgrade'
    | 'success'
    | 'error'
    | 'already-subscribed';
  targetPlan?: PlanType;
  currentPlan?: PlanType;
}

interface ClinkbillResponse {
  success: boolean;
  redirect: boolean;
  paymentUrl: string;
  message: string;
}

export default function PaymentClinkbillPage() {
  const [currentSubscription, setCurrentSubscription] =
    useState<PlanType>('Free');
  const [dialog, setDialog] = useState<DialogState>({
    isOpen: false,
    type: 'subscribe',
  });
  const [isProcessing, setIsProcessing] = useState(false);

  // 获取API数据
  const {
    plans: apiPlans,
    loading: plansLoading,
    error: plansError,
  } = useSubscriptionPlans();
  const [paymentStatus, setPaymentStatus] = useState<string | null>(null);

  // 检查URL参数以处理支付回调
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const success = urlParams.get('success');
    const canceled = urlParams.get('canceled');

    if (success === 'true') {
      setPaymentStatus('支付成功！订阅已激活');
      setDialog({
        isOpen: true,
        type: 'success',
        targetPlan: dialog.targetPlan,
        currentPlan: dialog.currentPlan,
      });

      // 清理URL参数
      window.history.replaceState({}, '', window.location.pathname);

      setTimeout(() => {
        setPaymentStatus(null);
      }, 5000);
    } else if (canceled === 'true') {
      setPaymentStatus('支付已取消');
      setDialog({
        isOpen: true,
        type: 'error',
        targetPlan: dialog.targetPlan,
        currentPlan: dialog.currentPlan,
      });

      // 清理URL参数
      window.history.replaceState({}, '', window.location.pathname);

      setTimeout(() => {
        setPaymentStatus(null);
      }, 5000);
    }
  }, []);

  // 合并API数据和前端UI配置的函数（复用原有逻辑）
  const mergeApiDataWithUIConfig = () => {
    const defaultPlans = [
      {
        name: 'Free' as PlanType,
        price: 0,
        period: 'mo',
        duration: '',
        badge: null,
        features: [
          '✅ 30 text messages / month',
          '✅ 1 picture / month',
          '✅ 1 voice message / month',
          '✅ Create 1 character',
          '✅ 30-second voice call trial',
          '❌ Multi-picture generation (4/9)',
          '✅ Standard response speed',
          '✅ Cancel anytime',
          '✅ No credit card required',
        ],
        note: 'No credit card required',
        apiId: 0,
      },
      {
        name: 'Prime' as PlanType,
        price: 12.99,
        period: 'mo',
        duration: '1 month',
        badge: null,
        features: [
          '✅ 1,000 text messages / month',
          '✅ 50 pictures / month',
          '✅ 60 voice messages / month',
          '✅ Create up to 2 characters',
          '✅ 3-minute voice call / month',
          '❌ Multi-picture generation (4/9)',
          '✅ Standard response speed',
          '✅ Cancel anytime',
        ],
        note: 'Cancel anytime',
        apiId: 1,
      },
      {
        name: 'Plus' as PlanType,
        price: 8.99,
        period: 'mo',
        duration: '3 months',
        badge: '⭐ Most Popular',
        badgeColor: 'from-pink-400 to-pink-500',
        features: [
          '✅ 3,000 text messages / month',
          '✅ 150 pictures / month',
          '✅ 180 voice messages / month',
          '✅ Create up to 10 characters',
          '✅ 10-minute voice call / month',
          '❌ Multi-picture generation (4/9)',
          '✅ Standard response speed',
          '✅ Cancel anytime',
        ],
        note: '3-month plan • Cancel anytime',
        apiId: 2,
      },
      {
        name: 'Pro' as PlanType,
        price: 5.99,
        period: 'mo',
        duration: '12 months',
        badge: null,
        features: [
          '✅ Unlimited text messages',
          '✅ 200 pictures / month',
          '✅ Unlimited voice messages',
          '✅ Create unlimited characters',
          '✅ Unlimited voice call time',
          '✅ Multi-picture generation (4/9)',
          '✅ VIP response speed',
          '✅ Cancel anytime',
        ],
        note: '12-month plan • Cancel anytime',
        apiId: 3,
      },
      {
        name: 'Lifetime' as PlanType,
        price: 199.99,
        period: 'total',
        duration: '',
        badge: null,
        features: [
          '✅ Unlimited text messages',
          '✅ 200 pictures / month',
          '✅ Unlimited voice messages',
          '✅ Create unlimited characters',
          '✅ Unlimited voice call time',
          '✅ Multi-picture generation (4/9)',
          '✅ VIP response speed',
          '✅ Lifetime access',
        ],
        note: 'One-time purchase • Lifetime access',
        apiId: 4,
      },
    ];

    if (!plansLoading && !plansError && apiPlans.length > 0) {
      return defaultPlans.map((defaultPlan) => {
        const apiPlan = apiPlans.find((api) => api.level === defaultPlan.name);

        if (apiPlan) {
          const updatedPlan = { ...defaultPlan };
          updatedPlan.price = apiPlan.price;
          updatedPlan.apiId = apiPlan.id;

          if (apiPlan.features) {
            updatedPlan.features = convertPrivilegeToFeatures(
              apiPlan.features,
              apiPlan.level
            );
          }

          return updatedPlan;
        }

        return defaultPlan;
      });
    }

    return defaultPlans;
  };

  const plans = mergeApiDataWithUIConfig();
  const planHierarchy = { Free: 0, Prime: 1, Plus: 2, Pro: 3, Lifetime: 4 };

  const handleSubscribeClick = (targetPlan: PlanType) => {
    if (targetPlan === 'Free') return;

    const currentLevel = planHierarchy[currentSubscription];
    const targetLevel = planHierarchy[targetPlan];

    if (currentSubscription === targetPlan) {
      setDialog({
        isOpen: true,
        type: 'already-subscribed',
        targetPlan,
        currentPlan: currentSubscription,
      });
    } else if (currentLevel > targetLevel) {
      setDialog({
        isOpen: true,
        type: 'downgrade',
        targetPlan,
        currentPlan: currentSubscription,
      });
    } else if (currentLevel < targetLevel) {
      if (currentSubscription === 'Free') {
        setDialog({
          isOpen: true,
          type: 'subscribe',
          targetPlan,
          currentPlan: currentSubscription,
        });
      } else {
        setDialog({
          isOpen: true,
          type: 'upgrade',
          targetPlan,
          currentPlan: currentSubscription,
        });
      }
    }
  };

  const handleConfirmSubscription = async () => {
    setIsProcessing(true);
    setPaymentStatus('正在创建Clinkbill支付会话...');

    try {
      const targetPlan = plans.find((p) => p.name === dialog.targetPlan);
      if (!targetPlan) {
        throw new Error('未找到对应的套餐');
      }

      console.log('🔄 开始Clinkbill订阅:', {
        planName: dialog.targetPlan,
        apiId: targetPlan.apiId,
      });

      // 调用Clinkbill订阅代理API
      const response = await fetch('/api/clinkbill-subscribe-proxy', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${localStorage.getItem('token')}`,
        },
        body: JSON.stringify({
          planId: targetPlan.apiId,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `HTTP ${response.status}`);
      }

      const result: ClinkbillResponse = await response.json();
      console.log('📄 Clinkbill代理响应:', result);

      if (result.redirect && result.paymentUrl) {
        setPaymentStatus('支付页面已在新窗口中打开，请完成支付');
        console.log('🔗 Clinkbill支付URL:', result.paymentUrl);

        // 在新窗口中打开支付页面（与主支付页面一致）
        const paymentWindow = window.open(
          result.paymentUrl,
          '_blank',
          'width=800,height=600,scrollbars=yes,resizable=yes'
        );

        if (!paymentWindow) {
          throw new Error('无法打开支付页面，请检查浏览器弹窗设置');
        }

        console.log('✅ Clinkbill支付窗口已打开，等待用户完成支付');

        // 监听支付窗口关闭事件
        const checkClosed = setInterval(() => {
          if (paymentWindow.closed) {
            clearInterval(checkClosed);
            console.log('支付窗口已关闭');

            setPaymentStatus('支付窗口已关闭，正在确认支付状态...');

            // 3秒后询问用户支付结果
            setTimeout(() => {
              const paymentSuccess = window.confirm(
                '支付是否成功？\n\n点击"确定"如果支付成功\n点击"取消"如果支付失败或取消'
              );

              if (paymentSuccess) {
                setPaymentStatus('支付成功！');
                setCurrentSubscription(dialog.targetPlan!);
                setDialog({
                  isOpen: true,
                  type: 'success',
                  targetPlan: dialog.targetPlan,
                  currentPlan: dialog.currentPlan,
                });
                setTimeout(() => {
                  setPaymentStatus(null);
                }, 3000);
              } else {
                setPaymentStatus('支付已取消或失败');
                setDialog({
                  isOpen: true,
                  type: 'error',
                  targetPlan: dialog.targetPlan,
                  currentPlan: dialog.currentPlan,
                });
                setTimeout(() => {
                  setPaymentStatus(null);
                }, 3000);
              }
              setIsProcessing(false);
            }, 3000);
          }
        }, 1000);

        // 10分钟后自动清理监听器
        setTimeout(() => {
          clearInterval(checkClosed);
          if (isProcessing) {
            setPaymentStatus(null);
            setIsProcessing(false);
          }
        }, 10 * 60 * 1000);
      } else {
        // 直接支付成功的情况（不太可能发生，但保留处理）
        setPaymentStatus('订阅成功！');
        setCurrentSubscription(dialog.targetPlan!);
        setDialog({
          isOpen: true,
          type: 'success',
          targetPlan: dialog.targetPlan,
          currentPlan: dialog.currentPlan,
        });
        setTimeout(() => {
          setPaymentStatus(null);
          setIsProcessing(false);
        }, 2000);
      }
    } catch (error) {
      console.error('❌ Clinkbill订阅失败:', error);
      setPaymentStatus(`Clinkbill支付失败: ${(error as Error).message}`);
      setDialog({
        isOpen: true,
        type: 'error',
        targetPlan: dialog.targetPlan,
        currentPlan: dialog.currentPlan,
      });
      setTimeout(() => {
        setPaymentStatus(null);
        setIsProcessing(false);
      }, 3000);
    }
  };

  const closeDialog = () => {
    setDialog({ isOpen: false, type: 'subscribe' });
  };

  const getButtonState = (planName: PlanType) => {
    if (planName === 'Free') {
      return currentSubscription === 'Free' ? 'subscribed' : 'downgrade';
    }

    const currentLevel = planHierarchy[currentSubscription];
    const planLevel = planHierarchy[planName];

    if (currentSubscription === planName) {
      return 'current';
    } else if (currentLevel > planLevel) {
      return 'downgrade';
    } else {
      return 'available';
    }
  };

  const getUpgradeFeatures = (targetPlan: PlanType) => {
    switch (targetPlan) {
      case 'Prime':
        return [
          '1,000 text messages per month',
          '50 pictures per month',
          'Up to 2 characters',
          '3-minute voice calls',
        ];
      case 'Plus':
        return [
          '3,000 text messages per month',
          '150 pictures per month',
          'Up to 10 characters',
          '10-minute voice calls',
        ];
      case 'Pro':
        return [
          'Unlimited text messages',
          '200 pictures per month',
          'Unlimited voice messages',
          'Multi-picture generation (4/9)',
          'VIP response speed',
        ];
      case 'Lifetime':
        return [
          'All Pro features',
          'Lifetime access',
          'One-time payment',
          'No recurring charges',
        ];
      default:
        return [];
    }
  };

  return (
    <div className="flex min-h-screen">
      <Sidebar />

      <main className="flex-1 p-8 bg-[#0e0314]">
        <div className="max-w-8xl mx-auto">
          {/* Payment Status Banner */}
          {paymentStatus && (
            <div className="mb-6 bg-gradient-to-r from-pink-500/20 to-purple-500/20 rounded-xl p-4 border border-pink-500/30">
              <div className="flex items-center justify-center">
                <div className="text-center">
                  <div className="text-lg font-medium text-pink-300 mb-2">
                    {paymentStatus}
                  </div>
                  {paymentStatus.includes('正在跳转到Clinkbill') && (
                    <div className="text-sm text-gray-300">
                      即将跳转到Clinkbill安全支付页面
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}

          {/* Loading State */}
          {plansLoading && (
            <div className="flex justify-center items-center py-12 mb-8">
              <div className="text-white">
                <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-pink-500 mx-auto mb-4"></div>
                <p className="text-lg">Loading subscription plans...</p>
              </div>
            </div>
          )}

          {/* Error State */}
          {plansError && (
            <div className="flex flex-col items-center justify-center py-12 mb-8">
              <div className="text-gray-400 mb-4">📋</div>
              <div className="text-lg font-medium mb-2">无法加载套餐信息</div>
              <div className="text-gray-400 text-sm text-center max-w-md">
                {plansError.includes('403') || plansError.includes('访问被拒绝')
                  ? '您暂时没有权限访问套餐信息'
                  : '套餐信息暂时无法加载，将显示默认套餐'}
              </div>
            </div>
          )}

          <div className="text-center mb-16">
            <h1 className="text-5xl font-bold mb-6 bg-gradient-to-r from-pink-400 to-purple-500 bg-clip-text text-transparent">
              Choose your plan. Unlock your Love Story.
            </h1>
            <p className="text-gray-300 max-w-3xl mx-auto text-xl leading-relaxed">
              Ready to fall deeper? Upgrade to enjoy more messages, voices,
              images, and romantic possibilities.
            </p>
            <div className="mt-4 text-pink-400 text-lg">
              Current Plan:{' '}
              <span className="font-bold">{currentSubscription}</span>
            </div>
          </div>

          {/* Pricing Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
            {plans.map((plan, index) => {
              const buttonState = getButtonState(plan.name);

              return (
                <div
                  key={plan.name}
                  className={`rounded-2xl p-6 relative border-2 flex flex-col transition-all duration-300 hover:scale-105 ${
                    plan.name === 'Plus'
                      ? 'bg-gradient-to-br from-pink-500 to-pink-600 border-pink-400'
                      : 'bg-[#1a0a24] border-[#3a1a44]'
                  } ${
                    currentSubscription === plan.name
                      ? 'ring-2 ring-pink-400'
                      : ''
                  }`}
                  style={{ minHeight: '600px' }}
                >
                  {plan.badge && (
                    <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                      <div
                        className={`bg-gradient-to-r ${plan.badgeColor} text-white px-4 py-2 rounded-full text-sm font-bold flex items-center whitespace-nowrap`}
                      >
                        {plan.badge}
                      </div>
                    </div>
                  )}

                  {currentSubscription === plan.name && (
                    <div className="absolute -top-4 right-4">
                      <div className="bg-green-500 text-white px-3 py-1 rounded-full text-xs font-bold">
                        ✅ Current
                      </div>
                    </div>
                  )}

                  <div className="flex-1">
                    {/* Plan Name and Duration */}
                    <div className="flex justify-between items-start mb-4">
                      <h3 className="text-2xl font-bold text-white">
                        {plan.name}
                      </h3>
                      {plan.duration && (
                        <span className="text-gray-300 text-sm">
                          {plan.duration}
                        </span>
                      )}
                    </div>

                    {/* Price */}
                    <div className="mb-6">
                      <div className="text-4xl font-bold text-white">
                        ${plan.price.toFixed(2)}
                        <span className="text-lg text-gray-300">
                          /{plan.period}
                        </span>
                      </div>
                    </div>

                    {/* Features */}
                    <div className="space-y-3 mb-6">
                      {plan.features.map((feature, featureIndex) => (
                        <div
                          key={featureIndex}
                          className="text-white text-sm leading-relaxed"
                        >
                          {feature}
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Button */}
                  <div className="mt-auto">
                    <Button
                      className={`w-full py-3 text-sm font-semibold ${
                        buttonState === 'subscribed' ||
                        buttonState === 'current'
                          ? 'bg-gray-600 text-gray-300 cursor-not-allowed'
                          : buttonState === 'downgrade'
                          ? 'bg-gray-700 text-gray-400 cursor-not-allowed'
                          : plan.name === 'Plus'
                          ? 'bg-white text-pink-600 hover:bg-gray-100'
                          : 'bg-white text-black hover:bg-gray-100'
                      }`}
                      disabled={
                        buttonState === 'subscribed' ||
                        buttonState === 'current' ||
                        buttonState === 'downgrade'
                      }
                      onClick={() => handleSubscribeClick(plan.name)}
                    >
                      {buttonState === 'subscribed'
                        ? 'SUBSCRIBED'
                        : buttonState === 'current'
                        ? 'SUBSCRIBED'
                        : buttonState === 'downgrade'
                        ? plan.name === 'Free'
                          ? 'DOWNGRADE NOT AVAILABLE'
                          : 'DOWNGRADE NOT AVAILABLE'
                        : currentSubscription === 'Free'
                        ? 'SUBSCRIBE VIA CLINKBILL'
                        : 'UPGRADE VIA CLINKBILL'}
                    </Button>

                    <div className="text-center mt-3">
                      <p className="text-gray-300 text-xs">🕒 {plan.note}</p>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </main>

      {/* Dialogs */}
      <Dialog open={dialog.isOpen} onOpenChange={closeDialog}>
        <DialogContent className="bg-[#1a0a24] border-[#3a1a44] text-white max-w-md">
          {dialog.type === 'subscribe' && (
            <>
              <DialogHeader>
                <DialogTitle className="text-2xl font-bold text-center bg-gradient-to-r from-pink-400 to-purple-500 bg-clip-text text-transparent">
                  Unlock your Love Story 💘
                </DialogTitle>
                <DialogDescription className="text-gray-300 text-center mt-4">
                  Subscribe to the {dialog.targetPlan} plan via Clinkbill and
                  start enjoying:
                  <ul className="mt-4 space-y-2 text-left">
                    {getUpgradeFeatures(dialog.targetPlan!).map(
                      (feature, index) => (
                        <li key={index}>• {feature}</li>
                      )
                    )}
                  </ul>
                </DialogDescription>
              </DialogHeader>
              <div className="flex gap-4 mt-6">
                <Button
                  onClick={handleConfirmSubscription}
                  disabled={isProcessing}
                  className="flex-1 bg-pink-500 hover:bg-pink-600"
                >
                  {isProcessing ? 'Creating Session...' : 'Pay with Clinkbill'}
                </Button>
                <Button
                  onClick={closeDialog}
                  variant="outline"
                  className="flex-1 bg-transparent"
                >
                  Cancel
                </Button>
              </div>
              <div className="mt-4 p-3 bg-[#2a1a34] rounded-lg border border-[#3a1a44]">
                <p className="text-gray-400 text-sm flex items-start">
                  <span className="mr-2">🔒</span>
                  <span>
                    Secure payment powered by Clinkbill. You'll be redirected to
                    complete payment safely.
                  </span>
                </p>
              </div>
            </>
          )}

          {dialog.type === 'upgrade' && (
            <>
              <DialogHeader>
                <DialogTitle className="text-xl font-bold text-center">
                  Upgrade to {dialog.targetPlan} Plan
                </DialogTitle>
                <DialogDescription className="text-gray-300 mt-4">
                  You're currently subscribed to the {dialog.currentPlan} Plan.
                  <br />
                  <br />
                  Switching to the {dialog.targetPlan} Plan unlocks:
                  <ul className="mt-4 space-y-2">
                    {getUpgradeFeatures(dialog.targetPlan!).map(
                      (feature, index) => (
                        <li key={index}>• {feature}</li>
                      )
                    )}
                  </ul>
                  <br />
                  <strong>Note:</strong> Upgrading will replace your current
                  plan immediately.
                </DialogDescription>
              </DialogHeader>
              <div className="flex gap-4 mt-6">
                <Button
                  onClick={handleConfirmSubscription}
                  disabled={isProcessing}
                  className="flex-1 bg-pink-500 hover:bg-pink-600"
                >
                  {isProcessing ? 'Creating Session...' : `Pay with Clinkbill`}
                </Button>
                <Button
                  onClick={closeDialog}
                  variant="outline"
                  className="flex-1 bg-transparent"
                >
                  Cancel
                </Button>
              </div>
              <div className="mt-4 p-3 bg-[#2a1a34] rounded-lg border border-[#3a1a44]">
                <p className="text-gray-400 text-sm flex items-start">
                  <span className="mr-2">🔒</span>
                  <span>Secure upgrade via Clinkbill payment gateway.</span>
                </p>
              </div>
            </>
          )}

          {dialog.type === 'downgrade' && (
            <>
              <DialogHeader>
                <DialogTitle className="text-xl font-bold text-center">
                  Current Plan: {dialog.currentPlan}
                </DialogTitle>
                <DialogDescription className="text-gray-300 text-center mt-4">
                  Downgrades are not supported at this time.
                  <br />
                  Please wait until your current plan expires.
                </DialogDescription>
              </DialogHeader>
              <div className="flex justify-center mt-6">
                <Button
                  onClick={closeDialog}
                  className="bg-pink-500 hover:bg-pink-600"
                >
                  OK
                </Button>
              </div>
            </>
          )}

          {dialog.type === 'already-subscribed' && (
            <>
              <DialogHeader>
                <DialogTitle className="text-xl font-bold text-center">
                  Already Subscribed
                </DialogTitle>
                <DialogDescription className="text-gray-300 text-center mt-4">
                  You are already subscribed to this plan.
                </DialogDescription>
              </DialogHeader>
              <div className="flex justify-center mt-6">
                <Button
                  onClick={closeDialog}
                  className="bg-pink-500 hover:bg-pink-600"
                >
                  OK
                </Button>
              </div>
            </>
          )}

          {dialog.type === 'success' && (
            <>
              <DialogHeader>
                <DialogTitle className="text-2xl font-bold text-center text-green-400">
                  🎉 Payment Successful!
                </DialogTitle>
                <DialogDescription className="text-gray-300 text-center mt-4">
                  You're now enjoying the {dialog.targetPlan} plan.
                  <br />
                  Your benefits have been activated via Clinkbill. 💖
                </DialogDescription>
              </DialogHeader>
              <div className="flex justify-center mt-6">
                <Button
                  onClick={closeDialog}
                  className="bg-green-500 hover:bg-green-600"
                >
                  Awesome!
                </Button>
              </div>
            </>
          )}

          {dialog.type === 'error' && (
            <>
              <DialogHeader>
                <DialogTitle className="text-xl font-bold text-center text-red-400">
                  ⚠️ Payment Failed
                </DialogTitle>
                <DialogDescription className="text-gray-300 text-center mt-4">
                  Something went wrong with your Clinkbill payment.
                  <br />
                  Please try again or contact support if the issue persists.
                </DialogDescription>
              </DialogHeader>
              <div className="flex gap-4 mt-6">
                <Button
                  onClick={handleConfirmSubscription}
                  className="flex-1 bg-pink-500 hover:bg-pink-600"
                >
                  Try Again
                </Button>
                <Button
                  onClick={closeDialog}
                  variant="outline"
                  className="flex-1"
                >
                  Cancel
                </Button>
              </div>
            </>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}
