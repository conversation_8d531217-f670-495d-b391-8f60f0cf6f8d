import { useState, useCallback } from "react";

export interface Message {
  id: number;
  sender: "user" | "ai";
  text: string;
  timestamp: string;
  audioDuration?: number;
  hasImage?: boolean;
  imageSrc?: string;
  hasAudio?: boolean;
  audioSrc?: string;
  isThinking?: boolean;
}

export const useMessages = () => {
  const [messages, setMessages] = useState<Message[]>([]);

  // 高效更新单个消息，避免遍历整个数组
  const updateMessage = useCallback((id: number, updates: Partial<Message>) => {
    setMessages(prev => {
      const index = prev.findIndex(msg => msg.id === id);
      if (index === -1) return prev;
      
      // 只创建新数组如果确实有更新
      const newMessages = [...prev];
      newMessages[index] = { ...newMessages[index], ...updates };
      return newMessages;
    });
  }, []);

  // 添加单个消息
  const addMessage = useCallback((message: Message) => {
    setMessages(prev => [...prev, message]);
  }, []);

  // 批量添加消息（用于初始化）
  const addMessages = useCallback((newMessages: Message[]) => {
    setMessages(prev => [...prev, ...newMessages]);
  }, []);

  // 替换所有消息（用于重新加载）
  const replaceMessages = useCallback((newMessages: Message[]) => {
    setMessages(newMessages);
  }, []);

  // 清空所有消息
  const clearMessages = useCallback(() => {
    setMessages([]);
  }, []);

  // 流式更新消息内容（专门优化）
  const updateStreamingMessage = useCallback((id: number, newText: string) => {
    setMessages(prev => {
      const index = prev.findIndex(msg => msg.id === id);
      if (index === -1) return prev;
      
      const newMessages = [...prev];
      newMessages[index] = { 
        ...newMessages[index], 
        text: newText,
        isThinking: false 
      };
      return newMessages;
    });
  }, []);

  return {
    messages,
    updateMessage,
    addMessage,
    addMessages,
    replaceMessages,
    clearMessages,
    updateStreamingMessage,
    // 保留原始setter以备需要
    setMessages,
  };
};

