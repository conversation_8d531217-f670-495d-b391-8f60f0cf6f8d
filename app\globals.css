@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 240 10% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 240 10% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 240 10% 3.9%;
    --primary: 328 73% 69%;
    --primary-foreground: 355.7 100% 97.3%;
    --secondary: 240 4.8% 95.9%;
    --secondary-foreground: 240 5.9% 10%;
    --muted: 240 4.8% 95.9%;
    --muted-foreground: 240 3.8% 46.1%;
    --accent: 240 4.8% 95.9%;
    --accent-foreground: 240 5.9% 10%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 5.9% 90%;
    --input: 240 5.9% 90%;
    --ring: 328 73% 69%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 270 70% 5%;
    --foreground: 0 0% 98%;
    --card: 270 70% 7%;
    --card-foreground: 0 0% 98%;
    --popover: 270 70% 7%;
    --popover-foreground: 0 0% 98%;
    --primary: 328 73% 69%;
    --primary-foreground: 355.7 100% 97.3%;
    --secondary: 240 3.7% 15.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 270 70% 11%;
    --muted-foreground: 240 5% 64.9%;
    --accent: 270 70% 15%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 270 70% 15%;
    --input: 270 70% 15%;
    --ring: 328 73% 69%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    margin: 0;
    padding: 0;
  }
  html {
    height: 100%;
  }
}

.gradient-bg {
  background: radial-gradient(circle at center, rgba(255, 105, 180, 0.3) 0%, rgba(14, 3, 20, 0.5) 70%);
}

.card-hover-effect {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card-hover-effect:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px -5px rgba(255, 105, 180, 0.4);
}

/* 聊天列表滚动条样式 */
.chat-list-scroll {
  scrollbar-width: thin;
  scrollbar-color: rgba(255, 105, 180, 0.3) transparent;
}

.chat-list-scroll::-webkit-scrollbar {
  width: 6px;
}

.chat-list-scroll::-webkit-scrollbar-track {
  background: transparent;
  border-radius: 3px;
}

.chat-list-scroll::-webkit-scrollbar-thumb {
  background: rgba(255, 105, 180, 0.3);
  border-radius: 3px;
  transition: background-color 0.2s ease;
}

.chat-list-scroll::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 105, 180, 0.5);
}

/* 隐藏水平滚动条 */
.scrollbar-hide {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;
}

/* 标签滑动条样式 */
.filter-tags-scroll {
  scrollbar-width: thin;
  scrollbar-color: rgba(255, 105, 180, 0.4) transparent;
}

.filter-tags-scroll::-webkit-scrollbar {
  height: 6px;
}

.filter-tags-scroll::-webkit-scrollbar-track {
  background: rgba(26, 10, 36, 0.3);
  border-radius: 3px;
}

.filter-tags-scroll::-webkit-scrollbar-thumb {
  background: rgba(255, 105, 180, 0.4);
  border-radius: 3px;
  transition: background-color 0.2s ease;
}

.filter-tags-scroll::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 105, 180, 0.6);
}

/* 确保flex容器正确处理高度 */
.flex-col > .flex-1 {
  min-height: 0;
}

/* 修复聊天页面布局 */
.chat-layout {
  height: 100vh;
  overflow: hidden;
}

.chat-layout .flex-1 {
  min-height: 0;
}

.chat-layout .overflow-y-auto {
  height: 100%;
}

/* 头像兜底样式 */
.avatar-fallback {
  transition: opacity 0.3s ease;
}

/* 当图片加载失败时，显示兜底头像 */
.avatar-container img:not([src]),
.avatar-container img[src=""],
.avatar-container img[src*="placeholder.svg"] {
  display: none;
}

.avatar-container img:not([src]) + .avatar-fallback,
.avatar-container img[src=""] + .avatar-fallback,
.avatar-container img[src*="placeholder.svg"] + .avatar-fallback {
  opacity: 1 !important;
}

/* 用户头像专用样式 */
.user-avatar-container {
  position: relative;
  overflow: hidden;
  border-radius: 50%;
}

.user-avatar-fallback {
  position: absolute;
  inset: 0;
  background: linear-gradient(135deg, #ec4899, #8b5cf6);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: bold;
  font-size: 0.875rem;
  opacity: 0;
  transition: opacity 0.3s ease;
}

/* 当SmartImage显示兜底内容时，隐藏原图片并显示兜底 */
.user-avatar-container:has(.bg-gradient-to-br) .user-avatar-fallback {
  opacity: 1;
}

/* 兜底图片样式优化 */
.user-avatar-container .bg-gradient-to-br {
  border-radius: 50%;
}

/* 移动端键盘适配样式 */
@supports (height: 100dvh) {
  .keyboard-open {
    height: 100dvh;
  }
}

.keyboard-open .chat-layout {
  padding-bottom: var(--keyboard-offset, 0px);
}

/* 移动端触摸优化 */
@media (hover: none) and (pointer: coarse) {
  /* 移动设备的按钮触摸优化 */
  button {
    -webkit-tap-highlight-color: rgba(255, 105, 180, 0.3);
  }
  
  /* 确保触摸目标足够大 */
  .min-h-\[44px\] {
    min-height: 44px;
  }
  
  .min-w-\[44px\] {
    min-width: 44px;
  }
}

/* 移动端底部导航样式 */
@media (max-width: 768px) {
  body {
    padding-bottom: env(safe-area-inset-bottom);
  }
  
  /* 确保内容不被底部导航遮挡 */
  .mobile-safe-bottom {
    padding-bottom: calc(64px + env(safe-area-inset-bottom, 0px));
  }
}

/* 移动端专用样式 */
@layer utilities {
  /* 隐藏滚动条但保持功能 */
  .no-scrollbar::-webkit-scrollbar {
    display: none;
  }
  .no-scrollbar {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
}

/* Swiper 样式覆盖 */
.swiper-vertical {
  height: 100%;
}

.swiper-slide {
  height: auto;
}
