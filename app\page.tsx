'use client';

import { useDeviceType } from '@/hooks/useDeviceType';
import { MobileHome } from '@/components/mobile/MobileHome';
import DesktopHome from '@/components/DesktopHome';
import { LumidateLoading } from '@/components/lumilove-loading';
import { useState, useEffect } from 'react';

export default function Home() {
  const { isMobile } = useDeviceType();
  const [isLoading, setIsLoading] = useState(true);
  
  // 设置一个固定的加载时间，模拟页面加载完成
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 1500); // 显示1.5秒的加载动画，让动画有足够时间完成
    
    return () => clearTimeout(timer);
  }, []); // 空依赖数组，只在组件首次挂载时运行一次
  
  // 如果正在加载，显示动画
  if (isLoading) {
    return (
      <LumidateLoading
        text="LUMILOVE"
        onComplete={() => setIsLoading(false)} // 动画完成后也可以提前结束
        className="z-50"
        loop={false}
        mobileAdaptive={true}
      />
    );
  }
  
  // 显示实际页面内容
  return isMobile ? <MobileHome /> : <DesktopHome />;
}
