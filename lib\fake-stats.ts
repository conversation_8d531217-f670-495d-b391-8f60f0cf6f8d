// 假统计数据生成工具
// 用于在角色卡组件中生成"过w"的假数据

// 更好的随机数生成函数
function generateRandomStat(id: string | number, type: 'chat' | 'like' | 'fav'): number {
  // 将ID转换为数字
  const numId = typeof id === 'string' ? parseInt(id.replace(/\D/g, '')) || 1 : Number(id) || 1;
  
  // 为不同类型使用不同的种子
  const typeSeeds = {
    chat: 13,
    like: 17, 
    fav: 23
  };
  
  // 使用角色ID和类型种子生成伪随机数
  const seed = numId * typeSeeds[type];
  const random = Math.sin(seed) * 10000;
  const normalized = Math.abs(random - Math.floor(random));
  
  // 生成1.2w到8.9w之间的随机数
  const min = 12000;
  const max = 89000;
  const result = Math.floor(normalized * (max - min + 1)) + min;
  
  return result;
}

export function getFakeStat(num: any, id: string | number, type: 'chat' | 'like' | 'fav'): string {
  // 如果是0、null、undefined、非数字，都使用假数据
  if (!num || Number(num) === 0 || isNaN(Number(num))) {
    const fake = generateRandomStat(id, type);
    
    // 格式化为 "1.2w" 这种格式
    const wan = Math.floor(fake / 10000);
    const remainder = Math.floor((fake % 10000) / 1000);
    return remainder > 0 ? `${wan}.${remainder}w` : `${wan}w`;
  }
  
  // 如果API返回的数据大于0，保持原样
  const numValue = Number(num);
  if (numValue >= 10000) {
    const wan = Math.floor(numValue / 10000);
    const remainder = Math.floor((numValue % 10000) / 1000);
    return remainder > 0 ? `${wan}.${remainder}w` : `${wan}w`;
  }
  
  return num.toString();
}

// 为角色生成所有假统计数据
export function getFakeStatsForCharacter(character: any) {
  const id = character.id;
  
  return {
    chatCount: getFakeStat(character.chatCount, id, 'chat'),
    likeCount: getFakeStat(character.likeCount, id, 'like'),
    followers: getFakeStat(character.followers || character.favoriteCount, id, 'fav'),
    creatorLikeCount: getFakeStat(character.creator?.likeCount, id, 'like')
  };
} 