"use client";

import { useState, useEffect, useCallback } from 'react';
import { getRecentChatCharactersSync } from '@/lib/characters';
import { getAvatarUrl } from '@/lib/image-utils';

interface RecentChat {
  id: string;
  name: string;
  avatarSrc: string;
  unreadCount: number;
}

export function useRecentChats(isLoggedIn: boolean, limit: number = 3) {
  const [recentChats, setRecentChats] = useState<RecentChat[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchRecentChats = useCallback(async () => {
    if (!isLoggedIn) {
      setRecentChats([]);
      return;
    }
    
    setLoading(true);
    setError(null);
    
    try {
      console.log('🔍 获取最近聊天数据...');
      
      // 动态导入API模块，减少初始包大小
      const { getRecentChats } = await import('@/lib/api/characters');
      const response = await getRecentChats(limit);
      
      if (response.success && response.data.characters) {
        console.log('✅ API获取最近聊天成功');
        setRecentChats(response.data.characters);
      } else {
        console.warn('🔄 API获取失败，使用本地数据');
        throw new Error('API调用失败');
      }
    } catch (error) {
      console.error('获取最近聊天错误:', error);
      setError('获取最近聊天失败');
      
      // 对于游客模式或API失败的情况，不显示任何虚假数据
      console.log('❌ API failed, not showing any fallback data to avoid fake entries');
      setRecentChats([]);
    } finally {
      setLoading(false);
    }
  }, [isLoggedIn, limit]);

  // 当登录状态变化时重新获取
  useEffect(() => {
    fetchRecentChats();
  }, [fetchRecentChats]);

  // 手动刷新函数
  const refresh = useCallback(() => {
    fetchRecentChats();
  }, [fetchRecentChats]);

  return {
    recentChats,
    loading,
    error,
    refresh,
    hasRecentChats: recentChats.length > 0
  };
}