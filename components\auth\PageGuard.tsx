"use client";

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useUser } from '@/contexts/UserContext';

export enum PageAccessLevel {
  PUBLIC = 'public',           // 游客可访问（首页、聊天浏览）
  AUTH_REQUIRED = 'auth',      // 必须登录（支付页面、个人资料）
  AUTH_OPTIONAL = 'optional'   // 登录后功能更完整
}

interface PageGuardProps {
  accessLevel: PageAccessLevel;
  children: React.ReactNode;
  fallback?: React.ReactNode;
}

export function PageGuard({ accessLevel, children, fallback }: PageGuardProps) {
  const { isLoggedIn, isLoading } = useUser();
  const router = useRouter();

  useEffect(() => {
    if (!isLoading && accessLevel === PageAccessLevel.AUTH_REQUIRED && !isLoggedIn) {
      router.push('/login');
    }
  }, [isLoggedIn, isLoading, accessLevel, router]);

  // 显示加载状态
  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-[#0e0314]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-pink-500 mx-auto mb-4"></div>
          <p className="text-gray-400">Loading...</p>
        </div>
      </div>
    );
  }
  
  // 如果需要认证但用户未登录，显示回退内容或重定向
  if (accessLevel === PageAccessLevel.AUTH_REQUIRED && !isLoggedIn) {
    return fallback || null;
  }

  return <>{children}</>;
}