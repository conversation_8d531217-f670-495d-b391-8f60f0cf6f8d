# Sneaky页面 API接口需求文档

## 概述

本文档梳理了Sneaky页面(付费图片包浏览页面)接入数据库后所需的API接口。基于当前页面功能分析，包含图片包管理、用户收藏、购买记录等核心功能。

## 核心数据实体

### 1. PhotoPack (图片包)
```typescript
interface PhotoPack {
  id: number
  name: string                    // 包名
  description: string             // 描述
  images: number                  // 图片数量
  price: number                   // 价格(美元)
  style: string                   // 风格类型
  intimacy: string               // 亲密度等级
  coverImage: string             // 封面图片URL
  previewImages: string[]        // 预览图片URL数组
  isHot: boolean                 // 是否热门
  isLimited: boolean             // 是否限时
  limitedTime?: string           // 限时时间描述
  isNew: boolean                 // 是否新品
  createdAt: string              // 创建时间
  updatedAt: string              // 更新时间
  popularity: number             // 热度评分
  tags: string[]                 // 标签数组
  creatorId: number              // 创作者ID
}
```

### 2. UserCollection (用户收藏)
```typescript
interface UserCollection {
  id: number
  userId: number                 // 用户ID
  packId: number                 // 图片包ID
  imageUrl: string               // 具体收藏的图片URL
  imageTitle: string             // 图片标题
  createdAt: string              // 收藏时间
}
```

### 3. UserPurchase (用户购买记录)
```typescript
interface UserPurchase {
  id: number
  userId: number                 // 用户ID
  packId: number                 // 图片包ID
  purchaseDate: string           // 购买日期
  amount: number                 // 购买金额
  paymentMethod: string          // 支付方式
  status: string                 // 状态 (completed, pending, cancelled)
}
```

## API接口列表

### 1. 图片包管理接口

#### 1.1 获取图片包列表
```http
GET /api/photo-packs
```

**查询参数:**
- `page`: 页码 (默认: 1)
- `limit`: 每页数量 (默认: 6)
- `style`: 风格过滤 (Bedroom, Cosplay, Outdoor, Shower, Fantasy)
- `intimacy`: 亲密度过滤 (Soft, Sexy, NSFW, VIP)
- `tags`: 标签过滤 (hot, limited, new)
- `sort`: 排序方式 (popularity, newest, price_asc, price_desc)
- `search`: 搜索关键词

**响应:**
```json
{
  "data": [
    {
      "id": 1,
      "name": "🔥 Forbidden Nurse",
      "description": "Tonight, the clinic is closed...",
      "images": 4,
      "price": 2.99,
      "style": "Cosplay",
      "intimacy": "NSFW",
      "coverImage": "/sneaky/heng01.jpg",
      "previewImages": ["url1", "url2"],
      "isHot": true,
      "isLimited": true,
      "limitedTime": "48h",
      "isNew": false,
      "createdAt": "2024-01-01T00:00:00Z",
      "popularity": 95,
      "tags": ["hot", "limited"],
      "creatorId": 1
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 6,
    "total": 50,
    "totalPages": 9
  }
}
```

#### 1.2 获取单个图片包详情
```http
GET /api/photo-packs/{id}
```

**响应:**
```json
{
  "data": {
    // PhotoPack 完整信息
    "allImages": ["url1", "url2", "url3", "url4"], // 所有图片URL
    "isOwned": true, // 当前用户是否拥有
    "purchaseDate": "2024-01-15T10:00:00Z" // 购买日期(如果已购买)
  }
}
```

### 2. 用户相关接口

#### 2.1 获取用户拥有的图片包
```http
GET /api/user/photo-packs
```

**查询参数:**
- `page`: 页码
- `limit`: 每页数量

**响应:**
```json
{
  "data": [
    {
      "id": 2,
      "name": "Midnight Swim",
      "description": "The pool is empty...",
      "images": 6,
      "purchaseDate": "2024-01-15T10:00:00Z",
      "lastUpdated": "2024-01-20T15:30:00Z",
      "coverImage": "/sneaky/heng02.jpg"
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 10,
    "total": 3,
    "totalPages": 1
  }
}
```

#### 2.2 获取用户收藏列表
```http
GET /api/user/collections
```

**查询参数:**
- `page`: 页码
- `limit`: 每页数量

**响应:**
```json
{
  "data": [
    {
      "id": 101,
      "imageUrl": "/sneaky/midnight_swim_cer.png",
      "imageTitle": "Midnight Swim #3",
      "packName": "Midnight Swim",
      "packId": 2,
      "createdAt": "2024-01-15T10:00:00Z"
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 20,
    "total": 8,
    "totalPages": 1
  }
}
```

#### 2.3 添加/移除收藏
```http
POST /api/user/collections
```

**请求体:**
```json
{
  "packId": 2,
  "imageUrl": "/sneaky/midnight_swim_cer.png",
  "imageTitle": "Midnight Swim #3"
}
```

```http
DELETE /api/user/collections/{collectionId}
```

### 3. 购买相关接口

#### 3.1 创建购买订单
```http
POST /api/purchases
```

**请求体:**
```json
{
  "packId": 1,
  "paymentMethod": "stripe" // 或其他支付方式
}
```

**响应:**
```json
{
  "data": {
    "orderId": "order_123456",
    "amount": 2.99,
    "paymentUrl": "https://checkout.stripe.com/...",
    "status": "pending"
  }
}
```

#### 3.2 确认购买完成
```http
POST /api/purchases/{orderId}/confirm
```

**请求体:**
```json
{
  "paymentId": "pi_123456", // 支付平台返回的ID
  "status": "completed"
}
```

#### 3.3 获取购买历史
```http
GET /api/user/purchases
```

**查询参数:**
- `page`: 页码
- `limit`: 每页数量
- `status`: 状态过滤

**响应:**
```json
{
  "data": [
    {
      "id": 1,
      "packId": 2,
      "packName": "Midnight Swim",
      "amount": 4.99,
      "purchaseDate": "2024-01-15T10:00:00Z",
      "paymentMethod": "stripe",
      "status": "completed"
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 10,
    "total": 5,
    "totalPages": 1
  }
}
```

### 4. 权限验证接口

#### 4.1 检查图片包访问权限
```http
GET /api/photo-packs/{id}/access
```

**响应:**
```json
{
  "data": {
    "hasAccess": true,
    "reason": "owned", // owned, subscription, trial, denied
    "purchaseDate": "2024-01-15T10:00:00Z"
  }
}
```

#### 4.2 获取图片包的所有图片
```http
GET /api/photo-packs/{id}/images
```

**响应:**
```json
{
  "data": {
    "images": [
      {
        "id": 1,
        "url": "/sneaky/pack_2_image_1.jpg",
        "title": "Midnight Swim #1",
        "description": "...",
        "order": 1
      }
    ]
  }
}
```

### 5. 统计和分析接口

#### 5.1 获取热门图片包
```http
GET /api/photo-packs/trending
```

**查询参数:**
- `limit`: 返回数量 (默认: 10)
- `timeframe`: 时间范围 (daily, weekly, monthly)

#### 5.2 获取推荐图片包
```http
GET /api/photo-packs/recommendations
```

**查询参数:**
- `limit`: 返回数量 (默认: 6)
- `userId`: 用户ID (用于个性化推荐)


## 实现优先级建议

### 高优先级 (核心功能)
1. `GET /api/photo-packs` - 图片包列表
2. `GET /api/photo-packs/{id}` - 图片包详情
3. `GET /api/user/photo-packs` - 用户图库
4. `GET /api/photo-packs/{id}/access` - 权限验证

### 中优先级 (增强功能)
1. `GET /api/user/collections` - 用户收藏
2. `POST /api/user/collections` - 添加收藏
3. `POST /api/purchases` - 创建购买
4. `POST /api/purchases/{orderId}/confirm` - 确认购买

### 低优先级 (优化功能)
1. `GET /api/photo-packs/trending` - 热门推荐
2. `GET /api/photo-packs/recommendations` - 个性化推荐
3. `GET /api/user/purchases` - 购买历史

## 安全考虑

1. **身份验证**: 所有用户相关接口需要JWT token验证
2. **权限控制**: 图片访问需要验证用户购买状态
3. **支付安全**: 购买流程需要与支付平台的webhook集成
4. **图片保护**: 付费图片需要通过授权URL访问，防止直接链接泄露
5. **频率限制**: 对搜索和浏览接口进行Rate Limiting

## 性能优化建议

1. **缓存策略**: 图片包列表使用Redis缓存
2. **CDN部署**: 图片资源使用CDN加速
3. **分页优化**: 大列表使用游标分页
4. **图片压缩**: 提供多种尺寸的图片版本
5. **懒加载**: 图片预览支持懒加载