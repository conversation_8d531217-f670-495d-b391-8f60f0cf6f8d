// API配置
export const API_CONFIG = {
  // 生产环境的后端URL
  BASE_URL: process.env.NEXT_PUBLIC_API_BASE_URL || 'https://api.loomelove.ai',

  // API端点
  ENDPOINTS: {
    AUTH: {
      LOGIN: '/api/auth/login',
      REGISTER: '/api/auth/register',
      VERIFY: '/api/auth/verify',
    },
    USERS: {
      PROFILE: '/api/users/profile',
    },
    CHAT: {
      SEND_MESSAGE: '/api/chat/stream',
      USAGE: '/api/chat/usage',
    },
    GALLERY: {
      // 图包列表接口
      LIST: '/package',
      // 已购买图包接口
      PURCHASED: '/package/purchased',
      // 图包详情接口
      DETAIL: '/package/:id',
      // 收藏资源列表接口
      FAVORITES: '/package/resource',
      // 收藏操作接口
      FAVORITE_TOGGLE: '/package/resource/collect',
      // 购买图包接口
      PURCHASE: '/api/package/purchase',
    },
    SUBSCRIPTION: {
      // 套餐列表接口
      PLAN_LIST: '/plan/list',
      // 订阅套餐接口
      SUBSCRIBE: '/api/plan/subscribe',
    },
    CHARACTERS: {
      // 角色列表接口
      LIST: '/characters/list',
      // 最近聊天接口
      RECENT: '/characters/recent',
      // 角色详情接口
      DETAIL: '/characters/detail',
      // 标签列表接口
      LABEL_LIST: '/characters/label/list',
    },
  },
};

// 图片资源配置
export const IMAGE_CONFIG = {
  // S3存储桶前缀
  CDN_BASE_URL: 'https://lumiloveaibucket.s3.ap-southeast-2.amazonaws.com',

  // 图片前缀拼接开关
  // true: 为相对路径添加CDN前缀 (如: /male/male_01.png -> https://lumiloveaibucket.s3.ap-southeast-2.amazonaws.com/male/male_01.png)
  // false: 保持原始路径不变 (如: /male/male_01.png -> /male/male_01.png)
  ENABLE_PREFIX_CONCAT:
    process.env.NEXT_PUBLIC_ENABLE_IMAGE_PREFIX === 'true' || true, // 默认启用
};

// 辅助函数：构建完整的API URL
export const buildApiUrl = (endpoint: string): string => {
  return `${API_CONFIG.BASE_URL}${endpoint}`;
};

// 辅助函数：构建完整的图片URL
export const buildImageUrl = (imagePath: string): string => {
  // 如果已经是完整URL，直接返回
  if (imagePath.startsWith('http://') || imagePath.startsWith('https://')) {
    return imagePath;
  }

  // 根据开关决定是否添加CDN前缀
  if (!IMAGE_CONFIG.ENABLE_PREFIX_CONCAT) {
    // 关闭前缀拼接，直接返回原始路径
    console.log('🚫 图片前缀拼接已关闭，使用原始路径:', imagePath);
    return imagePath;
  }

  // 启用前缀拼接，添加CDN前缀
  const cleanPath = imagePath.startsWith('/') ? imagePath.slice(1) : imagePath;
  const fullUrl = `${IMAGE_CONFIG.CDN_BASE_URL}/${cleanPath}`;
  // console.log('🔗 图片前缀拼接已启用:', imagePath, '->', fullUrl);
  return fullUrl;
};
