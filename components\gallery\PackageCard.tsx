/**
 * 图包卡片组件
 * 用于显示单个图包的信息和操作
 */

'use client';

import { useState } from 'react';
import { 
  Heart, 
  Lock, 
  CheckCircle, 
  Clock, 
  CreditCard,
  BookmarkIcon 
} from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import SmartImage from '@/components/SmartImage';
import { getGalleryImageUrl } from '@/lib/image-utils';
import type { GalleryPackage } from '@/lib/api/gallery';

interface PackageCardProps {
  package: GalleryPackage;
  isHovered: boolean;
  onHover: (id: number | null) => void;
  onFavoriteToggle: (id: number, currentState: boolean) => Promise<void>;
  onPurchase: (id: number) => Promise<void>;
  favoriteLoading?: boolean;
  purchaseLoading?: boolean;
}

export default function PackageCard({
  package: pack,
  isHovered,
  onHover,
  onFavoriteToggle,
  onPurchase,
  favoriteLoading = false,
  purchaseLoading = false,
}: PackageCardProps) {
  const [imageError, setImageError] = useState(false);

  // 获取标签显示
  const getTagBadges = () => {
    const badges = [];
    
    if (pack.tags.includes('HOT')) {
      badges.push(
        <Badge key="hot" className="bg-red-500/80 text-white text-xs">
          🔥 HOT
        </Badge>
      );
    }
    
    if (pack.tags.includes('Limited')) {
      badges.push(
        <Badge key="limited" className="bg-orange-500/80 text-white text-xs">
          <Clock className="h-3 w-3 mr-1" />
          Limited
        </Badge>
      );
    }
    
    if (pack.tags.includes('New')) {
      badges.push(
        <Badge key="new" className="bg-green-500/80 text-white text-xs">
          🆕 New
        </Badge>
      );
    }

    return badges;
  };

  // 获取亲密度等级颜色
  const getIntimacyColor = (intimacy: string) => {
    switch (intimacy.toLowerCase()) {
      case 'soft':
        return 'bg-blue-500/20 text-blue-300 border-blue-500/30';
      case 'sexy':
        return 'bg-pink-500/20 text-pink-300 border-pink-500/30';
      case 'nsfw':
        return 'bg-red-500/20 text-red-300 border-red-500/30';
      case 'vip':
        return 'bg-purple-500/20 text-purple-300 border-purple-500/30';
      default:
        return 'bg-gray-500/20 text-gray-300 border-gray-500/30';
    }
  };

  return (
    <div
      className="relative bg-[#1a0a24] rounded-xl overflow-hidden border border-[#3a1a44] transition-all duration-300 hover:scale-[1.02] shadow-lg group"
      onMouseEnter={() => onHover(pack.id)}
      onMouseLeave={() => onHover(null)}
    >
      {/* 封面图片 */}
      <div className="relative aspect-[3/4] overflow-hidden">
        <SmartImage
          src={getGalleryImageUrl(pack.coverImage)}
          alt={pack.title}
          fill
          className="object-cover transition-transform duration-300 group-hover:scale-105"
          onError={() => setImageError(true)}
        />
        
        {/* 遮罩层 */}
        <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-transparent to-transparent" />
        
        {/* 顶部标签 */}
        <div className="absolute top-2 left-2 flex flex-wrap gap-1">
          {getTagBadges()}
        </div>
        
        {/* 收藏按钮 */}
        <div className="absolute top-2 right-2">
          <Button
            size="sm"
            variant="ghost"
            className="h-8 w-8 p-0 bg-black/50 hover:bg-black/70 text-white"
            onClick={(e) => {
              e.stopPropagation();
              onFavoriteToggle(pack.id, pack.isFavorited);
            }}
            disabled={favoriteLoading}
          >
            <Heart
              className={`h-4 w-4 ${
                pack.isFavorited ? 'fill-red-500 text-red-500' : 'text-white'
              }`}
            />
          </Button>
        </div>
        
        {/* 拥有状态 */}
        {pack.isPurchased && (
          <div className="absolute top-2 right-12">
            <Badge className="bg-green-500/80 text-white text-xs">
              <CheckCircle className="h-3 w-3 mr-1" />
              Owned
            </Badge>
          </div>
        )}
        
        {/* 底部信息 */}
        <div className="absolute bottom-0 left-0 right-0 p-3">
          {/* 标题和描述 */}
          <h3 className="text-white font-medium text-base mb-1 line-clamp-1">
            {pack.title}
          </h3>
          <p className="text-gray-300 text-sm mb-2 line-clamp-2">
            {pack.description}
          </p>
          
          {/* 图片数量和亲密度 */}
          <div className="flex items-center justify-between mb-2">
            <span className="text-xs text-gray-400">
              {pack.images.length} images
            </span>
            <Badge
              variant="outline"
              className={`text-xs ${getIntimacyColor(pack.intimacy)}`}
            >
              {pack.intimacy}
            </Badge>
          </div>
          
          {/* 价格和购买按钮 */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              {pack.originalPrice && pack.originalPrice > pack.price && (
                <span className="text-xs text-gray-400 line-through">
                  ${pack.originalPrice}
                </span>
              )}
              <span className="text-pink-400 font-bold">
                ${pack.price}
              </span>
            </div>
            
            {pack.isPurchased ? (
              <Button size="sm" className="bg-green-600 hover:bg-green-700 text-white">
                <CheckCircle className="h-4 w-4 mr-1" />
                View
              </Button>
            ) : (
              <Button
                size="sm"
                className="bg-pink-500 hover:bg-pink-600 text-white"
                onClick={(e) => {
                  e.stopPropagation();
                  onPurchase(pack.id);
                }}
                disabled={purchaseLoading}
              >
                {purchaseLoading ? (
                  'Buying...'
                ) : (
                  <>
                    <CreditCard className="h-4 w-4 mr-1" />
                    Buy
                  </>
                )}
              </Button>
            )}
          </div>
        </div>
        
        {/* 悬停预览 */}
        {isHovered && pack.images.length > 1 && (
          <div className="absolute inset-0 bg-black/90 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
            <div className="grid grid-cols-2 gap-1 p-4 max-h-full overflow-hidden">
              {pack.images.slice(0, 4).map((image, index) => (
                <div key={index} className="relative aspect-square w-16 h-16 rounded overflow-hidden">
                  <SmartImage
                    src={getGalleryImageUrl(image)}
                    alt={`Preview ${index + 1}`}
                    fill
                    className="object-cover"
                  />
                </div>
              ))}
              {pack.images.length > 4 && (
                <div className="relative aspect-square w-16 h-16 rounded overflow-hidden bg-black/50 flex items-center justify-center">
                  <span className="text-white text-xs">
                    +{pack.images.length - 4}
                  </span>
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}