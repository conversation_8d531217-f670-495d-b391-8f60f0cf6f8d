export interface QuickReplyConfig {
  text: string;
  type: 'text' | 'image' | 'voice';
  resourceType?: 'image' | 'voice_plays';
  resourceIndex?: number;
}

export const QUICK_REPLY_BUTTONS: QuickReplyConfig[] = [
  // Text buttons - 只设置输入框内容
  { text: "Tell me what you're thinking...", type: 'text' },
  { text: 'Do you miss me?', type: 'text' },
  { text: 'Describe your dream date with me', type: 'text' },
  { text: 'What are you wearing right now?', type: 'text' },
  
  // Picture buttons - 映射到resource.image
  { text: '👙 Sexy Wet', type: 'image', resourceType: 'image', resourceIndex: 0 },
  { text: '🧼 Bath Time', type: 'image', resourceType: 'image', resourceIndex: 1 },
  { text: '🛌 Lying in Bed', type: 'image', resourceType: 'image', resourceIndex: 2 },
  { text: '🧥 Favorite Outfit', type: 'image', resourceType: 'image', resourceIndex: 3 },
  
  // Voice buttons - 映射到resource.voice_plays
  { text: '💬 Casual Talk', type: 'voice', resourceType: 'voice_plays', resourceIndex: 0 },
  { text: '💞 Whispered Affections', type: 'voice', resourceType: 'voice_plays', resourceIndex: 1 },
  { text: '🔥 Naughty Chat', type: 'voice', resourceType: 'voice_plays', resourceIndex: 2 },
  { text: '👀 Sexiest Appearance', type: 'voice', resourceType: 'voice_plays', resourceIndex: 3 },
  { text: '🤫 Private Conversation', type: 'voice', resourceType: 'voice_plays', resourceIndex: 4 },
  { text: '💋 Physical Touch', type: 'voice', resourceType: 'voice_plays', resourceIndex: 5 },
];