'use client';

import { useState, useEffect } from 'react';
import { Dialog, DialogContent } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';

interface AgeVerificationModalProps {
  isOpen: boolean;
  onConfirm: () => void;
  onDeny: () => void;
}

export function AgeVerificationModal({ isOpen, onConfirm, onDeny }: AgeVerificationModalProps) {
  const [showWarning, setShowWarning] = useState(false);

  const handleDeny = () => {
    setShowWarning(true);
    setTimeout(() => {
      onDeny();
    }, 2000);
  };

  return (
    <Dialog open={isOpen} onOpenChange={() => {}}>
      <DialogContent 
        className="sm:max-w-[425px] bg-[#1a0825] border border-purple-500/30 text-white [&>button]:hidden"
      >
        <div className="space-y-6 p-6">
          {!showWarning ? (
            <>
              <div className="text-center space-y-4">
                <div className="text-6xl mb-4">🔞</div>
                <h2 className="text-2xl font-bold text-purple-300">
                  Age Verification Required
                </h2>
                <div className="space-y-3 text-sm text-gray-300">
                  <p>
                    This website contains adult content and is intended for users 18+ only.
                  </p>
                  <p>
                    By continuing, you confirm that:
                  </p>
                  <ul className="text-left space-y-1 text-xs">
                    <li>• You are at least 18 years old</li>
                    <li>• Adult content is legal in your jurisdiction</li>
                    <li>• You consent to viewing adult content</li>
                  </ul>
                </div>
              </div>
              
              <div className="flex flex-col sm:flex-row gap-3">
                <Button
                  onClick={onConfirm}
                  className="flex-1 bg-purple-600 hover:bg-purple-700 text-white font-semibold py-3"
                >
                  I am 18+ years old
                </Button>
                <Button
                  onClick={handleDeny}
                  variant="outline"
                  className="flex-1 border-gray-600 text-gray-300 hover:bg-gray-800 py-3"
                >
                  I am under 18
                </Button>
              </div>
              
              <div className="text-xs text-gray-500 text-center">
                This confirmation will be saved on your device for 30 days
              </div>
            </>
          ) : (
            <div className="text-center space-y-4">
              <div className="text-4xl mb-4">⚠️</div>
              <h3 className="text-xl font-bold text-red-400">
                Access Restricted
              </h3>
              <p className="text-gray-300">
                Sorry, this website is only available to users 18 years and older.
                You will be redirected shortly.
              </p>
              <div className="flex justify-center">
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-purple-500"></div>
              </div>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}

export default AgeVerificationModal;