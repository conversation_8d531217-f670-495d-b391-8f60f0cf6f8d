"use client";

import Image from "next/image";
import { Check } from "lucide-react";
import { cn } from "@/lib/utils";

interface OptionSelectorProps {
  title: string;
  options: Record<string, string>;
  selectedOption: string | null;
  onSelect: (option: string) => void;
  columns?: number;
  className?: string;
}

export default function OptionSelector({
  title,
  options,
  selectedOption,
  onSelect,
  columns = 3,
  className = "",
}: OptionSelectorProps) {
  const optionEntries = Object.entries(options);

  return (
    <div className={cn("space-y-4", className)}>
      <h3 className="text-lg font-semibold">{title}</h3>
      <div 
        className={cn(
          "grid gap-3",
          {
            "grid-cols-2": columns === 2,
            "grid-cols-3": columns === 3,
            "grid-cols-4": columns === 4,
            "grid-cols-5": columns === 5,
          }
        )}
      >
        {optionEntries.map(([key, thumbnail]) => (
          <div
            key={key}
            className={cn(
              "relative cursor-pointer rounded-lg border-2 p-2 transition-all hover:scale-105",
              selectedOption === key
                ? "border-pink-500 bg-pink-500/10"
                : "border-[#3a1a44] bg-[#1a0a24] hover:border-pink-400"
            )}
            onClick={() => onSelect(key)}
          >
            {/* 选中状态指示器 */}
            {selectedOption === key && (
              <div className="absolute -top-2 -right-2 z-10 h-6 w-6 rounded-full bg-pink-500 flex items-center justify-center">
                <Check className="h-4 w-4 text-white" />
              </div>
            )}
            
            {/* 缩略图 */}
            <div className="aspect-square mb-2 overflow-hidden rounded-md">
              <Image
                src={thumbnail}
                alt={key}
                width={80}
                height={80}
                className="h-full w-full object-cover"
              />
            </div>
            
            {/* 标签 */}
            <p className="text-center text-sm font-medium text-gray-200">
              {key}
            </p>
          </div>
        ))}
      </div>
    </div>
  );
}