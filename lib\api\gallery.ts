/**
 * 图包相关API接口
 * Gallery/Sneaky 页面的所有接口集成
 */

import { API_CONFIG } from '../config';
import { STORAGE_KEYS, COMMON_HEADERS } from './config';
import { mainApiClient } from './client';

const API_BASE_URL = API_CONFIG.BASE_URL;

// ====================
// 类型定义
// ====================

// API返回的原始图包数据
export interface ApiPackage {
  id: number;
  packageName: string;
  price: number;
  description: string;
  style: string;
  intimacy: string;
  tag: string;
  cover: string;
  unlocked: boolean;
}

// 转换后的图包数据（用于前端）
export interface GalleryPackage {
  id: number;
  title: string;
  description: string;
  coverImage: string;
  images: string[];
  price: number;
  originalPrice?: number;
  tags: string[];
  style: string;
  intimacy: string;
  isPurchased: boolean;
  isFavorited: boolean;
  createdAt: string;
  updatedAt: string;
}

// API返回的分页信息
export interface ApiPagination {
  totalCount: number;
  totalPages: number;
  currentPage: number;
  pageSize: number;
  hasNext: boolean;
  hasPrev: boolean;
}

// 图包列表请求参数
export interface PackageListParams {
  style?: string;
  intimacy?: string;
  tag?: string;
  keyword?: string;
  sortBy?: string;
  sortDirection?: 'asc' | 'desc';
  page?: number;
  pageSize?: number;
}

// API返回的图包列表响应
export interface ApiPackageListResponse {
  success: boolean;
  message: string;
  data: {
    packages: ApiPackage[];
    pagination: ApiPagination;
  };
}

// API返回的已购买图包数据
export interface ApiPurchasedPackage {
  id: number;
  name: string;
  description: string;
  images: number;
  purchasedTime: string;
  updateTime: string;
  coverImage: string;
}

// 已购买图包请求参数
export interface PurchasedPackagesParams {
  page?: number;
  pageSize?: number;
}

// API返回的已购买图包响应
export interface ApiPurchasedPackagesResponse {
  success: boolean;
  message: string;
  data: {
    purchasedPackages: ApiPurchasedPackage[];
    pagination: ApiPagination;
  };
}

// API返回的图包资源数据
export interface ApiPackageResource {
  resourceId: number;
  resourceName: string;
  resourceUrl: string;
}

// API返回的图包详情响应
export interface ApiPackageDetailResponse {
  success: boolean;
  message: string;
  data: {
    packageId: number;
    packageName: string;
    resourceData: ApiPackageResource[];
  };
}

// API返回的收藏资源数据
export interface ApiCollectedResource {
  resourceId: number;
  resourceName: string;
  resourceUrl: string;
  packageId: number;
  packageName: string;
  collectTime: string;
}

// 收藏资源列表请求参数
export interface FavoriteResourcesParams {
  page?: number;
  pageSize?: number;
}

// API返回的收藏资源列表响应
export interface ApiFavoriteResourcesResponse {
  success: boolean;
  message: string;
  data: {
    collectedResources: ApiCollectedResource[];
    pagination: ApiPagination;
  };
}

// 收藏操作请求参数
export interface FavoriteActionParams {
  resourceId: number;
  collect: boolean; // true: 收藏, false: 取消收藏
}

// API返回的收藏操作响应
export interface ApiFavoriteActionResponse {
  success: boolean;
  data: null;
  message: string;
}

// 购买图包请求参数
export interface PurchasePackageParams {
  packageId: number;
}

// API返回的购买图包响应（暂时未知具体格式）
export interface ApiPurchaseResponse {
  success: boolean;
  data: any; // TODO: 等待确认具体的响应数据格式
  message: string;
}

export interface PurchasedPackage {
  id: number;
  packageId: number;
  purchaseDate: string;
  package: GalleryPackage;
}

export interface FavoriteResource {
  id: number;
  resourceId: number;
  resourceType: 'package' | 'image';
  favoriteDate: string;
  resource: GalleryPackage | any;
}

export interface GalleryListResponse {
  success: boolean;
  data: {
    packages: GalleryPackage[];
    total: number;
    currentPage: number;
    totalPages: number;
  };
  message: string;
}

export interface PurchasedPackagesResponse {
  success: boolean;
  data: {
    packages: PurchasedPackage[];
    total: number;
  };
  message: string;
}

export interface PackageDetailResponse {
  success: boolean;
  data: {
    package: GalleryPackage;
  };
  message: string;
}

export interface FavoriteListResponse {
  success: boolean;
  data: {
    favorites: FavoriteResource[];
    total: number;
  };
  message: string;
}

export interface FavoriteActionResponse {
  success: boolean;
  data: {
    isFavorited: boolean;
  };
  message: string;
}

export interface PurchaseResponse {
  success: boolean;
  data: {
    orderId: string;
    purchaseDate: string;
    paymentUrl?: string; // 支付页面URL
    packageId: number;
    paymentWindow?: Window | null; // 支付窗口引用
  };
  message: string;
}

// ====================
// API接口函数
// ====================

/**
 * 1. 获取图包列表
 * @param params 查询参数
 */
export async function getGalleryPackages(
  params: PackageListParams = {}
): Promise<GalleryListResponse> {
  const {
    style = 'All',
    intimacy = 'All',
    tag = 'All',
    keyword = '',
    sortBy = 'price',
    sortDirection = 'desc',
    page = 1,
    pageSize = 10,
  } = params;

  const requestBody = {
    style,
    intimacy,
    tag,
    keyword,
    sortBy,
    sortDirection,
    page,
    pageSize,
  };

  try {
    const result: ApiPackageListResponse = await mainApiClient.post(
      '/package',
      requestBody
    );

    if (!result.success) {
      throw new Error(result.message || 'Failed to fetch packages');
    }

    // 转换API数据为前端数据格式
    const packages: GalleryPackage[] =
      result.data.packages.map(transformApiPackage);

    return {
      success: true,
      message: result.message,
      data: {
        packages,
        total: result.data.pagination.totalCount,
        currentPage: result.data.pagination.currentPage,
        totalPages: result.data.pagination.totalPages,
      },
    };
  } catch (error) {
    console.error('获取图包列表失败:', error);
    throw error;
  }
}

/**
 * 2. 查询已购买的图包
 * @param params 查询参数
 */
export async function getPurchasedPackages(
  params: PurchasedPackagesParams = {}
): Promise<PurchasedPackagesResponse> {
  const { page = 1, pageSize = 10 } = params;

  const requestBody = {
    page,
    pageSize,
  };

  try {
    const result: ApiPurchasedPackagesResponse = await mainApiClient.post(
      '/package/purchased',
      requestBody
    );

    if (!result.success) {
      throw new Error(result.message || 'Failed to fetch purchased packages');
    }

    // 转换API数据为前端数据格式
    const packages: PurchasedPackage[] = result.data.purchasedPackages.map(
      transformApiPurchasedPackage
    );

    return {
      success: true,
      message: result.message,
      data: {
        packages,
        total: result.data.pagination.totalCount,
      },
    };
  } catch (error) {
    console.error('获取已购买图包失败:', error);
    throw error;
  }
}

/**
 * 3. 查询图包详情
 * @param packageId 图包ID
 */
export async function getPackageDetail(
  packageId: number
): Promise<PackageDetailResponse> {
  try {
    const result: ApiPackageDetailResponse = await mainApiClient.post(
      `/package/${packageId}`,
      {}
    );

    if (!result.success) {
      throw new Error(result.message || 'Failed to fetch package detail');
    }

    // 转换API数据为前端数据格式
    const packageDetail = transformApiPackageDetail(result.data);

    return {
      success: true,
      message: result.message,
      data: {
        package: packageDetail,
      },
    };
  } catch (error) {
    console.error('获取图包详情失败:', error);
    throw error;
  }
}

/**
 * 4. 查询收藏资源列表
 * @param params 查询参数
 */
export async function getFavoriteResources(
  params: FavoriteResourcesParams = {}
): Promise<FavoriteListResponse> {
  const { page = 1, pageSize = 10 } = params;

  const requestBody = {
    page,
    pageSize,
  };

  try {
    const result: ApiFavoriteResourcesResponse = await mainApiClient.post(
      '/package/resource',
      requestBody
    );

    if (!result.success) {
      throw new Error(result.message || 'Failed to fetch favorite resources');
    }

    // 转换API数据为前端数据格式
    const favorites: FavoriteResource[] = result.data.collectedResources.map(
      transformApiCollectedResource
    );

    return {
      success: true,
      message: result.message,
      data: {
        favorites,
        total: result.data.pagination.totalCount,
      },
    };
  } catch (error) {
    console.error('获取收藏资源列表失败:', error);
    throw error;
  }
}

/**
 * 5. 收藏/取消收藏资源
 * @param resourceId 资源ID
 * @param collect 是否收藏：true收藏，false取消收藏
 */
export async function toggleFavoriteResource(
  resourceId: number,
  collect: boolean
): Promise<FavoriteActionResponse> {
  const requestBody: FavoriteActionParams = {
    resourceId,
    collect,
  };

  try {
    const result: ApiFavoriteActionResponse = await mainApiClient.post(
      '/package/resource/collect',
      requestBody
    );

    // 注意：API在某些情况下success为false但不是错误（如重复收藏）
    // 这里我们根据message判断是否为真正的错误
    if (!result.success && !result.message.includes('No need to')) {
      throw new Error(result.message || 'Failed to toggle favorite');
    }

    return {
      success: true,
      message: result.message,
      data: {
        isFavorited: collect, // 返回预期的收藏状态
      },
    };
  } catch (error) {
    console.error('收藏操作失败:', error);
    throw error;
  }
}

/**
 * 6. 购买图包 - 拦截重定向并在新窗口打开支付页面
 * @param packageId 图包ID
 */
export async function purchasePackage(
  packageId: number
): Promise<PurchaseResponse> {
  const requestBody: PurchasePackageParams = {
    packageId,
  };

  // 获取token
  const token = mainApiClient.getAuthToken();
  if (!token) {
    throw new Error('用户未登录');
  }

  try {
    console.log('🔄 开始购买图包:', requestBody);
    
    // 使用Next.js API代理来避免CORS问题
    const response = await fetch('/api/purchase-proxy', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`,
      },
      body: JSON.stringify(requestBody),
    });

    console.log('📊 代理API响应状态:', response.status);

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || `HTTP ${response.status}`);
    }

    const result = await response.json();
    console.log('📄 代理API响应数据:', result);

    // 检查是否需要重定向到支付页面
    if (result.redirect && result.paymentUrl) {
      console.log('🔄 检测到重定向，支付URL:', result.paymentUrl);
      
      // 在新窗口中打开支付页面
      const paymentWindow = window.open(
        result.paymentUrl,
        '_blank',
        'width=800,height=600,scrollbars=yes,resizable=yes'
      );

      if (!paymentWindow) {
        throw new Error('无法打开支付页面，请检查浏览器弹窗设置');
      }

      console.log('✅ 支付窗口已打开，等待用户完成支付');

      return {
        success: true,
        message: '正在跳转到支付页面...',
        data: {
          orderId: `temp-order-${packageId}-${Date.now()}`,
          purchaseDate: new Date().toISOString(),
          paymentUrl: result.paymentUrl,
          packageId: packageId,
          paymentWindow: paymentWindow,
        },
      };
    } else {
      // 直接购买成功的情况
      return {
        success: true,
        message: result.message || '购买成功',
        data: {
          orderId: result.orderId || `order-${packageId}-${Date.now()}`,
          purchaseDate: new Date().toISOString(),
          paymentUrl: '',
          packageId: packageId,
          paymentWindow: null,
        },
      };
    }
  } catch (error) {
    console.error('购买图包失败:', error);
    throw error;
  }
}

// ====================
// 辅助函数
// ====================

/**
 * 转换API图包数据为前端格式
 * @param apiPackage API返回的图包数据
 */
function transformApiPackage(apiPackage: ApiPackage): GalleryPackage {
  // 处理标签 - 将单个tag字符串转换为数组
  const tags: string[] = [];
  if (apiPackage.tag && apiPackage.tag.trim()) {
    // 支持多个标签（逗号分隔）或单个标签
    const tagArray = apiPackage.tag
      .split(',')
      .map((t) => t.trim())
      .filter((t) => t);
    tags.push(...tagArray);
  }

  // 清理样式和亲密度字段（去除可能的换行符）
  const style = apiPackage.style?.replace(/\n/g, '').trim() || '';
  const intimacy = apiPackage.intimacy?.replace(/\n/g, '').trim() || '';

  return {
    id: apiPackage.id,
    title: apiPackage.packageName,
    description: apiPackage.description,
    coverImage: apiPackage.cover,
    images: [apiPackage.cover], // API暂时只返回封面，详情接口会有完整图片列表
    price: apiPackage.price,
    tags,
    style,
    intimacy,
    isPurchased: apiPackage.unlocked,
    isFavorited: false, // 需要从收藏接口获取
    createdAt: new Date().toISOString(), // API暂未返回创建时间
    updatedAt: new Date().toISOString(), // API暂未返回更新时间
  };
}

/**
 * 转换API已购买图包数据为前端格式
 * @param apiPurchasedPackage API返回的已购买图包数据
 */
function transformApiPurchasedPackage(
  apiPurchasedPackage: ApiPurchasedPackage
): PurchasedPackage {
  // 创建对应的GalleryPackage对象
  const galleryPackage: GalleryPackage = {
    id: apiPurchasedPackage.id,
    title: apiPurchasedPackage.name,
    description: apiPurchasedPackage.description,
    coverImage: apiPurchasedPackage.coverImage,
    images: Array(apiPurchasedPackage.images).fill(
      apiPurchasedPackage.coverImage
    ), // 用封面图填充，详情接口会有完整列表
    price: 0, // 已购买的图包不显示价格
    tags: [], // 已购买列表暂不返回标签信息
    style: '', // 已购买列表暂不返回样式信息
    intimacy: '', // 已购买列表暂不返回亲密度信息
    isPurchased: true, // 已购买
    isFavorited: false, // 需要从收藏接口获取
    createdAt: apiPurchasedPackage.updateTime,
    updatedAt: apiPurchasedPackage.updateTime,
  };

  return {
    id: apiPurchasedPackage.id,
    packageId: apiPurchasedPackage.id,
    purchaseDate: apiPurchasedPackage.purchasedTime,
    package: galleryPackage,
  };
}

/**
 * 转换API图包详情数据为前端格式
 * @param apiPackageDetail API返回的图包详情数据
 */
function transformApiPackageDetail(apiPackageDetail: {
  packageId: number;
  packageName: string;
  resourceData: ApiPackageResource[];
}): GalleryPackage {
  // 提取所有资源URL作为图片列表
  const images = apiPackageDetail.resourceData.map(
    (resource) => resource.resourceUrl
  );
  const coverImage = images[0] || ''; // 使用第一张图作为封面

  return {
    id: apiPackageDetail.packageId,
    title: apiPackageDetail.packageName,
    description: '', // 详情接口暂不返回描述信息
    coverImage,
    images,
    price: 0, // 详情接口暂不返回价格信息
    tags: [], // 详情接口暂不返回标签信息
    style: '', // 详情接口暂不返回样式信息
    intimacy: '', // 详情接口暂不返回亲密度信息
    isPurchased: true, // 能查看详情说明已购买
    isFavorited: false, // 需要从收藏接口获取
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  };
}

/**
 * 转换API收藏资源数据为前端格式
 * @param apiCollectedResource API返回的收藏资源数据
 */
function transformApiCollectedResource(
  apiCollectedResource: ApiCollectedResource
): FavoriteResource {
  // 创建资源对象 - 这里我们将单个图片资源作为一个简化的包对象
  const resourcePackage: GalleryPackage = {
    id: apiCollectedResource.packageId,
    title: apiCollectedResource.packageName,
    description: '', // 收藏列表暂不返回描述
    coverImage: apiCollectedResource.resourceUrl,
    images: [apiCollectedResource.resourceUrl], // 单张图片
    price: 0, // 收藏列表不显示价格
    tags: [], // 收藏列表暂不返回标签
    style: '', // 收藏列表暂不返回样式
    intimacy: '', // 收藏列表暂不返回亲密度
    isPurchased: true, // 能收藏说明已购买
    isFavorited: true, // 在收藏列表中，肯定是已收藏的
    createdAt: apiCollectedResource.collectTime,
    updatedAt: apiCollectedResource.collectTime,
  };

  return {
    id: apiCollectedResource.resourceId,
    resourceId: apiCollectedResource.resourceId,
    resourceType: 'image', // API返回的是单个图片资源
    favoriteDate: apiCollectedResource.collectTime,
    resource: resourcePackage,
  };
}

// 不再需要这些函数，因为我们使用 mainApiClient
