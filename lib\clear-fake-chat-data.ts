// 清理虚假的聊天数据
export const clearFakeChatData = () => {
  if (typeof window !== 'undefined') {
    // 清除localStorage中的虚假聊天数据
    localStorage.removeItem('recentChats');
    
    // 可以在这里添加其他需要清理的数据
    console.log('✅ Fake chat data cleared from localStorage');
  }
};

// 检查并清理已知的虚假角色数据
export const checkAndClearFakeData = () => {
  if (typeof window !== 'undefined') {
    const recentChats = localStorage.getItem('recentChats');
    if (recentChats) {
      try {
        const chats = JSON.parse(recentChats);
        const fakeCharacterNames = ['Innocent anime', 'Ethan', 'anime girl'];
        
        // 检查是否包含虚假角色
        const hasFakeData = chats.some((chat: any) => 
          fakeCharacterNames.some(fakeName => 
            chat.name?.toLowerCase().includes(fakeName.toLowerCase())
          )
        );
        
        if (hasFakeData) {
          console.log('🚨 Detected fake character data, clearing...');
          localStorage.removeItem('recentChats');
          console.log('✅ Fake character data cleared');
          return true;
        }
      } catch (error) {
        console.error('Error checking fake data:', error);
        // 如果解析失败，也清除数据
        localStorage.removeItem('recentChats');
        return true;
      }
    }
  }
  return false;
};

export default {
  clearFakeChatData,
  checkAndClearFakeData
};