'use client';

import { useState, useRef, useCallback, useEffect } from 'react';
import { PhoneCall } from 'lucide-react';
import { useDeviceType } from '@/hooks/useDeviceType';

interface DraggableCallButtonProps {
  callTime: number;
  onReturnToCall: () => void;
}

export default function DraggableCallButton({ callTime, onReturnToCall }: DraggableCallButtonProps) {
  const { isMobile } = useDeviceType();
  const [position, setPosition] = useState({ x: 0, y: 0 });
  const [isDragging, setIsDragging] = useState(false);
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 });
  const [initialPosition, setInitialPosition] = useState({ x: 0, y: 0 });
  const [isLongPressing, setIsLongPressing] = useState(false);
  const buttonRef = useRef<HTMLDivElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const longPressTimerRef = useRef<NodeJS.Timeout | null>(null);
  const hasDraggedRef = useRef(false);

  // 格式化通话时间
  const formatCallTime = useCallback((seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  }, []);

  // 初始化位置（右侧中间）
  useEffect(() => {
    const updateInitialPosition = () => {
      if (typeof window !== 'undefined') {
        const initialX = window.innerWidth - 80; // 距离右边80px
        const initialY = window.innerHeight / 2 - 32; // 垂直居中
        setPosition({ x: initialX, y: initialY });
        setInitialPosition({ x: initialX, y: initialY });
      }
    };

    updateInitialPosition();
    window.addEventListener('resize', updateInitialPosition);
    return () => window.removeEventListener('resize', updateInitialPosition);
  }, []);

  // 限制位置在屏幕范围内
  const constrainPosition = useCallback((x: number, y: number) => {
    const buttonSize = 64; // 按钮大小
    const padding = 16; // 边距
    
    const maxX = window.innerWidth - buttonSize - padding;
    const maxY = window.innerHeight - buttonSize - padding;
    
    return {
      x: Math.max(padding, Math.min(maxX, x)),
      y: Math.max(padding, Math.min(maxY, y))
    };
  }, []);

  // 开始长按检测
  const handleStart = useCallback((clientX: number, clientY: number) => {
    hasDraggedRef.current = false;
    setDragStart({ x: clientX - position.x, y: clientY - position.y });

    // 开始长按计时器
    longPressTimerRef.current = setTimeout(() => {
      setIsLongPressing(true);
      setIsDragging(true);
    }, 500); // 500ms 长按触发
  }, [position]);

  // 鼠标/触摸移动
  const handleMove = useCallback((clientX: number, clientY: number) => {
    // 如果还没有开始拖拽，检查是否移动了足够距离来取消长按
    if (!isDragging) {
      const moveDistance = Math.sqrt(
        Math.pow(clientX - (position.x + dragStart.x), 2) +
        Math.pow(clientY - (position.y + dragStart.y), 2)
      );

      // 如果移动距离超过10px，取消长按
      if (moveDistance > 10) {
        if (longPressTimerRef.current) {
          clearTimeout(longPressTimerRef.current);
          longPressTimerRef.current = null;
        }
      }
      return;
    }

    hasDraggedRef.current = true;
    const newX = clientX - dragStart.x;
    const newY = clientY - dragStart.y;
    const constrainedPosition = constrainPosition(newX, newY);

    setPosition(constrainedPosition);
  }, [isDragging, dragStart, constrainPosition, position]);

  // 鼠标/触摸结束
  const handleEnd = useCallback(() => {
    // 清除长按计时器
    if (longPressTimerRef.current) {
      clearTimeout(longPressTimerRef.current);
      longPressTimerRef.current = null;
    }

    // 如果是在拖拽状态
    if (isDragging) {
      setIsDragging(false);
      setIsLongPressing(false);

      // 自动吸附到屏幕边缘
      const screenWidth = window.innerWidth;
      const buttonSize = 64;
      const padding = 16;

      let finalX = position.x;

      // 如果在屏幕左半部分，吸附到左边；右半部分吸附到右边
      if (position.x < screenWidth / 2) {
        finalX = padding;
      } else {
        finalX = screenWidth - buttonSize - padding;
      }

      const constrainedPosition = constrainPosition(finalX, position.y);
      setPosition(constrainedPosition);
    } else {
      // 如果没有拖拽，这是一个点击事件
      if (!hasDraggedRef.current && !isLongPressing) {
        onReturnToCall();
      }
      setIsLongPressing(false);
    }
  }, [isDragging, position, constrainPosition, isLongPressing, onReturnToCall]);

  // 鼠标事件
  const handleMouseDown = (e: React.MouseEvent) => {
    e.preventDefault();
    handleStart(e.clientX, e.clientY);
  };

  const handleMouseMove = useCallback((e: MouseEvent) => {
    handleMove(e.clientX, e.clientY);
  }, [handleMove]);

  const handleMouseUp = useCallback(() => {
    handleEnd();
  }, [handleEnd]);

  // 触摸事件
  const handleTouchStart = (e: React.TouchEvent) => {
    e.preventDefault();
    const touch = e.touches[0];
    handleStart(touch.clientX, touch.clientY);
  };

  const handleTouchMove = useCallback((e: TouchEvent) => {
    e.preventDefault();
    const touch = e.touches[0];
    handleMove(touch.clientX, touch.clientY);
  }, [handleMove]);

  const handleTouchEnd = useCallback((e: TouchEvent) => {
    e.preventDefault();
    handleEnd();
  }, [handleEnd]);

  // 添加全局事件监听器
  useEffect(() => {
    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      document.addEventListener('touchmove', handleTouchMove, { passive: false });
      document.addEventListener('touchend', handleTouchEnd, { passive: false });
      
      return () => {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
        document.removeEventListener('touchmove', handleTouchMove);
        document.removeEventListener('touchend', handleTouchEnd);
      };
    }
  }, [isDragging, handleMouseMove, handleMouseUp, handleTouchMove, handleTouchEnd]);

  // 清理定时器
  useEffect(() => {
    return () => {
      if (longPressTimerRef.current) {
        clearTimeout(longPressTimerRef.current);
      }
    };
  }, []);

  // PC端渲染：固定位置的通话状态栏
  if (!isMobile) {
    return (
      <div className="fixed top-4 right-4 z-30 pointer-events-auto">
        <div
          className="bg-black/60 backdrop-blur-md rounded-xl border border-white/20 p-3 shadow-lg hover:shadow-xl transition-all duration-300 cursor-pointer hover:bg-black/70"
          onClick={onReturnToCall}
        >
          <div className="flex items-center space-x-3">
            {/* 通话图标 */}
            <div className="relative">
              <PhoneCall className="w-5 h-5 text-white" />
              {/* 通话状态指示器 */}
              <div className="absolute -top-1 -right-1 w-3 h-3 bg-gradient-to-r from-pink-500 to-red-500 rounded-full flex items-center justify-center">
                <div className="w-1.5 h-1.5 bg-white rounded-full animate-pulse"></div>
              </div>
            </div>

            {/* 通话信息 */}
            <div className="text-white">
              <div className="text-sm font-medium">Call in progress</div>
              <div className="text-xs text-gray-300 font-mono">{formatCallTime(callTime)}</div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // 移动端渲染：可拖拽悬浮按钮
  return (
    <div
      ref={containerRef}
      className="fixed z-30 pointer-events-none"
      style={{
        left: 0,
        top: 0,
        width: '100vw',
        height: '100vh',
      }}
    >
      <div
        ref={buttonRef}
        className={`absolute pointer-events-auto select-none transition-all duration-300 ${
          isDragging ? 'scale-110 shadow-2xl' : isLongPressing ? 'scale-105 shadow-xl' : 'hover:scale-105 shadow-lg'
        }`}
        style={{
          left: `${position.x}px`,
          top: `${position.y}px`,
          transform: isDragging ? 'none' : undefined,
        }}
        onMouseDown={handleMouseDown}
        onTouchStart={handleTouchStart}
      >
        <div className={`group relative w-14 h-14 sm:w-16 sm:h-16 bg-black/40 hover:bg-black/60 backdrop-blur-md rounded-xl border border-white/10 hover:border-white/20 transition-all duration-300 ${
          isDragging ? 'cursor-grabbing' : isLongPressing ? 'cursor-grab' : 'cursor-pointer'
        }`}>
          {/* 通话中图标 */}
          <div className="absolute inset-0 flex items-center justify-center">
            <PhoneCall className="w-6 h-6 sm:w-7 sm:h-7 text-white group-hover:text-pink-300 transition-colors" />
          </div>
          
          {/* 通话状态指示器 */}
          <div className="absolute -top-1 -right-1 w-4 h-4 bg-gradient-to-r from-pink-500 to-red-500 rounded-full flex items-center justify-center">
            <div className="w-2 h-2 bg-white rounded-full animate-pulse"></div>
          </div>
          
          {/* 通话时长显示 */}
          <div className="absolute -bottom-6 left-1/2 transform -translate-x-1/2 bg-black/60 backdrop-blur-sm px-2 py-0.5 rounded text-xs text-white font-mono whitespace-nowrap">
            {formatCallTime(callTime)}
          </div>
          
          {/* 悬停提示 */}
          <div className="absolute right-full mr-3 top-1/2 transform -translate-y-1/2 bg-black/80 backdrop-blur-sm text-white text-xs px-2 py-1 rounded whitespace-nowrap opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none">
            {isDragging ? 'Dragging...' : isLongPressing ? 'Long press to drag' : 'Tap to return • Long press to move'}
          </div>
        </div>
      </div>
    </div>
  );
}
