'use client';

import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { Home, MessageSquare, CreditCard, User } from 'lucide-react';

const bottomNavItems = [
  { 
    icon: Home, 
    label: 'Explore', 
    href: '/',
    activePatterns: ['/']
  },
  { 
    icon: MessageSquare, 
    label: 'Chat', 
    href: '/chat',
    activePatterns: ['/chat']
  },
  { 
    icon: CreditCard, 
    label: 'Premium', 
    href: '/payment',
    activePatterns: ['/payment']
  },
  { 
    icon: User, 
    label: 'Profile', 
    href: '/profile',
    activePatterns: ['/profile']
  },
];

export default function MobileBottomNav() {
  const pathname = usePathname();

  const isActive = (patterns: string[]) => {
    return patterns.some(pattern => {
      if (pattern === '/') {
        return pathname === '/';
      }
      return pathname.startsWith(pattern);
    });
  };

  return (
    <div className="md:hidden fixed bottom-0 left-0 right-0 z-50 bg-[#120518] border-t border-[#3a1a44] backdrop-blur-lg">
      <div className="grid grid-cols-4 h-16">
        {bottomNavItems.map((item, index) => {
          const Icon = item.icon;
          const active = isActive(item.activePatterns);
          
          return (
            <Link
              key={index}
              href={item.href}
              className={`flex flex-col items-center justify-center min-h-[64px] px-1 transition-colors ${
                active 
                  ? 'text-pink-400' 
                  : 'text-gray-400 hover:text-gray-300'
              }`}
            >
              <div className={`flex items-center justify-center w-6 h-6 mb-1 ${
                active ? 'text-pink-400' : 'text-gray-400'
              }`}>
                <Icon className="h-5 w-5" />
              </div>
              <span className={`text-xs font-medium ${
                active ? 'text-pink-400' : 'text-gray-400'
              }`}>
                {item.label}
              </span>
              {active && (
                <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-8 h-1 bg-pink-400 rounded-t-full" />
              )}
            </Link>
          );
        })}
      </div>
    </div>
  );
}